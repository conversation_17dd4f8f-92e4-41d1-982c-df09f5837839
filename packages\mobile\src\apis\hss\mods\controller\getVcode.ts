/**
 * @description 4 位图片验证码
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openid */
  openid: string;
}

export type Result = defs.hss.GeneralRespBean<string>;
export const path = '/yc-hs/api/getVcode/{openid}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
