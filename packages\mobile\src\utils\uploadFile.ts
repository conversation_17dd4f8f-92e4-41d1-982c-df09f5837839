import Taro from '@tarojs/taro'
import * as imageConversion from 'image-conversion'
import { BaseUrl } from '../../config/env'
import { getGlobalData } from './global-data'

const uploadFile = (_data: any, formData, updateFun: () => void) => {
  const PlatformInfo = sessionStorage.getItem('PlatformInfo')
  const globalToken = PlatformInfo ? JSON.parse(PlatformInfo).globalToken : undefined
  const otehrHeader = { wxGlobalToken: globalToken ? globalToken : undefined }
  const { openId, accountId } = getGlobalData<'account'>('account')
  Taro.showLoading({ title: '上传中...' })
  try {
    Taro.uploadFile({
      url: `${BaseUrl}${_data.url}`,
      name: '',
      filePath: '',
      header: otehrHeader,
      formData: { file: formData, openId, accountId, ..._data },
      success(res) {
        const data = JSON.parse(res.data)
        setTimeout(() => {
          console.log('上传')
          Taro.hideLoading()
          if (data.code == '500') {
            Taro.showToast({ title: data.message, icon: 'none' })
            return
          }
          Taro.showToast({ title: '上传成功' })
          updateFun()
        }, 0)
      },
      fail(res) {
        Taro.hideLoading()
        console.log(res.errMsg)
      }
    })
  } catch (error) {
    Taro.hideLoading()
  }
}

export const chooseImage = (_data: any, updateFun: () => void) => {
  try {
    Taro.chooseImage({
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        const file: any = res.tempFiles[0].originalFileObj
        // 比例 scale
        imageConversion.compressAccurately(file, { size: 300, width: 750 }).then(data => {
          uploadFile(_data, data, updateFun)
        })
      }
    })
  } catch (error) {
    console.log('chooseImage', error)
  }
}
