/*
 * @Author: your name
 * @Date: 2021-09-07 16:37:00
 * @LastEditTime: 2021-11-24 15:47:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\declare\supportElder\index.tsx
 */
/**
 * 税前抵扣申请-赡养老人
 */
import isEmpty from 'lodash/isEmpty'
import { useEffect, useState } from 'react'
import { View, Text, Button } from '@tarojs/components'
import { withPage, BottomBtn, Form, FormItemProps, useForm } from '@components'
import { render, useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils/transforms'
import { navigateTo, getGlobalData, encryCertificateNumber } from '@utils'
import { users } from '@apis/users'
import styles from './index.module.scss'

const ratio = [
  { key: 1, value: '是' },
  { key: 0, value: '否' }
]
const relativesType = [
  { key: 1, value: '父母' },
  { key: 0, value: '其他' }
]
const shareType = [
  { key: 0, value: '赡养人平均分摊' },
  { key: 1, value: '赡养人约定分摊' },
  { key: 2, value: '被赡养人指定分摊' }
]
const cardType = [
  { key: '1', value: '居民身份证' },
  { key: '2', value: '中国护照' },
  { key: '3', value: '港澳居民来往内地通行证' },
  { key: '4', value: '港澳居民居住证' },
  { key: '5', value: '台湾居民来往大陆通行证' },
  { key: '6', value: '台湾居民居住证' },
  { key: '7', value: '外国护照' },
  { key: '8', value: '外国人永久居留身份证' },
  { key: '9', value: '外国人工作许可证（A类）' },
  { key: '10', value: '外国人工作许可证（B类）' },
  { key: '11', value: '外国人工作许可证（C类）' },
  { key: '12', value: '其他个人证件' }
]
const Index = () => {
  const { readOnly = '' } = useRouter().params
  const employeeId = getGlobalData<'account'>('account').empId
  const [flag, setFlag] = useState<boolean>(false)
  const [onlyLook, setOnlyLook] = useState<boolean>(false)
  const [price, setPrice] = useState<number>()
  const [supportInfo, setSupportInfo] = useState<Array<defs.users.MoreSupportInfo> | undefined>()
  const { type = 'create' } = useRouter<{ type: 'create' | 'update' }>().params
  const form = useForm()
  const { result } = users.baseData.getBaseData.useRequest({ pageNum: '1', typeId: '911' })
  const openBankOptions = result?.data || ([] as defs.users.BaseData[])
  const supportCardType = form.watch('supportCardType')

  const isOnlyChild = form.watch('isOnlyChild')
  const onSubmit = (values: any) => {
    // console.log('values----', values)
    if (isOnlyChild == 1) {
      form.setValue('supportId', '')
    } else {
    }
    users.user.saveSupportInfo.request(values).then(res => {
      // console.log(res)
      if (res?.code == 0) {
        // console.log('保存成功')
        Taro.navigateTo({
          url: '/pages/pre-tax/declare/submitSuccessfully/index'
        })
      }
    })
  }
  useEffect(() => {
    users.user.getSupportInfo
      .request({
        employeeId: employeeId
      })
      .then(res => {
        console.log(res, '赡养人信息')

        users.user.getCountryInfo
          .request({
            countryName: ''
          })
          .then(countryRes => {
            // console.log(countryRes, 'countryRes')
            !isEmpty(countryRes?.data) &&
              countryRes?.data.map((item, index) => {
                if (item.countryId == res?.data?.supportCountryId) {
                  form.setValue('countryName', item.countryName)
                }
              })
          })

        !isEmpty(res.data?.tSupportInfoList) && setSupportInfo(res.data?.tSupportInfoList)
        form.reset({ ...res?.data, isOnlyChild: res.data?.isOnlyChild ? 1 : 0 })
        if (readOnly) {
          form.setValue('supportCardNum', encryCertificateNumber(res.data?.supportCardNum))
        }
      })
  }, [])
  useEffect(() => {
    if (isOnlyChild == 1) {
      setFlag(false)
      form.setValue('monthDeduction', 2000)
      form.setValue('supportId', '')
      form.setValue('employeeId', employeeId)
      form.setValue('deductionMonth', '')
    } else if (isOnlyChild == 0) {
      setFlag(true)
      form.setValue('monthDeduction', 1000)
      form.setValue('employeeId', employeeId)
      form.setValue('deductionMonth', '')
    }
  }, [isOnlyChild])
  useEffect(() => {
    form.setValue('employeeId', employeeId)
    form.setValue('deductionMonth', '')
  }, [employeeId])

  useEffect(() => {
    // console.log(readOnly, 'readOnly')
    if (readOnly !== '') {
      setOnlyLook(true)
    }
  }, [readOnly])

  const scrollStyle = getScrollStyle({ bottom: 120 })
  const columns: FormItemProps[] = [
    {
      name: 'isOnlyChild',
      disabled: onlyLook,
      type: 'single',
      title: '是否独生子女',
      options: ratio,
      rules: { required: true }
    },
    {
      name: 'shareType',
      disabled: onlyLook,
      isHidden: !flag,
      type: 'single',
      level: 2,
      title: '分摊方式',
      options: shareType,
      rules: { required: true }
    },
    {
      name: 'monthDeduction',
      disabled: onlyLook,
      type: 'text',
      title: '本年度月扣除金额',
      rules: { required: true, maxLength: 10 }
    },
    {
      name: 'supportName',
      disabled: onlyLook,
      type: 'text',
      title: '姓名',
      rules: { required: true, maxLength: 10 }
    },
    {
      name: 'supportRelation',
      disabled: onlyLook,
      type: 'single',
      title: '与本人关系',
      options: relativesType,
      rules: { required: true }
    },
    {
      name: 'supportCountryId',
      disabled: onlyLook,
      type: 'page_choice',
      title: '国籍',
      rules: { required: true },
      pageOptions: {
        keys: ['supportCountryId', 'countryName'],
        labelKey: 'countryName',
        url: '/country'
      }
    },
    {
      name: 'supportCardType',
      disabled: onlyLook,
      type: 'select',
      title: '身份证件类型',
      options: cardType,
      rules: { required: true }
    },
    {
      name: 'supportCardNum',
      disabled: onlyLook,
      type: 'id_card',
      title: '身份证件号码',
      rules: {
        required: true,
        pattern: supportCardType == 1 ? {
          value: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/,
          message: '请输入正确的身份证格式'
        } : {
          value: /([^\.\d]|^)(\d+)([^\.\d]|$)/,
          message: ''
        }
      }
    },
    {
      name: 'supportBirthDate',
      disabled: onlyLook,
      type: 'date',
      title: '出生日期',
      rules: { required: true }
    },
    {
      isHidden: !flag,
      render: () => (
        <View className={styles.addSupport}>
          <Text>共同赡养人信息</Text>
          <View className={styles.addbtn} onClick={toAdd}>
            <Text className={styles.btn} onClick={toAdd}>
              添加共同赡养人
            </Text>
          </View>
        </View>
      )
    },
    {
      isHidden: !flag,
      render: () => (
        <View>
          {supportInfo
            ? supportInfo.map(item => (
                <View className={styles.addSupport} key={item.tSupportId}>
                  <Text>共同赡养人: {item.tSupportName}</Text>
                  <View className={styles.addSupportbtn}>
                    <View className={styles.addbtn} onClick={() => toDelete(item.tSupportId)}>
                      <Text className={styles.btn}>删除</Text>
                    </View>
                    <View className={styles.addbtn} onClick={() => toEdit(item.tSupportId)}>
                      <Text className={styles.btn}>编辑</Text>
                    </View>
                  </View>
                </View>
              ))
            : null}
        </View>
      )
    }
  ]

  const toAdd = () => {
    Taro.navigateTo({ url: '/pages/pre-tax/declare/commonElder/index' })
  }
  const toDelete = tSupportId => {
    Taro.showModal({
      title: '删除',
      cancelText: '取消',
      cancelColor: 'xx',
      confirmText: '确认',
      confirmColor: 'xx',
      content: '一旦删除将不再继续抵扣，是否确认删除?',
      showCancel: true,
      success(results) {
        if (results.confirm) {
          // console.log('删除')
          users.user.deleteTSupportInfo
            .request({
              tSupportId: tSupportId
            })
            .then(res => {
              console.log(res)
              if (res?.code == 0) {
                users.user.getSupportInfo
                  .request({
                    employeeId: employeeId
                  })
                  .then(ress => {
                    // console.log(ress, '赡养人信息')
                    setSupportInfo(ress.data?.tSupportInfoList)
                    Taro.showToast({
                      title: '删除成功',
                      icon: 'success',
                      duration: 2000
                    })
                  })
              }
            })
        } else if (results.cancel) {
          // console.log('不删除')
        }
      }
    })
    // users.user.deleteTSupportInfo
    //   .request({
    //     tSupportId: tSupportId
    //   })
    //   .then(res => {
    //     console.log(res)
    //     if (res?.code == 0) {
    //       users.user.getSupportInfo
    //         .request({
    //           employeeId: employeeId
    //         })
    //         .then(ress => {
    //           console.log(ress, '赡养人信息')
    //           !isEmpty(ress.data?.tSupportInfoList) && setSupportInfo(ress.data?.tSupportInfoList)
    //         })
    //     }
    //   })
  }
  const toEdit = tSupportId => {
    // console.log('修改')
    Taro.navigateTo({ url: '/pages/pre-tax/declare/commonElder/index?tSupportId=' + tSupportId })
  }

  // const addSupport = () => {
  //   return (
  //     <View className={styles.addSupport}>
  //       <Text>共同赡养人信息</Text>
  //       <View className={styles.addbtn} onClick={toAdd}>
  //         添加共同赡养人
  //       </View>
  //     </View>
  //   )
  // }

  return (
    <View className={styles.wrap}>
      <Form style={scrollStyle} form={form} columns={columns} />
      {/* {flag ? addSupport() : null} */}
      <BottomBtn
        btns={[
          onlyLook
            ? {
                title: '返回',
                onClick: () => {
                  Taro.navigateBack({ delta: 1 })
                }
              }
            : {
                title: '保存',
                onClick: form.handleSubmit(onSubmit)
              }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
