{"name": "taro-mobile", "version": "1.0.0", "main": "index.js", "repository": "*************:liushuang1126/taro-mobile.git", "author": "刘双 <<EMAIL>>", "license": "MIT", "scripts": {"clear-all": "rimraf package-lock.json packages/*/node_modules packages/*/package-lock.json packages/*/yarn.lock", "bootstrap:ci": "lerna bootstrap --npm-client=yarn", "bootstrap:lerna": "lerna bootstrap", "bootstrap": "npm-run-all clear-all bootstrap:lerna", "dev:h5": "cd packages/mobile && npm-run-all dev:h5", "dev:weapp": "cd packages/mobile && npm-run-all dev:weapp", "dev:rn": "cd packages/mobile && npm-run-all dev:rn"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/runtime": "^7.11.2", "@react-native-community/eslint-config": "^2.0.0", "@tarojs/mini-runner": "3.3.7", "@tarojs/webpack-runner": "3.3.7", "@types/react": "^17.0.2", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "babel-jest": "^26.6.3", "babel-preset-taro": "3.3.7", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.5", "eslint": "^6.8.0", "eslint-config-taro": "3.3.7", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "install-peerdeps": "^3.0.3", "jest": "^26.6.3", "jest-junit": "^12.0.0", "jest-react-native": "^18.0.0", "lerna": "^4.0.0", "metro-react-native-babel-preset": "^0.64.0", "npm-run-all": "^4.1.5", "react-test-renderer": "^17.0.1", "sinon": "^9.2.0", "stylelint": "9.3.0", "typescript": "^4.1.0"}, "dependencies": {"@sentry/react": "^6.13.2", "app": "file:packages/app", "mobile": "file:packages/mobile", "pont-engine": "^1.3.3", "react-pdf": "^5.5.0", "react-pdf-js": "^5.1.0", "react-transition-group": "4.4.2"}}