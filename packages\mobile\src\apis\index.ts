import { defs as usersDefs, users } from './users';

import { defs as uploadDefs, upload } from './upload';

import { defs as dynamicsFormsDefs, dynamicsForms } from './dynamicsForms';

import { defs as handlerDefs, handler } from './handler';

import { defs as hssDefs, hss } from './hss';

import { defs as pactDefs, pact } from './pact';

import { defs as ncmpDefs, ncmp } from './ncmp';

import { defs as authDefs, auth } from './auth';

export const defs = {
  users: usersDefs,
  upload: uploadDefs,
  dynamicsForms: dynamicsFormsDefs,
  handler: handlerDefs,
  hss: hssDefs,
  pact: pactDefs,
  ncmp: ncmpDefs,
  auth: authDefs,
};
export const API = {
  users,
  upload,
  dynamicsForms,
  handler,
  hss,
  pact,
  ncmp,
  auth,
};
const g: any = IS_RN ? global : window;
g.API = API;
