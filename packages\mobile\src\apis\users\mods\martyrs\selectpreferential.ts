/**
 * @description selectpreferential
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = Array<defs.users.Preferential>;
export const path = '/user-server/api/selectpreferential';
export const method = 'POST';
export const request = (
  data: defs.users.ReceiveParams,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.users.ReceiveParams,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
