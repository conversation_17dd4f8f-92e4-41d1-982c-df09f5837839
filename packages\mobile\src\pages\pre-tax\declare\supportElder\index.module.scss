.wrap {
  width: 100%;
  background-color: #ffffff;
  position: relative;
  flex: 1;
  /*  #ifndef rn */
  height: 100vh;
  /*  #endif  */
}

.item {
  background-color: #ffffff;
  height: 120px;
  border: solid 0px #e9ecef;
  border-bottom-width: 1px;
  padding: 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: row;
}

.img {
  width: 64px;
  height: 64px;
  margin-right: 16px;
  border-radius: 32px;
}

.text {
  font-size: 30px;
  color: #333333;
}
.addSupport {
  width: 100%;
  display: flex;
  justify-content: space-around;
  // padding: 0 10px 0 10px;
}
.addbtn {
  // width: 300px;
  // border: 1px solid #ff0000;
  // color: #ff0000;
  padding: 4px 10px 4px 10px;
  // margin: 0 10px 0 0;
  // width: 160px;
  height: 60px;
  min-width: 160px;
  background-color: #b51f24;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 4px 0 0;
}
.btn {
  color: #fff;
  font-size: 22px;
}
.addSupportbtn {
  display: flex;
  justify-content: space-around;
}
