/**
 * @description 根据open_id查询account_id
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openId */
  openId?: string;
}

export type Result = defs.pact.FollowerInfo;
export const path = '/yc-wepact-mobile/appbind/getFollowerInfoByOpenId';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
