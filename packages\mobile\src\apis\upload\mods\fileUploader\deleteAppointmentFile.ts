/**
 * @description 删除预约业务办理附件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 文件主键id */
  bookImageId: number;
}

export type Result = defs.upload.HttpResult<defs.upload.UploadResult>;
export const path = '/wx-upload/appointment/deleteAppointmentFile';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
