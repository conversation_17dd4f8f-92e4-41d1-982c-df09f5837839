/**
 * @description 判断是否拥有打卡地点和考勤规则
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 公司id */
  custId: number;
}

export type Result = defs.ncmp.HttpResult<ObjectMap<string, ObjectMap>>;
export const path = '/wx-ncmp/eos/attendance/checkin/getIsHasCheck';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
