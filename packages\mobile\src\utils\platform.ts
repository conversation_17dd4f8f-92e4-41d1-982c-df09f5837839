// 平台 1:公众号 2:企微 3:钉钉 4:飞书
export const getPlatformCode = () => {
  const ua = navigator.userAgent.toLowerCase()
  const isWx = /micromessenger/i.test(ua) // 是否微信
  const isComWx = /wxwork/i.test(ua) // 是否企业微信
  const isDingTalk = /dingtalk/i.test(ua) // 是否企业微信
  if (isWx && !isComWx) {
    return 1
  } else if (isComWx) {
    return 2
  } else if (isDingTalk) {
    return 3
  } else {
    return 1
  }
}
export const isWx = () => {
  return /micromessenger/i.test(navigator.userAgent.toLowerCase()) && !isComWx()
}

export const isComWx = () => {
  return /wxwork/i.test(navigator.userAgent.toLowerCase())
}

export const isDingTalk = () => {
  return /dingtalk/i.test(navigator.userAgent.toLowerCase())
}


// 平台JSSDK
