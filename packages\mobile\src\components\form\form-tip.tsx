import { FunctionComponent } from 'react'
import { View, Text, Image } from '@tarojs/components'
import icon_tip from '@assets/icon/icon-tip.png'
import styles from './index.module.scss'

const FormTip: FunctionComponent<{ tip: string }> = ({ tip }) => {
  return (
    <View className={styles.tip}>
      <Image className={styles.tip_img} src={icon_tip} />
      <Text className={styles.tip_text}>{tip}</Text>
    </View>
  )
}

export default FormTip
