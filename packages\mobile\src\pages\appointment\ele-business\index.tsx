import { useEffect, useState } from 'react'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { View, Text, Button, Image } from '@tarojs/components'
import { getGlobalData } from '@utils'
import { withPage } from '@components'
import { ncmp } from '@apis/ncmp'
import no_data from '@assets/no-data.png'
import styles from './index.module.scss'

const typeMap = {
  '1': '未发起',
  '2': '拟定中',
  '3': '签署中',
  '4': '作废中',
  '5': '已完成',
  '6': '已过期',
  '7': '已撤回',
  '9': '已作废',
  '10': '已发起作废'
}
const Index = () => {
  const [list, setlist] = useState<defs.ncmp.EleContract[] | unknown[]>([])
  const PlatformInfo = sessionStorage.getItem('PlatformInfo')
  const accountInfo = PlatformInfo && JSON.parse(PlatformInfo)
  let { empId } = getGlobalData<'account'>('account')
  if(!empId) {
    empId = accountInfo?.empId
  }
  const { eleBusinessStatus } = useRouter().params
  const title =
    eleBusinessStatus == '3'
      ? '暂无签署电子业务'
      : eleBusinessStatus == '4'
      ? '暂无作废电子业务'
      : '暂无可以查看电子业务'
  useDidShow(() => {
    if (!empId) return
    let elecBusinessServer
    if (eleBusinessStatus == '3') {
      elecBusinessServer = ncmp.elecBusiness.signElecBusinessMaterial
    } else if (eleBusinessStatus == '4') {
      elecBusinessServer = ncmp.elecBusiness.delElecBusinessMaterial
    } else {
      elecBusinessServer = ncmp.elecBusiness.getEleContractList
    }
    elecBusinessServer
      .request(
        {
          empId
        },
        { isToken: true }
      )
      .then((res: any) => {
        if (res.code === '200') {
          Array.isArray(res?.resultObj) && setlist(res?.resultObj || [])
        } else {
          Taro.showModal({
            title: '提示',
            content: res.errorMsg || '系统异常!',
            showCancel: false
          })
        }
      })
      .catch(() => {
        Taro.showToast({ title: '系统异常!', icon: 'none' })
      })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  })
  useDidShow(() => {
    const title1 = eleBusinessStatus == '3' ? '签署电子业务' : eleBusinessStatus == '4' ? '作废电子业务'  : '查看电子业务'
    Taro.setNavigationBarTitle({title: title1})
  })
  const toEelSignaturePage = (params = {}) => {
    const server =
      eleBusinessStatus == '3' || eleBusinessStatus == '4'
        ? ncmp.elecBusiness.getElecSignBusinessUrl
        : ncmp.elecBusiness.getEleContractUrl
    server.request(params, { isToken: true }).then(res => {
      if (res.code === '200') {
        const eleSinUrl = res?.resultObj?.eleSinUrl || ''
        if (!eleSinUrl) {
          Taro.showToast({ title, icon: 'none' })
          return
        }
        window.location.href = eleSinUrl
      }
    })
  }
  return (
    <View className={styles.wrap}>
      {list.length ? (
        list.map((item: defs.ncmp.EleContract & { eleContractStatus: string }) => (
          <View
            className={styles.item}
            key={item.eleContractName}
            onClick={() => {
              toEelSignaturePage(item)
            }}
          >
            <Text className={!eleBusinessStatus ? styles.eleContractName : styles.eleContractName1}>
              {item.eleContractName}
            </Text>
            {!eleBusinessStatus && (
              <Button className={styles.eleContractStatus}>{typeMap[item.eleContractStatus]}</Button>
            )}
          </View>
        ))
      ) : (
        <View className={styles.empty}>
          <Image className={styles.no_data} src={no_data} />
          <Text className={styles.no_text}>{title}</Text>
        </View>
      )}
    </View>
  )
}

export default withPage(Index)
