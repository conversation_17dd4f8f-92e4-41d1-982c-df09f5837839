.wrap {
  width: 100%;
  background-color: #ffffff;
  flex: 1;
  position: relative;
  font-size: 28px;
}

.header_user {
  height: 229px;
  padding: 30px 0;
  background: #B51E25;
  color: #FFF;
  position: relative;
}

.bgImage {
  // display: block;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 0;
}

.bgImage_right {
  position: absolute;
  right: 0;
  z-index: 0;
}

.bg_image_right {
  width: 200px;
  height: 229px;
}

.bg_image {
  width: 120px;
  height: 120px;
}

.user_info {
  display: flex;
  position: relative;
  flex-direction: column;
  z-index: 1;
}

.user_avatar {
  // display: block;
  width: 120px;
  height: 120px;
  margin: 0 auto;
  border-radius: 100px;
}

.user_group {
  display: flex;
  margin-top: 10px;
}

.user_group_left {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 0px 15px;
  position: relative;
}

.user_group_inner {
  display: flex;
  align-items: center;
  font-size: 26px;
  color: #ffffff;
}

.icon_text {
  // display: block;
  margin-right: 5px;
  height: 25px;
  width: 25px;
}

.group_item {
  background: #FFF;
  margin: 10px 0;
}

.info {
  display: flex;
  flex-direction: row;
  align-items: center;
  min-height: 96px;
  padding: 14px 30px;
  border: 1px solid #EFEFF4;
  font-size: 28px;
  // border: 1px 0 1px 0;
  // border-color: #EFEFF4;
  // border-style: solid;
  line-height: 20px;
}

.item_title {
  width: 240px;
  padding: 0 20px 0 0;
  font-size: 28px;
}

.item_data {
  flex: 1;
  min-height: 15px;
  color: lighten(#000, 40%);
  font-size: 28px;
}

.title_mark {
  position: absolute;
  left: 0;
  width: 14px;
  height: 24px;
  margin: 10px 0;
  background: #B51E25;
}

.title_secondary {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 28px 30px;
  height: 97px;
  border: solid 1px #EFEFF4;
  font-weight: 400;
  position: relative;
}

.box_b {
  padding: 30px;
}

.box_dotted {
  padding: 20px;
  border: dashed 1px darken(#FFF, 15.5%);
  border-radius: 4px;
}

.sun_row {
  display: flex;
  flex-direction: row;
}

.sun_col {
  width: 333px;
  height: 254px;
  padding: 10px;
}

.sun_thumb {
  position: relative;
  background: #EFEFF4;
  width: 313px;
  height: 234px;
  // padding: 234px 0 0;
}

.list_stripe_info {
  display: flex;
  flex-direction: row;
  align-items: center;
  // min-height: 96px;
  // padding: 14px 30px;
  // border: 1px solid #EFEFF4;
  font-size: 28px;
  line-height: 20px;
  padding: 14px 20px;
  min-height: 67px;
  // background: #EFEFF4;
}

.list_stripe_info_dark {
  display: flex;
  flex-direction: row;
  align-items: center;
  // min-height: 96px;
  // padding: 14px 30px;
  // border: 1px solid #EFEFF4;
  font-size: 28px;
  line-height: 20px;
  padding: 14px 20px;
  min-height: 67px;
  background: #EFEFF4;
}

.list_stripe_item_title {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 160px;
  height: 39px;
  // padding: 0 20px 0 0;
  font-size: 28px;
}

// .list_stripe {
//   .info {
//     padding: 14px 20px;
//     min-height: 67px;

//     &:nth-of-type(even) {
//       background: #EFEFF4;
//     }
//   }

//   .item_title {
//     width: 160px;
//     height: 39px;
//   }
// }
