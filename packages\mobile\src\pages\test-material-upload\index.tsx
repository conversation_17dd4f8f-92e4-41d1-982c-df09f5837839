import { useState } from 'react'
import { View } from '@tarojs/components'
import { SimpleMaterialUpload, withPage } from '@components'
import type { FileInfo } from '@components/simple-material-upload'
import { getGuid } from '@utils'

const TestMaterialUpload = () => {
  // 已上传状态的文件数据（参考图中的"2.合同"）
  const [files1, setFiles1] = useState<FileInfo[]>([
    {
      fileName: '2343223.jpg',
      filePath: '/upload/2343223.jpg',
      fileUrl: 'https://example.com/2343223.jpg',
      bookImageId: *************
    }
  ])

  // 未上传状态（参考图中的"3.申请表"）
  const [files2, setFiles2] = useState<FileInfo[]>([])
  const [uuid] = useState(getGuid())

  return (
    <View style={{ padding: '20px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 已上传状态示例 - 只显示下载示例按钮（居中） */}
      <SimpleMaterialUpload
        material={{
          materialsId: '002',
          materialsName: '合同',
          isOriginal: '0',
          materialsAccount: 1,
          isReturn: '0',
          isRequired: true
        }}
        files={files1}
        uuid={uuid}
        onFileChange={setFiles1}
        onUploadSuccess={(file) => {
          console.log('合同上传成功:', file)
        }}
        onUploadError={(error) => {
          console.error('合同上传失败:', error)
          // 这里可以添加额外的错误处理逻辑，比如记录日志等
        }}
      />

      {/* 未上传状态示例 - 显示上传材料和下载示例按钮（左右分布） */}
      <SimpleMaterialUpload
        material={{
          materialsId: '003',
          materialsName: '申请表',
          isOriginal: '0',
          materialsAccount: 3,
          isReturn: '0',
          isRequired: false
        }}
        files={files2}
        uuid={uuid}
        onFileChange={setFiles2}
        onUploadSuccess={(file) => {
          console.log('申请表上传成功:', file)
        }}
        onUploadError={(error) => {
          console.error('申请表上传失败:', error)
          // 这里可以添加额外的错误处理逻辑，比如记录日志等
        }}
      />
    </View>
  )
}

export default TestMaterialUpload
