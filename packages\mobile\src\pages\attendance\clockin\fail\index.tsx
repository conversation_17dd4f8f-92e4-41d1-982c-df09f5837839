import Taro from "@tarojs/taro";
import styles from "./index.module.scss";
import FailImg from "@assets/attendance/fail.png";
import { withPage } from "@components/index";
import { View } from "@tarojs/components";

const Index = () => {
  const endtime = Taro.getCurrentInstance()?.router?.params.endtime;
  return (
    <div className={styles.containers}>
      <View className={styles.wrapper}>
        <View className={styles.box_wrap}>
          <img className={styles.success} src={FailImg} />
          <View className={styles.times + " " + styles.timeerror}>
            {endtime}
          </View>
          <View className={styles.suc + " " + styles.timeerror}>
            网络链接错误
          </View>
        </View>
      </View>
    </div>
  );
};

export default withPage(Index);
