.policy_filter{
    background-color: #fff;
    height: 100vh;
    display: flex;
    justify-content: space-between;
    padding-left: 12px;
    padding-right: 12px;
    overflow: hidden;
    border: 1px solid rgb(222, 222, 222);
}
.firstLevel{
    padding-left: 12px;
    padding-top: 10px;
    width: 48%;
}
.secondLevel{
    width: 48%;
    padding-left: 12px;
    padding-top: 10px;
    border-left: 1px solid rgb(222, 222, 222);
}
.font_size{
    font-size: 28px;
}
.opts_container{
    font-size: 28px;
}
.selected{
    border-right: 1px solid rgb(222, 222, 222);
    border-left: 1px solid rgb(222, 222, 222);
}
.item{
    padding: 5px 0px;
    overflow: hidden;
    white-space: nowrap; 
    text-overflow: ellipsis;
}
.active{
    color: #b51e25;
}
.empty {
    height: 100%;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: -20%;
  }
  .no_data {
    width: 160px;
    height: 103px;
    margin-bottom: 40px;
  }
  
  .no_text {
    color: #d9d9d9;
    font-size: 28px;
  }