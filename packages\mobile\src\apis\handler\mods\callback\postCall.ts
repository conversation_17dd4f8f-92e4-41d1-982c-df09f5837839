/**
 * @description call
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** creationTime */
  creationTime?: number;
  /** id */
  id?: string;
  /** lastAccessedTime */
  lastAccessedTime?: number;
  /** maxInactiveInterval */
  maxInactiveInterval?: number;
  /** new */
  new?: boolean;
  /** classLoader */
  classLoader?: ref;
  /** contextPath */
  contextPath?: string;
  /** defaultSessionTrackingModes */
  defaultSessionTrackingModes?: Array<'COOKIE' | 'URL' | 'SSL'>;
  /** effectiveMajorVersion */
  effectiveMajorVersion?: number;
  /** effectiveMinorVersion */
  effectiveMinorVersion?: number;
  /** effectiveSessionTrackingModes */
  effectiveSessionTrackingModes?: Array<'COOKIE' | 'URL' | 'SSL'>;
  /** buffer */
  buffer?: string;
  /** defaultContentType */
  defaultContentType?: string;
  /** deferredSyntaxAllowedAsLiteral */
  deferredSyntaxAllowedAsLiteral?: string;
  /** elIgnored */
  elIgnored?: string;
  /** errorOnUndeclaredNamespace */
  errorOnUndeclaredNamespace?: string;
  /** includeCodas */
  includeCodas?: Array<string>;
  /** includePreludes */
  includePreludes?: Array<string>;
  /** isXml */
  isXml?: string;
  /** pageEncoding */
  pageEncoding?: string;
  /** scriptingInvalid */
  scriptingInvalid?: string;
  /** trimDirectiveWhitespaces */
  trimDirectiveWhitespaces?: string;
  /** urlPatterns */
  urlPatterns?: Array<string>;
  /** taglibLocation */
  taglibLocation?: string;
  /** taglibURI */
  taglibURI?: string;
  /** majorVersion */
  majorVersion?: number;
  /** minorVersion */
  minorVersion?: number;
  /** requestCharacterEncoding */
  requestCharacterEncoding?: string;
  /** responseCharacterEncoding */
  responseCharacterEncoding?: string;
  /** serverInfo */
  serverInfo?: string;
  /** servletContextName */
  servletContextName?: string;
  /** comment */
  comment?: string;
  /** domain */
  domain?: string;
  /** httpOnly */
  httpOnly?: boolean;
  /** maxAge */
  maxAge?: number;
  /** name */
  name?: string;
  /** path */
  path?: string;
  /** secure */
  secure?: boolean;
  /** sessionTimeout */
  sessionTimeout?: number;
  /** virtualServerName */
  virtualServerName?: string;
  /** valueNames */
  valueNames?: Array<string>;
}

export type Result = string;
export const path = '/handler-service/call';
export const method = 'POST';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
