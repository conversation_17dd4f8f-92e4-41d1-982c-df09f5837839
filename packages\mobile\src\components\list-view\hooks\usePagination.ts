import { useEffect, useState, useCallback } from 'react'
import isEmpty from 'lodash/isEmpty'

const usePagination = <T = any[]>(
  requestFn: (page: number) => Promise<T>,
  options?: {
    auto?: boolean // 默认为 true
    deps?: any[]
    defalutPage?: number // 默认为1
  }
) => {
  const { auto = true, deps = [], defalutPage = 1 } = options || {}
  // const [loading, setLoading] = useState<boolean>(false)
  const [pageData, setPageData] = useState<T>()
  const [page, setPage] = useState<number>(defalutPage)
  const handleRequest = async (nextPage: number) => {
    try {
      // setLoading(true)
      // Taro.showLoading({ title: '' })
      const data = await requestFn(nextPage)
      if (nextPage === defalutPage) {
        setPageData(data)
      } else {
        const newData = [...(pageData || ([] as any)), ...(data as any)] as any
        setPageData(newData)
      }
      setPage(nextPage)
      // setLoading(false)
      // Taro.hideLoading()
      return !isEmpty(data)
    } catch (error) {
      // setLoading(false)
      // Taro.hideLoading()
      return false
    }
  }

  const onLoadMore = () => handleRequest(page + 1)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const onRefresh = useCallback(async () => handleRequest(defalutPage), [...deps])
  useEffect(() => {
    // TODO：节流
    auto && onRefresh()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [auto, ...deps])
  return {
    itemData: pageData || ([] as any),
    // loading,
    onLoadMore,
    onRefresh
  }
}

export { usePagination }
