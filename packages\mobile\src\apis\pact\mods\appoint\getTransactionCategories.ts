/**
 * @description 事务类别接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** categoryId */
  categoryId?: string;
  /** cityId */
  cityId?: string;
  /** openId */
  openId?: string;
}

export type Result = defs.pact.ThingsResponseBean;
export const path = '/yc-wepact-mobile/appoint/getTransactionCategories';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
