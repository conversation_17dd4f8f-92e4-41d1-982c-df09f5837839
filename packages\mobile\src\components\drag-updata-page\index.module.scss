.dragUpdataPage {
  height: 1252px;
  position: relative;
  overflow: hidden;
}
.downDragBox {
  width: 750px;
  top: 0px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  font-size: 28px;
  position: absolute;
}
.upDragBox {
  bottom: 0px;
  width: 750px;
  display: flex;
  overflow: hidden;
  justify-content: center;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  font-size: 28px;
  position: absolute;
}
.dragUpdata {
  height: 100%;
  width: 750px;
  position: absolute;
}
.downText {
  margin-left: 20px;
}
