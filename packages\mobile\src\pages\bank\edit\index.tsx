/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-08-31 14:05:13
 * @LastAuthor: 王正荣
 * @LastTime: 2021-11-26 16:29:46
 * @message:
 */
import { useEffect } from 'react'
import { View } from '@tarojs/components'
import { Form, BottomBtn, FormItemProps, withPage, useForm } from '@components'
import Taro, { useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils'
import { users } from '@apis/users'
import { getGlobalData } from '@utils/global-data'
import styles from './index.module.scss'
import { useDidHide } from '@tarojs/runtime'

const Index = () => {
  const { empId, openId: cmpToken } = getGlobalData<'account'>('account')
  const { type, originListData } = useRouter<{ type: 'create' | 'update'; originListData: string }>().params
  console.log('type', type)
  const form = useForm()
  const { result } = users.baseData.getBaseData.useRequest({ pageNum: '1', typeId: '911' })
  const openBankOptions = result?.data || ([] as defs.users.BaseData[])
  const onSubmit = (values: any) => {
    console.log('values----', values)
    users.bankcardInfo.insertBankcardInfo
      .request({
        ...values,
        empId,
        cmpToken,
        // provinceId: '10708' //provinceId要传
      })
      .then(res => {
        if (res.data?.[0]?.code === '1') {
          Taro.eventCenter.trigger('bankCard')
          Taro.navigateTo({ url: '/pages/bank/update-success/index' })
        } else {
          Taro.navigateTo({ url: '/pages/bank/update-err/index' })
        }
      })
  }
  useEffect(() => {
    if (type === 'update') {
      const listData = JSON.parse(originListData)
      form.reset(listData[0])
    }
  }, [originListData, type, form])
  useDidHide(() => {
    Taro.setNavigationBarTitle({ title: type === 'update' ? '修改银行卡' : '新增银行卡' })
  })
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const columns: FormItemProps[] = [
    {
      name: 'bankAcct',
      type: 'text',
      title: '银行卡号',
      rules: { required: true, maxLength: 25 }
    },
    {
      name: 'accountEmployeeName',
      type: 'text',
      title: '开户姓名',
      rules: { required: true, maxLength: 25 }
    },
    {
      name: 'bankId',
      type: 'select',
      title: '银行名称',
      options: openBankOptions,
      rules: { required: true }

    },
    {
      name: 'openBankName',
      type: 'text',
      title: '开户行全称',
      rules: { required: true }
    },
    {
      name: 'openAddress',
      type: 'text',
      title: '开户地',
      rules: { required: true }
    },
    {
      name: 'cityId',
      type: 'page_choice',
      title: '省市',
      rules: { required: true },
      pageOptions: {
        keys: ['cityId', 'cityName', 'provinceId', 'provinceName'],
        labelKey: 'cityName',
        scen: '2',
        url: '/city'
      }
    }
  ]
  return (
    <View className={styles.wrap}>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '提交',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
