import { useEffect, useState, useCallback } from 'react'
import Taro from '@tarojs/taro'
import { BaseUrl } from '../../config/env';
import { getGlobalData } from '.';
// const BaseUrl = 'https://wehr.ctgapp.com'

const handleUrlPath = (url: string, data?: any) => {
  let _url = url
  if (url.includes('/{')) {
    _url = ''
    url.split('/').forEach(item => {
      if (item.includes('}')) {
        const key = item.substr(1, item.length - 2)
        _url = _url + '/' + data?.[key]
        data && delete data[key]
      } else {
        _url = _url + ('/' + item)
      }
    })
  }
  return _url
}

// TODO: 多请求统一Loading
let requestCount = 0 // 正在进行的请求
const _request = async function request<R = any, P = any>(
  url: string,
  data?: P,
  options?: Taro.request.CommonOption
): Promise<R> {
  console.group()
  const account = getGlobalData<'account'>('account')
  const PlatformInfo = sessionStorage.getItem('PlatformInfo')
  const globalToken = PlatformInfo &&  JSON.parse(PlatformInfo).globalToken
  const otehrHeader = { wxGlobalToken: globalToken}
  let header = {
    'Content-Type': 'application/json',
  }
  if (globalToken) {
    header = {
      'Content-Type': 'application/json',
      ...otehrHeader
    }
  }
  let _url = handleUrlPath(url, data)
  console.log('开始请求----', _url,data)
  const { isToken = false} = options || {}
  if (isToken) {
    header['cmpToken'] = account.latestToken ?? account.token
  }
  try {
    const res: any = await Taro.request({
      url: `${BaseUrl}${_url}`,
      data,
      header,
      ...options
    })
    console.log('请求成功----', res)
    console.groupEnd()
    const { statusCode } = res
    if (statusCode === 200) {
      const result = res.data
      if (result.successful) {
        return result.data
      } else if (result.errorCode && result.errorCode !== '0') {
        throw result
      } else {
        // 返回结果非标准格式
        return result
      }
    } else {
      throw res.data
    }
  } catch (error) {
    console.log('请求失败----', error)
    console.groupEnd()
    showErr(error.errorMessage || '系统异常')
    throw error
  }
}

const useRequest = function<R = any, P = any>(
  url: string,
  data?: P,
  options?: Taro.request.CommonUseRequestOption<P>
): Taro.request.CommonUseResultType<R, P> {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState()
  const [error, setError] = useState()
  const { manual = false, deps = [], ...optionsRest } = options || {}
  const run: (data?: any) => Promise<any> = useCallback(
    (params = data) => {
      setLoading(true)
      return request(url, params, optionsRest)
        .then(res => {
          setResult(res as any)
        })
        .catch(err => {
          setError(err)
        })
        .finally(() => {
          setLoading(false)
        })
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )
  useEffect(() => {
    if (!manual) {
      run(data)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [manual, ...deps])
  return { result, loading, error, run }
}

const request = async function request<R = any, P = any>(
  url: string,
  data?: P,
  options?: Taro.request.CommonOption
): Promise<R> {
  const { showLoading = true, ...restOptions } = options || {}
  if (showLoading) {
    requestCount = requestCount + 1
    requestCount === 1 && Taro.showLoading({ title: '加载中...', mask: true })

  }
  return _request(url, data, restOptions).finally(() => {
    if (showLoading) {
      requestCount = requestCount - 1
      requestCount === 0 && Taro.hideLoading()
    }
  })
}

const showErr = (title?:string) => {
  title && setTimeout(() => {
    Taro.showToast({ title, icon: 'none' })
  }, 0);
}

const showSuccess = (title?:string) => {
  title && setTimeout(() => {
    Taro.showToast({title,icon:'success'})
  }, 0);
}

export { request, useRequest, BaseUrl, showErr,showSuccess }
