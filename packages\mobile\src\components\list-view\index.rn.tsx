import { FunctionComponent, useEffect, useState } from 'react'
import RefreshListView from './RefreshListView'
import { ListViewProps, RefreshStateType } from './type'

const RefreshStateMap = {
  Default: 0,
  Refresh: 1,
  LoadMore: 2,
  NoMoreData: 3
}
const ListView: FunctionComponent<ListViewProps> = props => {
  const { renderItem, refreshState, onRefresh, onLoadMore, noPull, noRefresh, ...rest } = props
  const [_refreshState, setRefreshState] = useState<RefreshStateType>('Default')
  useEffect(() => {
    refreshState && setRefreshState(refreshState)
  }, [refreshState])
  return (
    <RefreshListView
      onHeaderRefresh={async () => {
        if (noRefresh) return
        setRefreshState('Refresh')
        await onRefresh?.()
        setRefreshState('Default')
      }}
      onFooterRefresh={async () => {
        if (noPull) return
        if (_refreshState === 'Default') {
          setRefreshState('LoadMore')
          const canLoadMore = await onLoadMore?.()
          setRefreshState(canLoadMore ? 'Default' : 'NoMoreData')
        }
      }}
      refreshState={RefreshStateMap[_refreshState]}
      data={props.itemData}
      footerRefreshingText='加载中...'
      footerFailureText='我擦嘞，居然失败了 =.=!'
      footerNoMoreDataText='- 没有更多数据 -'
      footerEmptyDataText='- 好像什么东西都没有 -'
      renderItem={({ item, index }) => renderItem(item, index)}
      {...rest}
    />
  )
}

export default ListView
