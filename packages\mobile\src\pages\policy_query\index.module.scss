.page {
  height: 100vh;
  background-color: #f7f7f7;
}
.wrap {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  align-items: flex-end;
  justify-content: flex-end;
  padding-bottom: 12px;
  box-shadow: 5px 10px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 888;
  // box-shadow:  0px 24px 36px -24px #eee;
}
.input_box{
  display: flex;
  flex-direction:row;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
}
.search_filter{
  display: flex;
  flex-direction:row;
  align-items: center;
  justify-content: center;
}
.filter{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  padding: 25px 25px 20px 15px;
  border-radius: 8px;
  margin-right: 23px;
  position: relative;
}
.input {
  width: 460px;
  height: 72px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 30px;
  font-size: 28px;
  color: #333;
}
.input_btn {
  background-color: #b51e25;
  border-radius: 5px;
  width: 72px;
  height: 72px;
  text-align: center;
  line-height: 72px;
}
.input_wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.input_search {
  width: 580px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 15px;
  background-color: #f0f0f0;
  padding-left: 30px;
  margin-right: 30px;
}
.more {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.font_size {
  font-size: 24px;
  line-height: 5px;
}
.empty {
  flex-direction: column;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
}
.no_data {
  width: 160px;
  height: 103px;
}

.no_text {
  color: #d9d9d9;
  font-size: 28px;
}

.policy {
  background-color: #fff;
  padding: 20px 0px;
  margin: 0 14px;
 // margin: 0px 14px 0px;
  border-radius: 6px;
}
.policy_t {
  font-size: 32px;
  padding: 12px 20px;
  border-bottom: 1px solid #ccc;
  // width: 93vw;

}
.ellipsis{
  overflow: hidden;
  white-space: nowrap; 
  text-overflow: ellipsis;
}
.policy_y {
  font-size: 24px;
  padding: 12px 24px;
}
.policy_opts {
  padding: 12px 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.policy_c1 {
  font-size: 24px;
  // width: 160px;
  // height: 50px;
  padding: 10px 20px;
  // line-height: 50px;
  border-radius: 8px;
  border: 1px solid #ccc;
  text-align: center;
  margin-right: 30px;
}
.keywords_color{
    color: #b51e25;
}
.down_triangle {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;  /* 左边的透明边框 */
  border-right: 8px solid transparent; /* 右边的透明边框 */
  border-top: 15px solid #ccc;      /* 顶部的颜色边框 */
  position: absolute; /* 设定为绝对定位 */
  bottom: 5px;         /* 距离底部0 */
  right: 5px;  
  transform: rotate(190deg);   
}