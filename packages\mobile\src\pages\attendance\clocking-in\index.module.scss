.wrap {
  width: 100%;
  background-color: #ffffff;
  flex: 1;
  position: relative;
  font-size: 28px;
}

.tabPane {
  height: 100%;
  padding-top: 10px;
}

.dayItem {
  padding: 10px 30px;
  display: flex;
  align-items: center;
  gap: 40px;
  border: 1px solid #000;
  margin-top: -1px;
}

.dayItemRight {
  flex: 1;
}

.colorGray {
  color: gray;
}

.colorRed {
  color: red;
}

// :global(.at-calendar .mark) {
//   display: none;
// }
// :global(.at-calendar .flex__item--now .mark) {
//   display: block;
//   background-color: red !important;
//   width: 10px;
//   height: 10px;
//   margin-top: -10px;
//   border-radius: 50%;
//   margin: 0 auto;
// }

.monthCalendar {
  :global(.main.at-calendar-slider__main) {
    display: none;
  }
}

.monthItem {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
}
