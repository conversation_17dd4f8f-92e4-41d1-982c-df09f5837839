/**
 * @description 批量处理模板消息发送
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = object;
export const path = '/yc-wepact-mobile/template-msg/send';
export const method = 'POST';
export const request = (
  data: Array<defs.pact.TemplateMessageParam>,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: Array<defs.pact.TemplateMessageParam>,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
