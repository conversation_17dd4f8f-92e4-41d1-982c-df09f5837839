.wrap {
  width: 100%;
  background-color: #ffffff;
  position: relative;
  flex: 1;
  /*  #ifndef rn */
  height: 100vh;
  /*  #endif  */
}
.title {
  display: flex;
  flex: 1;
  width: 100%;
  // font-size: 40px !important;
  // height: 200px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // background-color: red;
  margin: 60px 0 20px 0;
}
.cont {
  display: flex;
  flex: 1;
  width: 100%;
  font-size: 40px;
  // height: 200px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 30px 0 30px;
  color: #666;
  font-size: 14px;
}
.checkbox {
  display: flex;
  flex-direction: row;
  // align-items: center;
  justify-content: flex-start;
  padding: 0 30px 0 30px;
  margin: 60px 0 0 0;
  // vertical-align: middle;
}
.checkd {
  align-items: center;
  width: 40px;
  height: 40px;
  background: #fff;
  // vertical-align: middle;
  // display: inline-block;
  margin: 0 20px 0 0px;
}
