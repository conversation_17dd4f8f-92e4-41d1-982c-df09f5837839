/**
 * 税前抵扣申请-基础信息
 */
import { useEffect, useState } from 'react'
import { View, Button } from '@tarojs/components'
import { BottomBtn, withPage, useForm, Form, FormItemProps } from '@components'
import { useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils/transforms'
import { navigateTo, getGlobalData } from '@utils'
import isEmpty from 'lodash/isEmpty'

import { users } from '@apis/users'

import styles from './index.module.scss'

const cardType = [
  { key: '1', value: '居民身份证' },
  { key: '2', value: '中国护照' },
  { key: '3', value: '港澳居民来往内地通行证' },
  { key: '4', value: '港澳居民居住证' },
  { key: '5', value: '台湾居民来往大陆通行证' },
  { key: '6', value: '台湾居民居住证' },
  { key: '7', value: '外国护照' },
  { key: '8', value: '外国人永久居留身份证' },
  { key: '9', value: '外国人工作许可证（A类）' },
  { key: '10', value: '外国人工作许可证（B类）' },
  { key: '11', value: '外国人工作许可证（C类）' },
  { key: '12', value: '其他个人证件' }
]
const spouse = [
  { key: 0, value: '无配偶' },
  { key: 1, value: '有配偶' }
]
const custName = [
  { key: '0', value: '客户0' },
  { key: '1', value: '客户1' }
]
const Index = () => {
  const accountId = getGlobalData<'account'>('account').accountId
  const openId = getGlobalData<'account'>('account').openId
  const employeeId = getGlobalData<'account'>('account').empId

  const [personInfo, setPersonInfo] = useState<defs.users.EssentialInfo | undefined>()
  const { type = 'create' } = useRouter<{ type: 'create' | 'update' }>().params
  const form = useForm()
  const hasSpouse = form.watch('hasSpouse')
  const annualDeduction = form.watch('annualDeduction')
  const taxpayerCardType = form.watch('taxpayerCardType')
  const spouseCardType = form.watch('spouseCardType')
  const { result } = users.baseData.getBaseData.useRequest({ pageNum: '1', typeId: '911' })
  const openBankOptions = result?.data || ([] as defs.users.BaseData[])
  const onSubmit = (values: any) => {
    // console.log('values----', values)
    values.employeeId = '*********'
    users.user.saveEssential.request(values).then(res => {
      // console.log(res)
      if (res.code == 0) {
        // console.log('保存成功')
        Taro.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000
        })
        Taro.navigateTo({
          url: '/pages/pre-tax/declare-list/index'
        })
      }
    })
  }

  const [flag, setFlag] = useState<boolean>(true)

  useEffect(() => {
    // 获取用户信息
    // getAccountInfo
    users.user.getAccountInfo
      .request({
        accountId: accountId,
        openId: openId
      })
      .then(res => {
        // console.log(res.data)
        !isEmpty(res?.data) && form.reset({ ...res?.data })
        users.user.getEssentialInfo
          .request({
            employeeId: employeeId
          })
          .then(ress => {
            // console.log(ress)
            !isEmpty(ress?.data) && form.reset({ ...ress?.data })
          })
      })
    // 获取客户名称
    //
    //
    // getCustName
    users.user.getCustName
      .request({
        employeeId: employeeId
      })
      .then(res => {
        console.log(res,'客户信息')
      })
  }, [employeeId])

  useEffect(() => {
    // console.log(hasSpouse)
    if (hasSpouse == 0) {
      setFlag(true)
    } else if (hasSpouse == 1) {
      setFlag(false)
    }
  }, [hasSpouse])

  useEffect(() => {
    form.setValue('employeeId', employeeId)
  }, [employeeId])

  useEffect(() => {
    let today = new Date()
    let date = today.getFullYear()
    form.setValue('annualDeduction', date)
  }, [annualDeduction])

  const getDate = () => {
    let today = new Date()
    let date = today.getFullYear()
    return date
  }
  const scrollStyle = getScrollStyle({ bottom: 120 })

  const columns: FormItemProps[] = [
    {
      name: 'annualDeduction',
      type: 'text',
      title: '扣除年度',
      rules: { required: true }
    },
    {
      name: 'taxpayerName',
      type: 'text',
      title: '纳税人姓名',
      rules: { required: true }
    },
    {
      name: 'taxpayerCardType',
      type: 'select',
      title: '身份证件类型',
      options: cardType,
      rules: { required: true },
    },
    {
      name: 'taxpayerCardNumber',
      type: 'id_card',
      title: '身份证件号码',
      rules: {
        required: true,
        pattern: taxpayerCardType == 1 ? {
          value: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/,
          message: '请输入正确的身份证格式'
        }: 
        {
          value: /([^\.\d]|^)(\d+)([^\.\d]|$)/,
          message: ''
        }
      }
    },
    {
      name: 'custId',
      type: 'select',
      title: '客户名称',
      options: custName,
      rules: { required: true }
    },
    {
      name: 'taxpayerNumber',
      type: 'text',
      title: '纳税人识别号',
      rules: { required: false }
    },
    {
      name: 'taxpayerPhone',
      type: 'mobile',
      title: '手机号码',
      rules: {
        required: true,
        pattern: {
          value: /^1[3-9]\d{9}$/,
          message: '请输入正确的手机号码格式'
        }
      }
    },
    {
      name: 'taxpayerAddress',
      type: 'text',
      title: '联系地址',
      rules: { required: false }
    },
    {
      name: 'taxpayerEmail',
      type: 'email',
      title: '电子邮箱',
      rules: {
        required: false,
        pattern: {
          value: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
          message: '请输入正确的邮箱格式'
        }
      }
    },
    {
      name: 'hasSpouse',
      type: 'single',
      title: '配偶情况',
      options: spouse,
      rules: { required: true }
    },
    {
      isHidden: flag,
      name: 'spouseName',
      type: 'text',
      title: '配偶姓名',
      rules: { required: true }
    },
    {
      isHidden: flag,
      name: 'spouseCardType',
      type: 'select',
      title: '配偶身份证件类型',
      options: cardType,
      rules: { required: true }
    },
    {
      isHidden: flag,
      name: 'spouseCardNumber',
      type: 'id_card',
      title: '配偶身份证件号码',
      rules: {
        required: true,
        pattern: spouseCardType == 1 ? {
          value: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/,
          message: '请输入正确的身份证格式'
        }:
        {
          value: /([^\.\d]|^)(\d+)([^\.\d]|$)/,
          message: ''
        }
      }
    }
  ]

  return (
    <View className={styles.wrap}>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '保存',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
