/* eslint-disable react-hooks/exhaustive-deps */
import React, { FunctionComponent, memo, useCallback, useEffect, useState } from 'react'
import { Text, View } from '@tarojs/components'
import VirtualList from '@tarojs/components/virtual-list'
import { getNumberSize, getGlobalData } from '@utils'
import PullToRefresh from 'pulltorefreshjs'
import { ListViewProps, RefreshStateType } from './type'
import styles from './index.module.scss'
import { LoadingSVG } from './svg'

const ListView: FunctionComponent<ListViewProps> = props => {
  const {
    onScrollToUpper,
    onScrollToLower,
    itemData,
    renderItem,
    refresherEnabled,
    refreshState,
    height,
    width = '100%',
    itemSize,
    style,
    onRefresh,
    onLoadMore,
    noPull,
    noRefresh,
    ...rest
  } = props
  const [_refreshState, setRefreshState] = useState<RefreshStateType>('Default')
  useEffect(() => {
    refreshState && setRefreshState(refreshState)
  }, [refreshState])
  const renderBottom = useCallback(() => {
    if (_refreshState === 'LoadMore') {
      return (
        <View className={styles.footer}>
          <Text className={styles.footer_text}>加载中...</Text>
        </View>
      )
    }
    if (_refreshState === 'NoMoreData') {
      return (
        <View className={styles.footer}>
          <View className={styles.footer_text}>- 没有更多数据 -</View>
        </View>
      )
    }
  }, [_refreshState])
  const listRef = React.useRef<{ field: any }>(null)
  useEffect(() => {
    PullToRefresh.init({
      mainElement: '#list-view',
      onRefresh() {
        setTimeout(() => {
          onRefresh?.()
        }, 50)
      },
      instructionsPullToRefresh: '下拉刷新',
      instructionsReleaseToRefresh: '释放刷新',
      instructionsRefreshing: LoadingSVG,
      shouldPullToRefresh: () => !noRefresh && listRef?.current?.field?.scrollTop === 0,
      triggerElement: '#list-view'
    })
    return () => {
      PullToRefresh.destroyAll()
    }
  }, [onRefresh])
  const _itemData = (getGlobalData<'systemInfo'>('systemInfo').windowWidth * itemSize) / 750
  return (
    <div style={style as React.CSSProperties} className={rest.className}>
      <div id='list-view'>
        <VirtualList
          ref={listRef}
          height={height || getNumberSize((style as any)?.height)}
          itemSize={_itemData}
          width={width}
          itemData={itemData}
          itemCount={itemData.length}
          refresherEnabled
          refresherTriggered={_refreshState === 'Refresh'}
          scrollWithAnimation
          renderBottom={renderBottom()}
          onScrollToUpper={async () => {
            setTimeout(() => {
              setRefreshState('Default')
            },0)
          }}
          onScrollToLower={async () => {
            if (noPull) return
            if (_refreshState === 'Default') {
              setRefreshState('LoadMore')
              const canLoadMore = await onLoadMore?.()
              setRefreshState(canLoadMore ? 'Default' : 'NoMoreData')
            }
          }}
          {...(rest as any)}
        >
          {memo(({ index, data, id }: any) => {
            const item = data[index]
            return renderItem(item, index, id)
          })}
        </VirtualList>
      </div>
    </div>
  )
}

export default ListView
