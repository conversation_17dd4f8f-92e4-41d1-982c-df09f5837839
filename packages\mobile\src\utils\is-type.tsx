/*
 * @Author: your name
 * @Date: 2021-08-11 17:05:34
 * @LastEditTime: 2021-08-24 15:13:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \wehr_web2\packages\mobile\src\utils\is-type.tsx
 */
const pattern = {
  // http://emailregex.com/
  email:
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  url: new RegExp(
    '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$',
    'i'
  ),
  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i,
  phoneNumber: /^1[3-9]\d{9}$/,
  idCard:
    /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/,
}

export const isNumber = (value: any) => {
  return typeof value === 'number'
}

export const isRegExp = (value: any) => {
  return Object.prototype.toString.call(value) === '[object RegExp]'
}

export const isObject = (value: any) => {
  return Object.prototype.toString.call(value) === '[object Object]'
}

export const isError = (value: any) => {
  return Object.prototype.toString.call(value) === '[object Error]'
}

export const isString = (value: any) => {
  return typeof value === 'string'
}

export const isFunction = (value: any) => {
  return typeof value === 'function'
}

export const isArray = (value: any) => {
  return Object.prototype.toString.call(value) === '[object Array]'
}

export const isEmptyObject = (obj: any) => {
  if (obj === null || obj === undefined) return 'Cannot convert undefined or null to object'
  return Object.keys(obj).length === 0
}

export const isBoolean = (value: any) => {
  return (
    value === true ||
    value === false ||
    (isObject(value) && Object.prototype.toString.call(value) === '[object Boolean]')
  )
}

export const isInteger = (value: any) => {
  return isNumber(value) && parseInt(value, 10) === value
}
export const isFloat = (value: any) => {
  return isNumber(value) && !isInteger(value)
}

export const isDate = (value: any) => {
  if (!value) return false
  return (
    typeof value.getTime === 'function' && typeof value.getMonth === 'function' && typeof value.getYear === 'function'
  )
}

export const isEmail = (value: any) => {
  return typeof value === 'string' && !!value.match(pattern.email) && value.length < 255
}

export const isUrl = (value: any) => {
  return typeof value === 'string' && !!value.match(pattern.url)
}

export const isHex = (value: any) => {
  return typeof value === 'string' && !!value.match(pattern.hex)
}

export const isPromise = (value: any) => {
  return value && typeof value.then === 'function'
}

export const isPhoneNumber = (value: any) => {
  return typeof value === 'string' && !!value.match(pattern.phoneNumber)
}

export const isIdCard = (value: any) => {
  return typeof value === 'string' && !!value.match(pattern.idCard)
}
