/**
 * @description 所属材料列表接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openId */
  openId?: string;
  /** subTypeId */
  subTypeId?: string;
}

export type Result = defs.pact.MaterBean;
export const path = '/yc-wepact-mobile/appoint/getMaterialList';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
