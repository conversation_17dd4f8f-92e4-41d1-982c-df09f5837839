export const camelCase = (value: string) =>
  value.replace(/-([a-z])/g, (g: string[]) => g[1].toUpperCase());

export const camelCaseNodeName = ({ nodeName, nodeValue }) => ({
  nodeName: camelCase(nodeName),
  nodeValue
});

export const removePixelsFromNodeValue = ({ nodeName, nodeValue }) => ({
  nodeName,
  nodeValue: nodeValue.replace("px", "")
});

export const transformStyle = ({ nodeName, nodeValue, fillProp }) => {
  if (nodeName === "style") {
    return nodeValue.split(";").reduce((acc: any, attribute: { split: (arg0: string) => [any, any]; }) => {
      const [property, value] = attribute.split(":");
      if (property == "") return acc;
      else
        return {
          ...acc,
          [camelCase(property)]:
            fillProp && property === "fill" ? fillProp : value
        };
    }, {});
  }
  return null;
};

export const getEnabledAttributes = (enabledAttributes: string | any[]) => ({ nodeName }) =>
  enabledAttributes.includes(camelCase(nodeName));
