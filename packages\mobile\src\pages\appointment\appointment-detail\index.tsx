/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-16 15:43:09
 * @LastAuthor: 王正荣
 * @LastTime: 2021-10-08 14:00:47
 * @message:
 */
import { View, ScrollView } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { encryCertificateNumber, encryPhoneNumber, getGlobalData, getScrollStyle } from '@utils'
import { pact } from '@apis/pact'
import { BottomBtn, withPage, Labels } from '@components'
import Taro, { useRouter } from '@tarojs/taro'
// import styles from './index.module.scss'

const bookingStatusMap = new Map<string, string>([
  ['0', '预约中'],
  ['1', '预约成功'],
  ['2', '预约失败'],
  ['3', '过期'],
  ['4', '办理完成']
])

const Index = () => {
  const { openId } = getGlobalData<'account'>('account')
  const { bookingId } = useRouter<{ bookingId: string }>().params
  const [detail, setDetail] = useState<defs.pact.AppointmentDetailData>()
  const scrollStyle = getScrollStyle({ bottom: 120 })

  useEffect(() => {
    pact.appoint.getAppointmentDetail
      .request({
        bookingId,
        openId
      })
      .then(res => {
        setDetail(res.data)
      })
  }, [bookingId, openId])
  return (
    <View>
      <ScrollView style={scrollStyle} scrollY>
        <View>
          <Labels title='预约分公司' detail={detail?.departmentName} />
          <Labels title='分公司电话' detail={detail?.contactTel} />
          <Labels title='所属类型' detail={detail?.categoryName} />
          <Labels title='业务大类' detail={detail?.busTypeName} />
          <Labels title='业务小类' detail={detail?.busSubTypeName} />
          <Labels title='预约人' detail={detail?.empName} />
          <Labels title='身份证号码' detail={encryCertificateNumber(detail?.idCardNum)} />
          <Labels title='手机号码' detail={encryPhoneNumber(detail?.mobilePhoneNum)} />
          <Labels title='提交预约时间' detail={detail?.createDt} />
          <Labels title='预约时间' detail={detail?.bookingDt} />
          <Labels title='办理状态' detail={detail?.bookingStatus && bookingStatusMap.get(detail?.bookingStatus)} />
          { detail?.bookingStatus=='2' && <Labels title='取消原因' detail={detail?.cancelReason} /> }
          { detail?.bookingStatus=='2' && <Labels title='取消时间' detail={detail?.updateDt} /> }
          <Labels title='预约备注' detail={detail?.bookingRemark} />
        </View>
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '取消预约',
            hide: !(detail?.bookingStatus === '0'),
            onClick: () => {
              Taro.navigateTo({ url: `/pages/appointment/appointment-cancel/index?bookingId=${bookingId}&source=2` })
            }
          },
          {
            title: '返回列表',
            onClick: () => {
              Taro.navigateBack({ delta: 1 })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
