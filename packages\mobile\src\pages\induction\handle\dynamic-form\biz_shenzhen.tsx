import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleShenZhenColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
    const hasSSinShenzhen = form.watch('hasSSinShenzhen_shenzhen');
    const hasPFinShenzhen = form.watch('hasPFinShenzhen_shenzhen');
    if (column.name === 'hasSSinShenzhen_shenzhen'){
        form.register('hasSSinShenzhen_shenzhen', {value: column.defaultValue})
    }
    if (column.name === 'hasPFinShenzhen_shenzhen'){
        form.register('hasPFinShenzhen_shenzhen', {value: column.defaultValue})
    }
    if (column.name === 'ssPCNo_shenzhen'){
      return {...column, isHidden: ['0', '否',''].includes(hasSSinShenzhen)}
    }
    if (column.name === 'pfNo_shenzhen'){
        return {...column, isHidden: ['0', '否',''].includes(hasPFinShenzhen)}
    }
    if (column.name === 'bankcardType_shenzhen'){
        return {...column, isHidden: ['1', '是',''].includes(hasPFinShenzhen)}
    }
    if (column.name === 'bankcardNo_shenzhen'){ 
        return {...column, isHidden: ['1', '是',''].includes(hasPFinShenzhen)}
    }
    return column;
}

export { handleShenZhenColumn }