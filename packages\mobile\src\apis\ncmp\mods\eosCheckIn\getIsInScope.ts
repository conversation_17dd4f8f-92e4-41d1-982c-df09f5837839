/**
 * @description 判断是否在打卡范围内
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 公司id */
  custId: number;
  /** 纬度 */
  latitude: string;
  /** 经度 */
  longitude: string;
}

export type Result = defs.ncmp.HttpResult<number>;
export const path = '/wx-ncmp/eos/attendance/checkin/getIsInScope';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
