/**
 *  需要根据城市特殊处理的字段
 *  北京 10740
 *
 *  天津 10743
 *
 *  苏州
 *
 *  厦门
 *
 */

import { useMemo } from 'react'
import { View, Button } from '@tarojs/components'
import dayjs from 'dayjs'
import isEmpty from 'lodash/isEmpty'
import { idCardReg, mobileReg, emailReg } from '@utils'
import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'
import { handleBeiJingColumn } from './biz_beijing'
import { handleTianJingColumn } from './biz_tianjing'
import { handleNanJingColumn } from './biz_nanjing'
import { handleChangChunColumn } from './biz_changchun'
import { handleJiLinColumn } from './biz_jilin'
import { handleShangHaiColumn } from './biz_shanghai'
import { handleSuZhouColumn } from './biz_suzhou'
import { handleBinZhouColumn } from './biz_binzhou'
import styles from './index.module.scss'
import { handleBaoTouColumn } from './biz_baotou'
import { handleQingDaoColumn } from './biz_qingdao'
import { handleKunMingColumn } from './biz_kunming'
import { handleLongYanColumn } from './biz_longyan'
import { handleZhangZhouColumn } from './biz_zhangzhou'
import { handleDongYingColumn } from './biz_dongying'
import { handleSuQianColumn } from './biz_suqian'
import { handleTaiAnColumn } from './biz_taian'
import { handleShenZhenColumn } from './biz_shenzhen'
import { handleXIANColumn } from './biz_xian'
import { handleTaiYuanColumn } from './biz_taiyuan'

export interface FormItemType {
  name: string
  code: string
  titleLevel: 1 | 2 | 3
  requireFlag: boolean
  operation: string | 'textarea' | 'text'
  optionList: []
  restrictValue: string // 最多长度
  defaultValue: string
  remind: string // 提示
  orderValue: number
  subList: FormItemType[]
  itemUUID: string
  parentItemUUID: string
  businessId: string
  itemValue: string
  fields: 'year' | 'month' | 'day'
}
const handleColumn = (column: FormItemProps, cityCode: string, form: UseFormReturn, page: string) => {
  const options = [
    { key: '是', value: '是' },
    { key: '否', value: '否' }
  ]
  const { setValue, getValues, clearErrors } = form
  if (column.title === '户口所在地' && !column.disabled) {
    const renderTitleRight = () => {
      return (
        <View>
          <Button
            className={styles.button}
            onClick={() => {
              const values = getValues()
              setValue('hukouProvinceCity', values.residenceProvinceCity)
              setValue('hukouAddress', values.residentAddress)
              setValue('hukouRegion', values.region)
              setValue('hukouSubDistrict', values.subDistrict)
              setValue('hukouAddress', values.residentAddress)
              clearErrors('hukouProvinceCity')
              clearErrors('hukouAddress')
              clearErrors('hukouRegion')
              clearErrors('hukouSubDistrict')
              clearErrors('hukouAddress')
            }}
          >
            同现居住地址
          </Button>
        </View>
      )
    }
    column.renderTitleRight = renderTitleRight
  }
  switch (cityCode) {
    case '10740':
      return handleBeiJingColumn(column, form, page, cityCode)
    case '10743':
      return handleTianJingColumn(column, form, page)
    case '10746':
      return handleNanJingColumn(column, form, page)
    case '10802':
      return handleChangChunColumn(column, form, page)
    case '10803':
      if (column.name === 'isEnterFund_jilin') {
        column.options = options
      }
      return handleJiLinColumn(column, form, page)
    case '10742':
      return handleShangHaiColumn(column, form, page)
    case '10836':
      return handleSuZhouColumn(column, form, page)
    case '10856':
      return handleBinZhouColumn(column, form, page)
    case '10824':
      return handleBaoTouColumn(column, form, page)
    case '10848':
      return handleQingDaoColumn(column, form, page)
    case '10996':
      return handleKunMingColumn(column, form, page)
    case '10889':
      if (['workExp_zhangzhou', 'isEnterFund_zhangzhou'].includes(column.name!)) {
        column.options = options
      }
      return handleZhangZhouColumn(column, form, page)
    case '10890':
      if (column.name === 'isEnterFund_longyan'){
        column.options = options
      }
      return handleLongYanColumn(column, form, page)
    case '10763':
      if (column.name === 'graduatingStd_taiyuan'){
        column.options = options 
      }
      return handleTaiYuanColumn(column, form, page)
    case '10857':
      if (column.name === 'isFullTime_dongying') {
        column.options = options
      }
      return handleDongYingColumn(column, form, page)
    case '10845':
      if (column.name === 'payedIn_suqian') {
        column.options = options
      }
      return handleSuQianColumn(column, form, page)
    case '10854':
      if (column.name === 'payedIn_taian') {
        column.options = options
      }
      return handleTaiAnColumn(column, form, page)
    case '10937':
      if (column.name === 'newHukouType_shenzhen') {
        column.title = '户籍性质'
      }
      if (column.name === 'hukouType') {
        column = {}
      }
      if (['hasSSinShenzhen_shenzhen', 'hasPFinShenzhen_shenzhen'].includes(column.name!)) {
        column.options = options
      }
      return handleShenZhenColumn(column, form, page)
    case '10749':
      if (['shaanxiPersonMediIns_xian', 'otherProvMediIns_xian', 'anyCityStaffMediIns_xian', 'shanxiProvinceHasPaidPensionInsurance_xian'].includes(column.name!)) {
        column.options = options
      }
      if (column.title === '紧急联系方式及婚姻状况') {
        column.title = '紧急联系方式'
      }
      return handleXIANColumn(column, form, page)
    case '10764':
    case '10768':
    case '10771':
    case '10769':
    case '10767':
    case '10761':
    case '900000460':
    case '10762':
    case '10765':
    case '900000740':
      if (column.name === 'graduationlatest_shanxi') {
        column.options = options
      }
      return column
    default:
      return column
  }
}
let columns: FormItemProps[] | any = []
const handleColumns = (
  _items: FormItemType[],
  cityCode: string,
  form: UseFormReturn,
  page: string,
  isChild: boolean = false
) => {
  if (!isChild) {
    columns = []
  }
  const removeCode = ['post_code', 'hukouZipCode']
  _items.length > 0 &&
    _items
      .filter(
        item =>
          !removeCode.includes(item.operation) &&
          item.code !== 'enterinsurance_beijing' && item.code !== 'unisocialcode_beijing'
      )
      .map(section => {
        const optionList = section.optionList.map(item => ({
          key: Object.keys(item)[0],
          value: Object.values(item)[0]
        }))
        let reg: any
        let validateIDDate: any
        switch (section.code) {
          case 'idCardNum':
            reg = {
              value: idCardReg,
              message: '证件号码不正确'
            }
            break
          case 'mobilePhoneNum':
            reg = {
              value: mobileReg,
              message: '必须是11位手机号码'
            }
            break
          case 'emergencyPhone':
            reg = {
              value: mobileReg,
              message: '必须是11位手机号码'
            }
            break
          case 'email':
            reg = {
              value: emailReg,
              message: '邮箱格式不正确'
            }
            break
          case 'emergencyAge_handan':
            reg = {
              value: /^[0-9]*$/,
              message: '年龄必须是数字'
            }
            break
          case 'validStart':
            validateIDDate = {
              validStartFun: v => {
                const validEnd = form.getValues('validEnd')
                if (validEnd === '长期') return
                if (validEnd) {
                  const startD = dayjs(v).valueOf()
                  const endD = dayjs(validEnd).valueOf()
                  return startD <= endD || '有效期开始时间不能大于于有效期截止时间'
                }
              }
            }
            break
          case 'validEnd':
            validateIDDate = {
              validEndFun: v => {
                const validStart = form.getValues('validStart')
                if (validStart) {
                  const currentDay = dayjs().format('YYYY-MM-DD')
                  const day = dayjs(currentDay).valueOf()
                  const startD = dayjs(validStart).valueOf()
                  const endD = dayjs(v).valueOf()
                  if (v === '长期') return
                  if (endD < day) {
                    return '身份证已过期，请提交有效身份证'
                  }
                  return endD >= startD || '有效期截止时间不能小于有效期开始时间'
                }
              }
            }
            break
          case 'workBeginTime_zhangzhou':
            if (section.operation === 'date'){
              section.restrictValue = 'month'
            }
            validateIDDate = {
              validEndFun: v => {
                const workEndTime = form.getValues('workEndTime_zhangzhou')
                if (dayjs(v).valueOf() > dayjs(workEndTime).valueOf()){
                  return '入职开始时间不能大于入职结束时间'
                }
              }
            }
            break
          case 'workEndTime_zhangzhou':
            if (section.operation === 'date'){
              section.restrictValue = 'month'
            }
            validateIDDate = {
              validEndFun: v => {
                const workBeginTime = form.getValues('workBeginTime_zhangzhou')
                if (dayjs(v).valueOf() < dayjs(workBeginTime).valueOf()){
                  return '入职结束时间不能小于入职开始开始时间'
                }
              }
            }
            break
          default:
            validateIDDate = {}
            break
        }
        const column = {
          options: optionList,
          type: section.titleLevel === 1 ? '' : (section.operation as any),
          rules: { required: section.requireFlag ? '请填写必填项' : false, pattern: reg, validate: validateIDDate },
          name: section.code,
          level: section.titleLevel,
          title: section.name,
          fields: section.restrictValue === 'month' ?  'month' : 'day',
          remind: section.remind,
          defaultValue: section.itemValue
        }
        columns.push(handleColumn(column, cityCode!, form, page))
        !isEmpty(section.subList) && handleColumns(section.subList, cityCode!, form, page, true)
      })
  return columns
}
const getColumns = (
  items,
  otherItems,
  cityCode,
  form,
  renderFileItem = () => null,
  regionData,
  subDistrictData,
  regionData2,
  subDistrictData2
) => {
  const isBeijingPaid = form.watch('isBeijingPaid')
  const columns1 = handleColumns(items, cityCode, form, '1')
  const columns2 = handleColumns(otherItems, cityCode, form, '2')
  const regionInfo = {
    title: '区',
    name: 'region',
    level: 3,
    type: 'select',
    rules: { required: true },
    options: regionData,
    disabled: !regionData.length
  }
  const regionInfo1 = {
    title: '街道',
    name: 'subDistrict',
    level: 3,
    type: 'select',
    rules: { required: true },
    options: subDistrictData,
    disabled: !subDistrictData.length
  }
  const hukouRegionInfo = {
    title: '区',
    name: 'hukouRegion',
    level: 3,
    type: 'select',
    rules: { required: true },
    options: regionData2,
    disabled: !regionData2.length
  }
  const hukouRegionInfo1 = {
    title: '街道',
    name: 'hukouSubDistrict',
    level: 3,
    type: 'select',
    rules: { required: true },
    options: subDistrictData2,
    disabled: !subDistrictData2.length
  }
  const idx = columns1.findIndex((i: { name: string }) => i.name === 'residenceProvinceCity')
  const idx1 = columns1.findIndex((i: { name: string }) => i.name === 'hukouProvinceCity')
  idx !== -1 && columns1.splice(idx + 1, 0, regionInfo, regionInfo1)
  idx1 !== -1 && columns1.splice(idx1 + 3, 0, hukouRegionInfo, hukouRegionInfo1)
  if (cityCode === '10740' && isBeijingPaid == '0') {
    const index = columns2.findIndex((i: { name: string }) => i.name === 'residenceProvinceCity')
    const index1 = columns2.findIndex((i: { name: string }) => i.name === 'hukouProvinceCity')
    index !== -1 && columns2.splice(index + 1, 0, regionInfo, regionInfo1)
    index1 !== -1 && columns2.splice(index1 + 3, 0, hukouRegionInfo, hukouRegionInfo1)
  }

  columns.push({ render: () => renderFileItem() })
  // eslint-disable-next-line react-hooks/rules-of-hooks
  return useMemo(() => {
    if (cityCode === '10743') {
      const index = columns1.findIndex((i: { name: string }) => i.name === 'tianjinUnemploymentCardNo')
      columns1.splice(index, 0, ...columns2)
      return columns1
    }
    const i = columns1.findIndex((ii: { name: string }) => ii.name === 'idCardNum')
    const i1 = columns1.findIndex((ii: { name: string }) => ii.name === 'validStart')
    const i2 = columns1.findIndex((ii: { name: string }) => ii.name === 'validEnd')
    const item1 = columns1.find((ii: { name: string }) => ii.name === 'validStart')
    const item2 = columns1.find((ii: { name: string }) => ii.name === 'validEnd')
    columns1.splice(i + 1, 0, { ...item1 })
    columns1.splice(i + 2, 0, { ...item2 })
    columns1.splice(i1 + 2, 1)
    columns1.splice(i2 + 1, 1)
    return [...columns1, ...columns2]
  }, [cityCode, columns1, columns2])
}

export { handleColumn, handleColumns, getColumns }
