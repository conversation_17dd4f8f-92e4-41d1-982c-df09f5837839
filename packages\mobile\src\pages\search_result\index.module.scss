.wrap{
  // height: 100vh;
  // background-color: rgb(230, 230, 238);
}
.templateScope, .serviceTypeName{
  display: flex;
  align-items: center;
  flex-direction: row;
  padding: 20px, 0px;
}
.templateInfoName{
  padding: 20px 10px;
  margin: 12px 30px;
}
.policyTemplateGroup{
  border-bottom: 1px solid #d9d9d9;
  padding: 20px 40px;
  display: flex;
  align-items: center;
  flex-direction: row;
}
.policyTemplateIcon{
  width: 15px;
  height: 40px;
  background-color: #b51e25;
  margin-right: 10px;
}
.item{
  display: flex;
  align-items: center;
  justify-content:flex-start;
  flex-direction: row;
  padding: 20px 10px;
  margin: 12px 30px;
  border-bottom: 1px solid #d9d9d9;
  flex-wrap: wrap;
  width: 90vw;
  // background-color: #fff;
}
.fieldName{
  width: 25vw;
  font-size: 24px;
  padding-right: 10px;
}
.fieldValue{
  width: 60vw;
  font-size: 24px;
  word-break:break-all;
  display: flex;
  justify-content: right;
}
.font_size{
  font-size: 24px;
}
.fieldUrlName{
 
  display: flex;
  flex-direction: column;

}
.urlName{
  width: 60vw;
  font-size: 24px;
  word-break:break-all;
  display: flex;
  justify-content: right;
}