type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};

declare namespace defs {
  export namespace users {
    export class Bankcard {
      /** bankurl */
      bankurl?: string;

      /** id */
      id?: number;

      /** remake */
      remake?: string;
    }

    export class BankcardInfo {
      /** code */
      code?: string;

      /** message */
      message?: string;
    }

    export class BaseData {
      /** key */
      key?: string;

      /** value */
      value?: string;
    }

    export class CertificationInfo {
      /** certificationId */
      certificationId?: number;

      /** certificationName */
      certificationName?: string;

      /** certificationType */
      certificationType?: string;

      /** isDel */
      isDel?: boolean;
    }

    export class ChildrenEducation {
      /** childrenBirthDate */
      childrenBirthDate?: string;

      /** childrenCardNumber */
      childrenCardNumber?: string;

      /** childrenCardType */
      childrenCardType?: number;

      /** childrenId */
      childrenId?: number;

      /** childrenName */
      childrenName?: string;

      /** childrenProportion */
      childrenProportion?: number;

      /** childrenRelation */
      childrenRelation?: boolean;

      /** countryId */
      countryId?: number;

      /** createDate */
      createDate?: string;

      /** deductionMonth */
      deductionMonth?: string;

      /** deductionType */
      deductionType?: number;

      /** eduCountry */
      eduCountry?: string;

      /** eduEndDate */
      eduEndDate?: string;

      /** eduPhase */
      eduPhase?: number;

      /** eduSchoolName */
      eduSchoolName?: string;

      /** eduStartDate */
      eduStartDate?: string;

      /** eduStopDate */
      eduStopDate?: string;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** updateDate */
      updateDate?: string;
    }

    export class CityInfo {
      /** cityId */
      cityId?: number;

      /** cityLevel */
      cityLevel?: string;

      /** cityName */
      cityName?: string;

      /** countryId */
      countryId?: number;

      /** isDel */
      isDel?: boolean;

      /** phonetic */
      phonetic?: string;
    }

    export class ContinuingEduInfo {
      /** certificationAuthority */
      certificationAuthority?: string;

      /** certificationDate */
      certificationDate?: string;

      /** certificationName */
      certificationName?: string;

      /** certificationNumber */
      certificationNumber?: string;

      /** continuingEduType */
      continuingEduType?: number;

      /** createDate */
      createDate?: string;

      /** deductionAmount */
      deductionAmount?: number;

      /** deductionMonth */
      deductionMonth?: string;

      /** deductionType */
      deductionType?: number;

      /** eduEndDate */
      eduEndDate?: string;

      /** eduId */
      eduId?: number;

      /** eduPhase */
      eduPhase?: number;

      /** eduStartDate */
      eduStartDate?: string;

      /** eduType */
      eduType?: number;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** updateDate */
      updateDate?: string;
    }

    export class CountryInfo {
      /** countryId */
      countryId?: number;

      /** countryName */
      countryName?: string;

      /** isDel */
      isDel?: boolean;
    }

    export class EmpSalary {
      /** classId */
      classId?: string;

      /** custName */
      custName?: string;

      /** empId */
      empId?: string;

      /** f1 */
      f1?: string;

      /** f10 */
      f10?: string;

      /** f2 */
      f2?: string;

      /** f3 */
      f3?: string;

      /** pages */
      pages?: string;

      /** sendId */
      sendId?: string;

      /** sendMonth */
      sendMonth?: string;

      /** wageTaxStatus */
      wageTaxStatus?: string;
    }

    export class EndowmentInsuranceInfo {
      /** checkCode */
      checkCode?: string;

      /** createDate */
      createDate?: string;

      /** deductionMonth */
      deductionMonth?: string;

      /** distinguishNum */
      distinguishNum?: string;

      /** employeeId */
      employeeId?: string;

      /** endDate */
      endDate?: string;

      /** flag */
      flag?: boolean;

      /** insuranceId */
      insuranceId?: number;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** monthAmount */
      monthAmount?: number;

      /** preSalary */
      preSalary?: number;

      /** updateDate */
      updateDate?: string;

      /** yearAmount */
      yearAmount?: number;
    }

    export class EssentialInfo {
      /** annualDeduction */
      annualDeduction?: string;

      /** countryId */
      countryId?: number;

      /** createDate */
      createDate?: string;

      /** custId */
      custId?: string;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** hasSpouse */
      hasSpouse?: number;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** spouseCardNumber */
      spouseCardNumber?: string;

      /** spouseCardType */
      spouseCardType?: number;

      /** spouseName */
      spouseName?: string;

      /** taxpayerAddress */
      taxpayerAddress?: string;

      /** taxpayerCardNumber */
      taxpayerCardNumber?: string;

      /** taxpayerCardType */
      taxpayerCardType?: number;

      /** taxpayerEmail */
      taxpayerEmail?: string;

      /** taxpayerId */
      taxpayerId?: number;

      /** taxpayerName */
      taxpayerName?: string;

      /** taxpayerNumber */
      taxpayerNumber?: string;

      /** taxpayerPhone */
      taxpayerPhone?: string;

      /** updateDate */
      updateDate?: string;
    }

    export class EventFlag {
      /** childFlag */
      childFlag?: boolean;

      /** continueFlag */
      continueFlag?: boolean;

      /** essentialFlag */
      essentialFlag?: boolean;

      /** healthFlag */
      healthFlag?: boolean;

      /** houseLoanFlag */
      houseLoanFlag?: boolean;

      /** houseRentalFlag */
      houseRentalFlag?: boolean;

      /** pensionFlag */
      pensionFlag?: boolean;

      /** supportFlag */
      supportFlag?: boolean;
    }

    export class EventInfo {
      /** deductionAmount */
      deductionAmount?: string;

      /** endDate */
      endDate?: string;

      /** eventDescribe */
      eventDescribe?: string;

      /** eventId */
      eventId?: number;

      /** eventName */
      eventName?: string;

      /** eventQuota */
      eventQuota?: string;

      /** isDel */
      isDel?: boolean;

      /** startDate */
      startDate?: string;
    }

    export class ExecuteResult<T0 = any> {
      /** code */
      code?: number;

      /** data */
      data?: T0;

      /** message */
      message?: string;
    }

    export class FeedBack {
      /** content */
      content?: string;

      /** title */
      title?: string;
    }

    export class GetBankcardInfo {
      /** accountEmployeeName */
      accountEmployeeName?: string;

      /** bankAcct */
      bankAcct?: string;

      /** bankId */
      bankId?: string;

      /** bankName */
      bankName?: string;

      /** cityId */
      cityId?: string;

      /** cityName */
      cityName?: string;

      /** empBankCardId */
      empBankCardId?: string;

      /** empId */
      empId?: string;

      /** openAddress */
      openAddress?: string;

      /** openBankName */
      openBankName?: string;

      /** provinceId */
      provinceId?: string;

      /** provinceName */
      provinceName?: string;
    }

    export class HealthInsuranceInfo {
      /** createDate */
      createDate?: string;

      /** deductedAmount */
      deductedAmount?: number;

      /** deductionMonth */
      deductionMonth?: string;

      /** distinguishNum */
      distinguishNum?: string;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** insuranceId */
      insuranceId?: number;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** monthAmount */
      monthAmount?: number;

      /** startDate */
      startDate?: string;

      /** updateDate */
      updateDate?: string;

      /** yearAmount */
      yearAmount?: number;
    }

    export class HouseBasicInfo {
      /** id */
      id?: number;

      /** type */
      type?: number;

      /** updateDate */
      updateDate?: string;
    }

    export class HouseLoanInfo {
      /** createDate */
      createDate?: string;

      /** deductionAmount */
      deductionAmount?: number;

      /** deductionMonth */
      deductionMonth?: string;

      /** deductionType */
      deductionType?: number;

      /** employeeId */
      employeeId?: string;

      /** endRepaymentDate */
      endRepaymentDate?: string;

      /** flag */
      flag?: boolean;

      /** houseAddress */
      houseAddress?: string;

      /** houseCredentialsNum */
      houseCredentialsNum?: string;

      /** houseCredentialsType */
      houseCredentialsType?: number;

      /** houseFirstLoan */
      houseFirstLoan?: number;

      /** houseId */
      houseId?: number;

      /** houseLoanBank */
      houseLoanBank?: string;

      /** houseLoanNum */
      houseLoanNum?: string;

      /** houseLoanType */
      houseLoanType?: number;

      /** isDel */
      isDel?: boolean;

      /** isOneself */
      isOneself?: boolean;

      /** isSync */
      isSync?: boolean;

      /** loanPeriod */
      loanPeriod?: number;

      /** startRepaymentDate */
      startRepaymentDate?: string;

      /** updateDate */
      updateDate?: string;
    }

    export class HouseRentalInfo {
      /** createDate */
      createDate?: string;

      /** deductionAmount */
      deductionAmount?: number;

      /** deductionMonth */
      deductionMonth?: string;

      /** deductionType */
      deductionType?: number;

      /** employeeId */
      employeeId?: string;

      /** endLeaseDate */
      endLeaseDate?: string;

      /** flag */
      flag?: boolean;

      /** houseAddress */
      houseAddress?: string;

      /** houseId */
      houseId?: number;

      /** huoseLeaseType */
      huoseLeaseType?: number;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** lessorCardNum */
      lessorCardNum?: string;

      /** lessorCardType */
      lessorCardType?: number;

      /** lessorName */
      lessorName?: string;

      /** rentalNum */
      rentalNum?: string;

      /** startLeaseDate */
      startLeaseDate?: string;

      /** updateDate */
      updateDate?: string;

      /** workCity */
      workCity?: number;
    }

    export class Martyrs {
      /** key */
      key?: string;

      /** value */
      value?: string;
    }

    export class MedicalInfo {
      /** burdenAmount */
      burdenAmount?: number;

      /** countryId */
      countryId?: number;

      /** createDate */
      createDate?: string;

      /** deductionMonth */
      deductionMonth?: string;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** patientCardNumber */
      patientCardNumber?: string;

      /** patientCardType */
      patientCardType?: number;

      /** patientId */
      patientId?: number;

      /** patientName */
      patientName?: string;

      /** patientType */
      patientType?: number;

      /** totalAmount */
      totalAmount?: number;

      /** updateDate */
      updateDate?: string;
    }

    export class MoreChildrenEducation {
      /** childrenBirthDate */
      childrenBirthDate?: string;

      /** childrenCardNumber */
      childrenCardNumber?: string;

      /** childrenCardType */
      childrenCardType?: number;

      /** childrenId */
      childrenId?: number;

      /** childrenName */
      childrenName?: string;

      /** childrenProportion */
      childrenProportion?: number;

      /** childrenRelation */
      childrenRelation?: boolean;

      /** countryId */
      countryId?: number;

      /** createDate */
      createDate?: string;

      /** deductionMonth */
      deductionMonth?: string;

      /** deductionType */
      deductionType?: number;

      /** eduCountry */
      eduCountry?: string;

      /** eduEndDate */
      eduEndDate?: string;

      /** eduPhase */
      eduPhase?: number;

      /** eduSchoolName */
      eduSchoolName?: string;

      /** eduStartDate */
      eduStartDate?: string;

      /** eduStopDate */
      eduStopDate?: string;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** updateDate */
      updateDate?: string;

      /** updateTime */
      updateTime?: string;
    }

    export class MoreContinuingEduInfo {
      /** certificationAuthority */
      certificationAuthority?: string;

      /** certificationDate */
      certificationDate?: string;

      /** certificationName */
      certificationName?: string;

      /** certificationNumber */
      certificationNumber?: string;

      /** continuingEduType */
      continuingEduType?: number;

      /** createDate */
      createDate?: string;

      /** deductionAmount */
      deductionAmount?: number;

      /** deductionMonth */
      deductionMonth?: string;

      /** deductionType */
      deductionType?: number;

      /** eduEndDate */
      eduEndDate?: string;

      /** eduId */
      eduId?: number;

      /** eduPhase */
      eduPhase?: number;

      /** eduStartDate */
      eduStartDate?: string;

      /** eduType */
      eduType?: number;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** updateDate */
      updateDate?: string;

      /** updateTime */
      updateTime?: string;
    }

    export class MoreSupportInfo {
      /** createDate */
      createDate?: string;

      /** deductionMonth */
      deductionMonth?: string;

      /** deductionType */
      deductionType?: number;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** isDel */
      isDel?: boolean;

      /** isOnlyChild */
      isOnlyChild?: boolean;

      /** isSync */
      isSync?: boolean;

      /** monthDeduction */
      monthDeduction?: number;

      /** shareType */
      shareType?: number;

      /** supportBirthDate */
      supportBirthDate?: string;

      /** supportCardNum */
      supportCardNum?: string;

      /** supportCardType */
      supportCardType?: number;

      /** supportCountryId */
      supportCountryId?: number;

      /** supportId */
      supportId?: number;

      /** supportName */
      supportName?: string;

      /** supportRelation */
      supportRelation?: number;

      /** tSupportInfoList */
      tSupportInfoList?: Array<defs.users.TSupportInfo>;

      /** updateDate */
      updateDate?: string;
    }

    export class Preferential {
      /** certificate */
      certificate?: string;

      /** cmpToken */
      cmpToken?: string;

      /** code */
      code?: string;

      /** creatTime */
      creatTime?: string;

      /** empId */
      empId?: string;

      /** img */
      img?: string;

      /** martyrId */
      martyrId?: string;

      /** martyrvlaue */
      martyrvlaue?: string;
    }

    export class PsnCust {
      /** classId */
      classId?: string;

      /** custName */
      custName?: string;

      /** dataId */
      dataId?: string;

      /** empId */
      empId?: string;

      /** f18 */
      f18?: string;

      /** f33 */
      f33?: string;

      /** f9 */
      f9?: string;

      /** payAddress */
      payAddress?: string;

      /** sendId */
      sendId?: string;

      /** taxMonth */
      taxMonth?: string;

      /** taxPayerType */
      taxPayerType?: string;
    }

    export class PsnCustTotal {
      /** f18 */
      f18?: string;

      /** f33 */
      f33?: string;

      /** f9 */
      f9?: string;

      /** pages */
      pages?: string;
    }

    export class PsnDetailParams {
      /** cmpToken */
      cmpToken?: string;

      /** dataId */
      dataId?: string;

      /** empId */
      empId?: string;
    }

    export class ReceiveBankcardInfo {
      /** accountEmployeeName */
      accountEmployeeName?: string;

      /** bankAcct */
      bankAcct?: string;

      /** bankId */
      bankId?: string;

      /** cityId */
      cityId?: string;

      /** cmpToken */
      cmpToken?: string;

      /** empId */
      empId?: string;

      /** openAddress */
      openAddress?: string;

      /** openBankName */
      openBankName?: string;

      /** provinceId */
      provinceId?: string;
    }

    export class ReceiveBaseData {
      /** pageNum */
      pageNum?: string;

      /** typeId */
      typeId?: string;
    }

    export class ReceiveEmp {
      /** cmpToken */
      cmpToken?: string;

      /** empId */
      empId?: string;

      /** sendMonth */
      sendMonth?: string;
    }

    export class ReceiveParams {
      /** cmpToken */
      cmpToken?: string;

      /** empId */
      empId?: string;

      /** endMonth */
      endMonth?: string;

      /** id */
      id?: number;

      /** pageNum */
      pageNum?: string;

      /** startMonth */
      startMonth?: string;
    }

    export class RequestPsnDetail {
      /** empName */
      empName?: string;

      /** f12 */
      f12?: string;

      /** f13 */
      f13?: string;

      /** f16 */
      f16?: string;

      /** f17 */
      f17?: string;

      /** f18 */
      f18?: string;

      /** f19 */
      f19?: string;

      /** f20 */
      f20?: string;

      /** f21 */
      f21?: string;

      /** f25 */
      f25?: string;

      /** f33 */
      f33?: string;

      /** f34 */
      f34?: string;

      /** f35 */
      f35?: string;

      /** f36 */
      f36?: string;

      /** f37 */
      f37?: string;

      /** f38 */
      f38?: string;

      /** f44 */
      f44?: string;

      /** f55 */
      f55?: string;

      /** f56 */
      f56?: string;

      /** f57 */
      f57?: string;

      /** f58 */
      f58?: string;

      /** f63 */
      f63?: string;

      /** f67 */
      f67?: string;

      /** f68 */
      f68?: string;

      /** f69 */
      f69?: string;

      /** f8261 */
      f8261?: string;

      /** f8362 */
      f8362?: string;

      /** f8564 */
      f8564?: string;

      /** f8665 */
      f8665?: string;

      /** f8766 */
      f8766?: string;

      /** f9 */
      f9?: string;

      /** f9total */
      f9total?: string;

      /** idCardNum */
      idCardNum?: string;

      /** idCardType */
      idCardType?: string;

      /** maxtax */
      maxtax?: string;

      /** mintax */
      mintax?: string;

      /** pages */
      pages?: string;

      /** taxPayerType */
      taxPayerType?: string;
    }

    export class ResponseEntity {
      /** body */
      body?: object;

      /** statusCode */
      statusCode?: any;

      /** statusCodeValue */
      statusCodeValue?: number;
    }

    export class SupportInfo {
      /** createDate */
      createDate?: string;

      /** deductionMonth */
      deductionMonth?: string;

      /** deductionType */
      deductionType?: number;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** isDel */
      isDel?: boolean;

      /** isOnlyChild */
      isOnlyChild?: boolean;

      /** isSync */
      isSync?: boolean;

      /** monthDeduction */
      monthDeduction?: number;

      /** shareType */
      shareType?: number;

      /** supportBirthDate */
      supportBirthDate?: string;

      /** supportCardNum */
      supportCardNum?: string;

      /** supportCardType */
      supportCardType?: number;

      /** supportCountryId */
      supportCountryId?: number;

      /** supportId */
      supportId?: number;

      /** supportName */
      supportName?: string;

      /** supportRelation */
      supportRelation?: number;

      /** updateDate */
      updateDate?: string;
    }

    export class TSupportInfo {
      /** createDate */
      createDate?: string;

      /** employeeId */
      employeeId?: string;

      /** flag */
      flag?: boolean;

      /** isDel */
      isDel?: boolean;

      /** isSync */
      isSync?: boolean;

      /** tSupportCardNum */
      tSupportCardNum?: string;

      /** tSupportCardType */
      tSupportCardType?: number;

      /** tSupportCountryId */
      tSupportCountryId?: number;

      /** tSupportId */
      tSupportId?: number;

      /** tSupportName */
      tSupportName?: string;

      /** updateDate */
      updateDate?: string;
    }

    export class TemplateMsg {
      /** empId */
      empId?: string;

      /** errorList */
      errorList?: defs.users.FeedBack;

      /** first */
      first?: string;

      /** idCard */
      idCard?: string;

      /** keyword1 */
      keyword1?: string;

      /** keyword2 */
      keyword2?: string;

      /** keyword3 */
      keyword3?: string;

      /** messageId */
      messageId?: string;

      /** remark */
      remark?: string;

      /** typeCode */
      typeCode?: string;
    }
  }
}

declare namespace API {
  export namespace users {
    /**
     * Bankcard Info Controller
     */
    export namespace bankcardInfo {
      /**
       * insertBankcardInfo
       * /user-server/api/insertBankcardInfo
       */
      export namespace insertBankcardInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.BankcardInfo>
        >;
        export const request: (
          data?: defs.users.ReceiveBankcardInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ReceiveBankcardInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.ReceiveBankcardInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ReceiveBankcardInfo
        >;
      }
    }

    /**
     * Base Data Controller
     */
    export namespace baseData {
      /**
       * getBaseData
       * /user-server/api/getBaseData
       */
      export namespace getBaseData {
        export class Params {}

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.BaseData>
        >;
        export const request: (
          data?: defs.users.ReceiveBaseData,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ReceiveBaseData,
          options?: Taro.request.CommonUseRequestOption<defs.users.ReceiveBaseData>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ReceiveBaseData
        >;
      }
    }

    /**
     * Emp Salary Controller
     */
    export namespace empSalary {
      /**
       * getEmpSalary
       * /user-server/api/getEmpSalary
       */
      export namespace getEmpSalary {
        export class Params {}

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.EmpSalary>
        >;
        export const request: (
          data?: defs.users.ReceiveEmp,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ReceiveEmp,
          options?: Taro.request.CommonUseRequestOption<defs.users.ReceiveEmp>,
        ) => Taro.request.CommonUseResultType<Response, defs.users.ReceiveEmp>;
      }
    }

    /**
     * Excel Controller
     */
    export namespace excel {
      /**
       * excel
       * /user-server/api/excel/all
       */
      export namespace excel {
        export class Params {}

        export type Response = any;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * excelByCardNumber
       * /user-server/api/excel/card/{number}
       */
      export namespace excelByCardNumber {
        export class Params {
          /** number */
          number: string;
        }

        export type Response = any;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * test
       * /user-server/api/excel/test
       */
      export namespace test {
        export class Params {}

        export type Response = any;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * excelByTime
       * /user-server/api/excel/time/{time}
       */
      export namespace excelByTime {
        export class Params {
          /** time */
          time: string;
        }

        export type Response = any;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Get Bankcard Info Controller
     */
    export namespace getBankcardInfo {
      /**
       * getBankcardInfo
       * /user-server/api/getBankcardInfo
       */
      export namespace getBankcardInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.GetBankcardInfo>
        >;
        export const request: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonUseRequestOption<defs.users.ReceiveParams>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ReceiveParams
        >;
      }
    }

    /**
     * Martyrs Controller
     */
    export namespace martyrs {
      /**
       * insertpreferential
       * /user-server/api/insertpreferential
       */
      export namespace insertpreferential {
        export class Params {}

        export type Response = number;
        export const request: (
          data?: defs.users.Preferential,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.Preferential,
          options?: Taro.request.CommonUseRequestOption<defs.users.Preferential>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.Preferential
        >;
      }

      /**
       * preferential
       * /user-server/api/preferential
       */
      export namespace preferential {
        export class Params {}

        export type Response = Array<defs.users.Preferential>;
        export const request: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonUseRequestOption<defs.users.ReceiveParams>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ReceiveParams
        >;
      }

      /**
       * selectBankcard
       * /user-server/api/selectBankcard
       */
      export namespace selectBankcard {
        export class Params {}

        export type Response = Array<defs.users.Bankcard>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * selectId
       * /user-server/api/selectId
       */
      export namespace selectId {
        export class Params {}

        export type Response = Array<defs.users.Preferential>;
        export const request: (
          data?: defs.users.Preferential,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.Preferential,
          options?: Taro.request.CommonUseRequestOption<defs.users.Preferential>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.Preferential
        >;
      }

      /**
       * selectpreferential
       * /user-server/api/selectpreferential
       */
      export namespace selectpreferential {
        export class Params {}

        export type Response = Array<defs.users.Preferential>;
        export const request: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonUseRequestOption<defs.users.ReceiveParams>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ReceiveParams
        >;
      }

      /**
       * taxPreeRence
       * /user-server/api/taxPreeRence
       */
      export namespace taxPreeRence {
        export class Params {}

        export type Response = Array<defs.users.Martyrs>;
        export const request: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonUseRequestOption<defs.users.ReceiveParams>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ReceiveParams
        >;
      }
    }

    /**
     * Psn Cust Controller
     */
    export namespace psnCust {
      /**
       * getPsnCust
       * /user-server/api/getPsnCust
       */
      export namespace getPsnCust {
        export class Params {}

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.PsnCust>
        >;
        export const request: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonUseRequestOption<defs.users.ReceiveParams>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ReceiveParams
        >;
      }
    }

    /**
     * Psn Cust Total Controller
     */
    export namespace psnCustTotal {
      /**
       * getPsnCustTotal
       * /user-server/api/getPsnCustTotal
       */
      export namespace getPsnCustTotal {
        export class Params {}

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.PsnCustTotal>
        >;
        export const request: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ReceiveParams,
          options?: Taro.request.CommonUseRequestOption<defs.users.ReceiveParams>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ReceiveParams
        >;
      }
    }

    /**
     * Psn Detail Controller
     */
    export namespace psnDetail {
      /**
       * getPsnDetail
       * /user-server/api/getPsnDetail
       */
      export namespace getPsnDetail {
        export class Params {}

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.RequestPsnDetail>
        >;
        export const request: (
          data?: defs.users.PsnDetailParams,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.PsnDetailParams,
          options?: Taro.request.CommonUseRequestOption<defs.users.PsnDetailParams>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.PsnDetailParams
        >;
      }
    }

    /**
     * Template Msg Controller
     */
    export namespace templateMsg {
      /**
       * detail
       * /user-server/api/template-msg/detail/{uuid}
       */
      export namespace detail {
        export class Params {
          /** uuid */
          uuid: string;
        }

        export type Response = defs.users.ResponseEntity;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * sendTemplateMsg
       * /user-server/api/template-msg/send/personalInformationApplicationFailed
       */
      export namespace sendTemplateMsg {
        export class Params {}

        export type Response = defs.users.ResponseEntity;
        export const request: (
          data?: defs.users.TemplateMsg,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.TemplateMsg,
          options?: Taro.request.CommonUseRequestOption<defs.users.TemplateMsg>,
        ) => Taro.request.CommonUseResultType<Response, defs.users.TemplateMsg>;
      }

      /**
       * sendTemplateMsg1
       * /user-server/api/template-msg/send/salaryPaymentFailed
       */
      export namespace sendTemplateMsg1 {
        export class Params {}

        export type Response = defs.users.ResponseEntity;
        export const request: (
          data?: defs.users.TemplateMsg,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.TemplateMsg,
          options?: Taro.request.CommonUseRequestOption<defs.users.TemplateMsg>,
        ) => Taro.request.CommonUseResultType<Response, defs.users.TemplateMsg>;
      }
    }

    /**
     * User Controller
     */
    export namespace user {
      /**
       * deleteChildrenEdu
       * /user-server/api/deleteChildrenEdu
       */
      export namespace deleteChildrenEdu {
        export class Params {
          /** childrenId */
          childrenId: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * deleteContinuingEdu
       * /user-server/api/deleteContinuingEdu
       */
      export namespace deleteContinuingEdu {
        export class Params {
          /** eduId */
          eduId: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * deleteEndowmentInsuranceInfo
       * /user-server/api/deleteEndowmentInsuranceInfo
       */
      export namespace deleteEndowmentInsuranceInfo {
        export class Params {
          /** insuranceId */
          insuranceId: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * deleteHealthInsuranceInfo
       * /user-server/api/deleteHealthInsuranceInfo
       */
      export namespace deleteHealthInsuranceInfo {
        export class Params {
          /** insuranceId */
          insuranceId: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * deleteHouseLoanInfo
       * /user-server/api/deleteHouseLoanInfo
       */
      export namespace deleteHouseLoanInfo {
        export class Params {
          /** houseId */
          houseId: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * deleteHouseRentalInfo
       * /user-server/api/deleteHouseRentalInfo
       */
      export namespace deleteHouseRentalInfo {
        export class Params {
          /** houseId */
          houseId: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * deleteMedicalInfo
       * /user-server/api/deleteMedicalInfo
       */
      export namespace deleteMedicalInfo {
        export class Params {
          /** patientId */
          patientId: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * deleteSupportInfo
       * /user-server/api/deleteSupportInfo
       */
      export namespace deleteSupportInfo {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * deleteTSupportInfo
       * /user-server/api/deleteTSupportInfo
       */
      export namespace deleteTSupportInfo {
        export class Params {
          /** tSupportId */
          tSupportId: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getAccountInfo
       * /user-server/api/getAccountInfo
       */
      export namespace getAccountInfo {
        export class Params {
          /** accountId */
          accountId?: string;
          /** openId */
          openId?: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.EssentialInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getCertificationInfo
       * /user-server/api/getCertificationInfo
       */
      export namespace getCertificationInfo {
        export class Params {
          /** certificationName */
          certificationName: string;
          /** certificationType */
          certificationType: string;
        }

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.CertificationInfo>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getChildrenBasicInfo
       * /user-server/api/getChildrenBasicInfo
       */
      export namespace getChildrenBasicInfo {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.MoreChildrenEducation>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getChildrenEduInfo
       * /user-server/api/getChildrenEduInfo
       */
      export namespace getChildrenEduInfo {
        export class Params {
          /** childrenId */
          childrenId: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.ChildrenEducation>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getCityInfo
       * /user-server/api/getCityInfo
       */
      export namespace getCityInfo {
        export class Params {
          /** cityName */
          cityName: string;
        }

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.CityInfo>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getContinuingBasicInfo
       * /user-server/api/getContinuingBasicInfo
       */
      export namespace getContinuingBasicInfo {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.MoreContinuingEduInfo>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getContinuingEduInfo
       * /user-server/api/getContinuingEduInfo
       */
      export namespace getContinuingEduInfo {
        export class Params {
          /** eduId */
          eduId: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.ContinuingEduInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getCountryInfo
       * /user-server/api/getCountryInfo
       */
      export namespace getCountryInfo {
        export class Params {
          /** countryName */
          countryName: string;
        }

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.CountryInfo>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getCustName
       * /user-server/api/getCustName
       */
      export namespace getCustName {
        export class Params {
          /** employeeId */
          employeeId?: string;
        }

        export type Response = defs.users.ExecuteResult<Array>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getEndowmentInsuranceInfo
       * /user-server/api/getEndowmentInsuranceInfo
       */
      export namespace getEndowmentInsuranceInfo {
        export class Params {
          /** employeeId */
          employeeId?: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.EndowmentInsuranceInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getEssentialInfo
       * /user-server/api/getEssentialInfo
       */
      export namespace getEssentialInfo {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.EssentialInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getEventFlag
       * /user-server/api/getEventFlag
       */
      export namespace getEventFlag {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export type Response = defs.users.ExecuteResult<defs.users.EventFlag>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getEventInfo
       * /user-server/api/getEventInfo
       */
      export namespace getEventInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.EventInfo>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getHealthInsuranceInfo
       * /user-server/api/getHealthInsuranceInfo
       */
      export namespace getHealthInsuranceInfo {
        export class Params {
          /** employeeId */
          employeeId?: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.HealthInsuranceInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getHouseBasicInfo
       * /user-server/api/getHouseBasicInfo
       */
      export namespace getHouseBasicInfo {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.HouseBasicInfo>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getHouseLoanInfo
       * /user-server/api/getHouseLoanInfo
       */
      export namespace getHouseLoanInfo {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.HouseLoanInfo>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getHouseRentalInfo
       * /user-server/api/getHouseRentalInfo
       */
      export namespace getHouseRentalInfo {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.HouseRentalInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getLoanInfo
       * /user-server/api/getLoanInfo
       */
      export namespace getLoanInfo {
        export class Params {
          /** houseId */
          houseId: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.HouseLoanInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getMedicalBasicInfo
       * /user-server/api/getMedicalBasicInfo
       */
      export namespace getMedicalBasicInfo {
        export class Params {
          /** employeeId */
          employeeId: string;
        }

        export type Response = defs.users.ExecuteResult<
          Array<defs.users.MedicalInfo>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getMedicalInfo
       * /user-server/api/getMedicalInfo
       */
      export namespace getMedicalInfo {
        export class Params {
          /** patientId */
          patientId: string;
        }

        export type Response = defs.users.ExecuteResult<defs.users.MedicalInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getSupportInfo
       * /user-server/api/getSupportInfo
       */
      export namespace getSupportInfo {
        export class Params {
          /** employeeId */
          employeeId?: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.MoreSupportInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * getTSupportInfo
       * /user-server/api/getTSupportInfo
       */
      export namespace getTSupportInfo {
        export class Params {
          /** tSupportId */
          tSupportId?: string;
        }

        export type Response =
          defs.users.ExecuteResult<defs.users.TSupportInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * isTaxApply
       * /user-server/api/isTaxApply
       */
      export namespace isTaxApply {
        export class Params {
          /** employeeId */
          employeeId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * saveChildrenEdu
       * /user-server/api/saveChildrenEdu
       */
      export namespace saveChildrenEdu {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.ChildrenEducation,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ChildrenEducation,
          options?: Taro.request.CommonUseRequestOption<defs.users.ChildrenEducation>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ChildrenEducation
        >;
      }

      /**
       * saveContinuingEdu
       * /user-server/api/saveContinuingEdu
       */
      export namespace saveContinuingEdu {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.ContinuingEduInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.ContinuingEduInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.ContinuingEduInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.ContinuingEduInfo
        >;
      }

      /**
       * saveEndowmentInsuranceInfo
       * /user-server/api/saveEndowmentInsuranceInfo
       */
      export namespace saveEndowmentInsuranceInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.EndowmentInsuranceInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.EndowmentInsuranceInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.EndowmentInsuranceInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.EndowmentInsuranceInfo
        >;
      }

      /**
       * saveEssential
       * /user-server/api/saveEssential
       */
      export namespace saveEssential {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.EssentialInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.EssentialInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.EssentialInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.EssentialInfo
        >;
      }

      /**
       * saveHealthInsuranceInfo
       * /user-server/api/saveHealthInsuranceInfo
       */
      export namespace saveHealthInsuranceInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.HealthInsuranceInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.HealthInsuranceInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.HealthInsuranceInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.HealthInsuranceInfo
        >;
      }

      /**
       * saveHouseLoanInfo
       * /user-server/api/saveHouseLoanInfo
       */
      export namespace saveHouseLoanInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.HouseLoanInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.HouseLoanInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.HouseLoanInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.HouseLoanInfo
        >;
      }

      /**
       * saveHouseRentalInfo
       * /user-server/api/saveHouseRentalInfo
       */
      export namespace saveHouseRentalInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.HouseRentalInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.HouseRentalInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.HouseRentalInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.HouseRentalInfo
        >;
      }

      /**
       * saveMedicalInfo
       * /user-server/api/saveMedicalInfo
       */
      export namespace saveMedicalInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.MedicalInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.MedicalInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.MedicalInfo>,
        ) => Taro.request.CommonUseResultType<Response, defs.users.MedicalInfo>;
      }

      /**
       * saveSupportInfo
       * /user-server/api/saveSupportInfo
       */
      export namespace saveSupportInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.SupportInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.SupportInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.SupportInfo>,
        ) => Taro.request.CommonUseResultType<Response, defs.users.SupportInfo>;
      }

      /**
       * saveTSupportInfo
       * /user-server/api/saveTSupportInfo
       */
      export namespace saveTSupportInfo {
        export class Params {}

        export type Response = defs.users.ExecuteResult;
        export const request: (
          data?: defs.users.TSupportInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.users.TSupportInfo,
          options?: Taro.request.CommonUseRequestOption<defs.users.TSupportInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.users.TSupportInfo
        >;
      }
    }
  }
}
