import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleQingDaoColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {

    const isBankPaySs = form.watch('isBankPaySs');
    const isAgriculturalMedical = form.watch('isAgriculturalMedical');
    const hasUnemployBenifit = form.watch('hasUnemployBenifit');

    if (column.name === 'isBankPaySs'){
        return { ...column, remind: !'0'.includes(isBankPaySs) ? column.remind : ''}
    }
    if (column.name === 'isAgriculturalMedical'){
        return { ...column, remind: !'0'.includes(isAgriculturalMedical) ? column.remind : ''}
    }
    if (column.name === 'hasUnemployBenifit'){
        return { ...column, remind: !'0'.includes(hasUnemployBenifit) ? column.remind : ''}
    }


    return column
}

export { handleQingDaoColumn }