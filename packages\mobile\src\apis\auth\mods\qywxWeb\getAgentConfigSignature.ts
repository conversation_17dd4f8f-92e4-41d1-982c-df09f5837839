/**
 * @description 获取WECOM-JSSDK签名-应用签名
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 企业ID，getTokenByCode返回的，前端缓存着使用 */
  cropId: string;
  /** 页面地址，#号之前的部分 */
  pageUrl: string;
}

export type Result = defs.auth.GlobalResult<defs.auth.QySdkSign>;
export const path = '/enterprise-auth/web/qywx/getAgentConfigSignature';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
