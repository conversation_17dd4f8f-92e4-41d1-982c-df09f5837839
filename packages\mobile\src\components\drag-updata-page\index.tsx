import Taro, { Component } from "@tarojs/taro";
import { useReducer } from "react";
import { View, Text, ScrollView } from "@tarojs/components";
import styles from "./index.module.scss";

interface DragUpdataPageProps {
  // 上拉
  onPull?: () => void;
  // 禁止上拉加载
  noPull?: boolean;
  // 下拉
  onDown?: () => void;
  // 禁止下拉刷新
  noDown?: boolean;
  // 滚动到顶部事件
  onUpper?: () => void;
  // 滚动到底部事件
  onLower?: () => void;
  children?: any;
}
const reducer = (state, action) => {
  switch (action.type) {
    case "setState":
      return { ...state, ...action.payload };
    default:
      throw new Error();
  }
};
const initialState = {
  dargStyle: {
    //下拉框的样式
    top: 0 + "px"
  },
  downDragStyle: {
    //下拉图标的样式
    height: 0 + "px"
  },
  downText: "下拉刷新",
  upDragStyle: {
    //上拉图标样式
    height: 0 + "px"
  },
  pullText: "上拉加载更多",
  start_p: {},
  scrollY: true,
  dargState: 0 //刷新状态 0不做操作 1刷新 -1加载更多
};
const DragUpdataPage = (props: DragUpdataPageProps) => {
  const { onPull, noPull, onDown, noDown, onLower, onUpper } = props;
  const [state, dispatch] = useReducer(reducer, initialState);
  const setState = data => {
    dispatch({ type: "setState", payload: data });
  };
  const reduction = () => {
    //还原初始设置
    const time = 0.5;
    setState({
      upDragStyle: {
        //上拉图标样式
        height: 0 + "px",
        transition: `all ${time}s`
      },
      dargState: 0,
      dargStyle: {
        top: 0 + "px",
        transition: `all ${time}s`
      },
      downDragStyle: {
        height: 0 + "px",
        transition: `all ${time}s`
      },
      scrollY: true
    });
    setTimeout(() => {
      setState({
        dargStyle: {
          top: 0 + "px"
        },
        upDragStyle: {
          //上拉图标样式
          height: 0 + "px"
        },
        pullText: "上拉加载更多",
        downText: "下拉刷新"
      });
    }, time * 1000);
  };
  const touchStart = e => {
    setState({
      start_p: e.touches[0]
    });
  };
  const touchmove = e => {
    let move_p = e.touches[0], //移动时的位置
      deviationX = 0.3, //左右偏移量(超过这个偏移量不执行下拉操作)
      deviationY = 70, //拉动长度（低于这个值的时候不执行）
      maxY = 100; //拉动的最大高度

    let start_x = state.start_p.clientX,
      start_y = state.start_p.clientY,
      move_x = move_p.clientX,
      move_y = move_p.clientY;

    //得到偏移数值
    let dev = Math.abs(move_x - start_x) / Math.abs(move_y - start_y);
    if (dev < deviationX) {
      //当偏移数值大于设置的偏移数值时则不执行操作
      let pY = Math.abs(move_y - start_y) / 3.5; //拖动倍率（使拖动的时候有粘滞的感觉--试了很多次 这个倍率刚好）
      if (move_y - start_y > 0) {
        if (noDown) return;
        //下拉操作
        if (pY >= deviationY) {
          setState({ dargState: 1, downText: "释放刷新" });
        } else {
          setState({ dargState: 0, downText: "下拉刷新" });
        }
        if (pY >= maxY) {
          pY = maxY;
        }
        setState({
          dargStyle: {
            top: pY + "px"
          },
          downDragStyle: {
            height: pY + "px"
          },
          scrollY: false //拖动的时候禁用
        });
      }
      if (start_y - move_y > 0) {
        //上拉操作
        if (noPull) return;
        if (pY >= deviationY) {
          setState({ dargState: -1, pullText: "释放加载更多" });
        } else {
          setState({ dargState: 0, pullText: "上拉加载更多" });
        }
        if (pY >= maxY) {
          pY = maxY;
        }
        setState({
          dargStyle: {
            top: -pY + "px"
          },
          upDragStyle: {
            height: pY + "px"
          },
          scrollY: false //拖动的时候禁用
        });
      }
    }
  };
  const pull = () => {
    // 上拉加载
    // console.log("上拉加载");
    onPull && onPull();
  };
  const down = () => {
    // 下拉刷新
    // console.log("下拉刷新");
    onDown && onDown();
  };
  const ScrollToUpper = () => {
    // 滚动到顶部事件
    // console.log("滚动到顶部事件");
    onUpper && onUpper();
  };
  const ScrollToLower = () => {
    // 滚动到底部事件
    // console.log("滚动到底部事件");
    onLower && onLower();
  };
  const touchEnd = () => {
    if (state.dargState === 1) {
      down();
    } else if (state.dargState === -1) {
      pull();
    }
    reduction();
  };
  return (
    <View className={styles.dragUpdataPage}>
      <View className={styles.downDragBox} style={state.downDragStyle}>
        <Text className={styles.downText}>{state.downText}</Text>
      </View>
      <ScrollView
        style={state.dargStyle}
        onTouchMove={touchmove}
        onTouchEnd={touchEnd}
        onTouchStart={touchStart}
        onScrollToUpper={ScrollToUpper}
        onScrollToLower={ScrollToLower}
        className={styles.dragUpdata}
        scrollY={state.scrollY}
        scrollWithAnimation
      >
        {props.children}
      </ScrollView>
      <View className={styles.upDragBox} style={state.upDragStyle}>
        <Text className={styles.downText}>{state.pullText}</Text>
      </View>
    </View>
  );
};

export { DragUpdataPage };
