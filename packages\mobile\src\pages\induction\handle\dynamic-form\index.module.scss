.wrap {
  width: 100%;
  background-color: #ffffff;
  position: relative;
  flex: 1;
  /*  #ifndef rn */
  height: 100vh;
  /*  #endif  */
}

.section_header {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  margin: 30px;
}
.section_title {
  font-size: 30px;
  color: #b51e25;
}

.section_line {
  width: 20px;
  height: 1px;
  background-color: #b51e25;
  margin: 0px 20px;
}

.cell {
  display: flex;
  align-items: center;
  flex-direction: row;
  border: 0px solid #efeff4;
  border-bottom-width: 1px;
  padding: 20px 30px;
  background-color: #ffffff;
}

.cell_require {
  width: 20px;
  height: 20px;
  margin-right: 16px;
}

.cell_label {
  font-size: 28px;
  width: 300px;
}

.cell_arrow {
  width: 20px;
  height: 20px;
  margin-left: 20px;
}

.cell_input {
  background-color: #ffffff;
  border-radius: 4px;
  height: 72px;
  width: 620px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
  font-size: 28px;
  color: #333;
}

.cell_tag {
  width: 40%;
  height: 80px;
  border-radius: 8px;
  border: 1px solid #d7d7d7;
}

.cell_tag_active {
  width: 40%;
  height: 80px;
  border-radius: 8px;
  border: 1px solid #b51e25;
}

.tip {
  margin-top: 30px;
}
.button {
  color: #fff;
  background-color: #b51e25;
  font-size: 24px;
}
.medical {
  padding: 10px;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex-wrap: wrap;
}
.medical_content {
  border: 0 dashed #d7d7d7;
  width: 100%;
  border-width: 1px;
  border-radius: 4px;
  padding: 14px;
  background: rgba(239, 239, 244, 0.3);
}
.medical_label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
}
.medical_text {
  background: #fff;
  border: solid 0 #e3e3ec;
  border-width: 1px;
}
.medical_title {
  font-size: 28px;
  color: #333;
}
.medical_item {
  border: 0 solid #e3e3ec;
  border-width: 1px;
  background: #fff;
}
.medical_fourth {
  padding: 20px;
  background: rgba(239, 239, 244, 0.2);
  border: solid 0 #efeff4;
  border-bottom-width: 1px;
  font-size: 26px;
  color: #333;
  min-height: 50px;
  line-height: 50px;
}
.item_ul {
  padding: 20px;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.item_list {
  display: flex;
  width: 50%;
  // white-space: nowrap;
  font-size: 26px;
  color: #333;
  padding: 10px;
}

.icon {
  margin: 20px;
  font-size: 24px;
}

.item_arrow {
  height: 20.55px;
  width: 12.97px;
  margin-left: 20px;
}

.item_wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-left: 6px;
}

.file_item {
  width: 210px;
  height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #efeff4;
  margin: 15px;
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 40px;
}
.title {
  color: #333;
  font-size: 24px;
}

.img_wrap {
  width: 160px;
  height: 160px;
  border: 1px dashed #efeff4;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.img {
  width: 150px;
  height: 150px;
}
