import { useState } from 'react'
import { View, Text, Input, Image, Picker } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import { BoxShadow, ListView, usePagination, withPage } from '@components'
import { pact } from '@apis/pact'
import { getGlobalData,getScrollStyle } from '@utils'
import bgTop from '@assets/appointment/bg-top.jpg'
import icon_search from '@assets/icon/icon-search.png'
import arrowDown from '@assets/icon/icon-arrow-down.png'
import arrow from '@assets/icon/icon-arrow.png'

import styles from './index.module.scss'

interface resultType {
  key: string
  value: string
  hosCode: string
  distric: string
  hosLevel: string
  hosType: string
}
const cityRange = [
  '门头沟区',
  '顺义区',
  '朝阳区',
  '房山区',
  '通州区',
  '平谷区',
  '亦庄开发区',
  '西城区',
  '密云区',
  '海淀区',
  '石景山区',
  '大兴区',
  '丰台区',
  '延庆区',
  '东城区',
  '昌平区',
  '怀柔区'
]
const levelsRange = ['三级甲等', '三级合格', '二级甲等', '二级合格', '一级甲等', '一级合格', '未评级']
const Index = () => {
  const { openId } = getGlobalData<'account'>('account')
  const scrollStyle = getScrollStyle({})
  const { eventName, scen = '2', cityId, keys1, keys2, keys3, keys4 } = useRouter().params
  const cityName = useState<string>('请选择')
  const LevelsName = useState<string>('请选择')
  const filterValue = useState<string>('')
  const filterName = useState<string>('')
  const list = usePagination(
    async page => {
      if (page > 1) {
        return []
      }
      let result = await pact.entry.designatedHospitals.request({
        openId,
        cityId: cityId,
        filterValue: filterValue[0],
        hosCountyName: cityName[0] === '请选择' ? '' : cityRange[cityName[0]],
        hosLevel: LevelsName[0] === '请选择' ? '' : levelsRange[LevelsName[0]]
      })
      result?.data?.forEach((element, index) => {
        if (element.key === keys1) {
          result?.data?.splice(index, 1)
        }
      })
      result?.data?.forEach((element, index) => {
        if (element.key === keys2) {
          result?.data?.splice(index, 1)
        }
      })
      result?.data?.forEach((element, index) => {
        if (element.key === keys3) {
          result?.data?.splice(index, 1)
        }
      })
      result?.data?.forEach((element, index) => {
        if (element.key === keys4) {
          result?.data?.splice(index, 1)
        }
      })
      console.log(result.data)
      return result.data
    },
    { deps: [cityName[0], LevelsName[0], filterValue[0]] }
  )
  const handleCityChange = e => {
    cityName[1](e.detail.value)
  }
  const handleLevelsChange = e => {
    LevelsName[1](e.detail.value)
  }
  const handleInput = e => {
    filterName[1](e.detail.value)
  }
  const handleSearch = () => {
    filterValue[1](filterName[0] || '')
  }
  const handleClick = item => {
    if (eventName) {
      if (scen === '2') {
        Taro.eventCenter.trigger(eventName, {
          [eventName]: {
            category: item.hosType,
            code: item.hosCode,
            distric: item.hosCountyName,
            key: item.key,
            level: item.hosLevel,
            name: item.value
          }
        })
        Taro.navigateBack({
          delta: 1
        })
        return
      }
    }
  }
  return (
    <View>
      <View className={styles.wrap}>
        <Image className={styles.bg_img} src={bgTop} style={{ height: Taro.pxTransform(1 ? 256 : 286) }} />
        <View className={styles.content}>
          <View className={styles.medical_search}>
            <Input className={styles.input} placeholder='请输入定点机构名称' onBlur={handleInput} />
            <View className={styles.search_btn} onClick={handleSearch}>
              <Image className={styles.search_icon} src={icon_search} />
            </View>
          </View>
          <View className={styles.tag_wrap}>
            <View className={styles.tag_wrap_list}>
              <View className={`${styles.tag_inline} ${styles.p_t}`}>城区</View>
              <View className={`${styles.tag_inline}`}>
                <Picker mode='selector' range={cityRange} onChange={handleCityChange}>
                  <View className={styles.form_picker_view}>
                    <Text className={styles.text}>{cityRange[cityName[0]] || cityName[0]}</Text>
                    <Image className={styles.form_select_arrow} src={arrowDown} />
                  </View>
                </Picker>
              </View>
            </View>
            <View className={styles.tag_wrap_list}>
              <View className={`${styles.tag_inline} ${styles.p_t}`}>等级</View>
              <View className={styles.tag_inline}>
                <Picker mode='selector' range={levelsRange} onChange={handleLevelsChange}>
                  <View className={styles.form_picker_view}>
                    <Text className={styles.text}>{levelsRange[LevelsName[0]] || LevelsName[0]}</Text>
                    <Image className={styles.form_select_arrow} src={arrowDown} />
                  </View>
                </Picker>
              </View>
            </View>
          </View>
        </View>
        <ListView
          style={scrollStyle}
          itemSize={250}
          renderItem={(item, index) => (
            // <View className={styles.item}>
            <View key={index} onClick={() => handleClick(item)}>
              <BoxShadow
                shadowColor='#000'
                shadowOffset={{
                  width: 0,
                  height: -1
                }}
                shadowOpacity={0.1}
                shadowRadius={1}
                elevation={5}
                boxShadow='0px 2px 8px 0px rgba(211,215,218,1)'
                className={styles.item_detail}
              >
                <View className={styles.list_title}>
                  <Text className={styles.text_title}>{item.value}</Text>
                  <Image className={styles.list_title_arrow} src={arrow} />
                </View>
                <View className={styles.group_list}>
                  <View className={styles.list_top_item}>
                    医院编号:<Text className={styles.text_title}>{item.hosCode}</Text>
                  </View>
                  <View className={styles.list_top_item}>
                    医院类别:<Text className={styles.text_title}>{item.hosType}</Text>
                  </View>
                </View>
                <View className={styles.group_list}>
                  <View className={styles.list_top_item}>
                    所属县区：<Text className={styles.text_title}>{item.hosCountyName}</Text>
                  </View>
                  <View className={styles.list_top_item}>
                    医院等级：<Text className={styles.text_title}>{item.hosLevel}</Text>
                  </View>
                </View>
              </BoxShadow>
            </View>
            // </View>s
          )}
          {...list}
        />
      </View>
    </View>
  )
}

export default withPage(Index)
