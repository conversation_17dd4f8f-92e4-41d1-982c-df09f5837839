/**
 * @description 获取所有城市接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** cityname */
  cityname?: string;
}

export type Result = Array<defs.pact.MoreCityInfo>;
export const path = '/yc-wepact-mobile/cityinfo/getAllcitys';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
