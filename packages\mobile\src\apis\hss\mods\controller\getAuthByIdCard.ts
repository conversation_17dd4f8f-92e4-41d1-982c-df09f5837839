/**
 * @description 根据身份证号获取汇算认证信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** idCard */
  idCard: string;
}

export type Result = defs.hss.GeneralRespBean<defs.hss.HsAuth>;
export const path = '/yc-hs/api/getAuthByIdCard/{idCard}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
