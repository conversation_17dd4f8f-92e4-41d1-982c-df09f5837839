import { ScrollView, View, Text, Image } from '@tarojs/components'
import { Fragment, useEffect, useState, FunctionComponent } from 'react'
import { BottomBtn } from '@components'
import { getGlobalData, getScrollStyle } from '@utils'
import { ncmp } from '@apis/ncmp'
import Taro from '@tarojs/taro'
import icon_circle from '@assets/icon/icon-circle.png'
import icon_checkbox from '@assets/icon/icon-checkbox.png'
import styles from './index.module.scss'
import { PageParams } from './type'

interface CheckboxPage extends PageParams {}
// 支出多选 TODO: 单选限制个数
const Index: FunctionComponent<CheckboxPage> = props => {
  const { eventName, busTypeId,busSubtypeId } = props
  const [values, setValues] = useState<string[]>(busSubtypeId?.split(',') || [])
  const [list, setList] = useState<any[]>([])
  const { empId} = getGlobalData<'account'>('account')
  useEffect(() => {
    ncmp.policy.getSubType
    .request({busTypeId, empId, isBooked: '1'})
    .then(res => {
      if (res.code == '200') {
        let data = res?.resultObj?.map(item => {
          return {
            key: item.busSubtypeId,
            value: item.busSubtypeName,
            remark: item.remark,
          }
        })
        if (data.length === 0) {
          Taro.showToast({ icon: 'none', title: '暂无可选信息！' })
          return
        }
        setList(data as any)
      } else {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      }
    })
    .catch(() => {
      Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
    })
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const onSubmit = () => {
    const value = values.map(v => list.find(l => l.key === v).value).join(',')
    eventName && Taro.eventCenter.trigger(eventName, { busSubtypeId: values.join(','), busSubtypeName: value, remark: ''})
    Taro.navigateBack()
  }
  return (
    <Fragment>
      <ScrollView style={scrollStyle} scrollY>
        {list?.map(item => {
          const index = values.indexOf(item.key)
          const checked = index >= 0
          return (
            <View
              key={item.key}
              className={styles.item}
              onClick={() => {
                if (checked) {
                  values.splice(index, 1)
                  setValues([...values])
                } else {
                  setValues([...values, item.key])
                }
              }}
            >
              <View className={styles.item_value}>
                <Image className={styles.checkbox} src={checked ? icon_checkbox : icon_circle} />
                <Text className={styles.text}>{item.value}</Text>
              </View>
              {item.remark && <View className={styles.remark}>{`说明: ${item.remark}`}</View>}
            </View>
          )
        })}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () => Taro.navigateBack()
          },
          {
            title: '确定',
            onClick: onSubmit,
            disabled:values.length === 0,
          }
        ]}
      />
    </Fragment>
  )
}

export default Index
