/*
 * @Author: your name
 * @Date: 2021-08-31 15:48:40
 * @LastEditTime: 2021-10-14 15:48:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\pledge\index.tsx
 */
/**
 * 税前抵扣申请-承诺书
 */
import { useState, useEffect } from 'react'
import { BottomBtn } from '@components'
import { View, ScrollView, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { getScrollStyle } from '@utils/transforms'
import styles from './index.module.scss'

const Index = () => {
  const [isChek, setIsChek] = useState(false)
  const handleChange = e => {
    // console.log(e.target.checked)
    setIsChek(e.target.checked)
  }

  const scrollStyle = getScrollStyle({ bottom: 120 })
  return (
    <View className={styles.wrap}>
      <ScrollView style={scrollStyle}>
        <View className={styles.title}>
          <Text>申报材料真实性承诺书</Text>
        </View>
        <View className={styles.cont}>
          <Text>
            我郑重承诺，委托单位申报2019年度个人所得税中所提交的个人信息、材料及附件内容均真实、有效、合法，若申报材料中存在虚假、伪造等不实情况，所产生的一切后果由本人承担。
          </Text>
        </View>
        <View className={styles.checkbox}>
          <input onClick={handleChange} type='checkbox' className={styles.checkd} />
          <View>我同意</View>
        </View>
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '下一步',
            onClick: () => Taro.navigateTo({ url: '/pages/pre-tax/basicInformation/index' })
          }
        ]}
      />
    </View>
  )
}

export default Index
