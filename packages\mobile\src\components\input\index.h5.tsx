/**
 * H5端输入框
 * 解决 键盘输入删除问题
 * 九宫格数字标点符号输入，多点会回退删除
 */

import { InputProps } from '@tarojs/components/types/Input'
import React, { FunctionComponent } from 'react'

function fixControlledValue(value?: string) {
  return value ?? ''
}

function getTrueType(type: string | undefined, confirmType: string = '', password: boolean = false) {
  if (confirmType === 'search') type = 'search'
  if (password) type = 'password'
  if (typeof type === 'undefined') {
    return 'text'
  }
  if (!type) {
    throw new Error('unexpected type')
  }
  if (type === 'digit') type = 'number'

  return type
}

const Input: FunctionComponent<InputProps> = React.forwardRef((props, ref) => {
  const {
    value,
    name,
    onInput,
    onBlur,
    onConfirm,
    onFocus,
    className = '',
    type,
    autoFocus,
    confirmType,
    password,
    placeholder,
    disabled,
    maxlength = 140,
    ...rest
  } = props
  const handleKeyDown = (e: any) => {
    e.stopPropagation()
    // this.onKeyDown.emit({ value: e.target.value })
    e.keyCode === 13 && onConfirm?.({ value: e.target.value } as any)
  }
  return (
    <input
      ref={ref}
      className={`weui-input ${className}`}
      value={fixControlledValue(value)}
      type={getTrueType(type, confirmType, password)}
      placeholder={placeholder}
      disabled={disabled}
      maxLength={maxlength}
      autoFocus={autoFocus}
      name={name}
      // onInput={handleInput}
      onFocus={onFocus as any}
      onBlur={onBlur as any}
      onChange={e => onInput?.(e as any)}
      onKeyDown={handleKeyDown}
    ></input>
  )
})

export default Input
