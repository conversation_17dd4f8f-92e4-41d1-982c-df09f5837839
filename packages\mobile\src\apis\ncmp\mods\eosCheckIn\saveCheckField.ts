/**
 * @description 外勤打卡）
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<number>;
export const path = '/wx-ncmp/eos/attendance/checkin/saveCheckField';
export const method = 'POST';
export const request = (
  data: defs.ncmp.AcCheckLog,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.AcCheckLog,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
