
import { Text, View } from '@tarojs/components'
import { BottomBtn, Form, FormItemProps, useForm, withPage } from '@components'
import styles from './index.module.scss'
import { getScrollStyle } from '@utils/index'

const Index = () => {

  const form = useForm<any>()
  const scrollStyle = getScrollStyle({ bottom: 120, top: 20, hasNav: false })
  const columns: FormItemProps[] = [
    {
      title: '补签类型',
      name: 'idCardNum',
      type: 'select',
      rules: { required: true },
      showLine: true,
      options: [{ key: 'a', value: 1 }]
    },
    {
      title: '补签日期',
      name: 'mobilePhoneNum',
      type: 'date',
      rules: { required: true },
      showLine: true
    },
    {
      title: '补签时间',
      name: 'mobilePhoneNum2',
      type: 'time',
      rules: { required: true },
      showLine: false
    },
    {
      title: '',
      name: '',
      showLine: false,
      render: () => <View className={styles.line}></View>
    },
    {
      title: '补签原因',
      name: 'mobilePhoneNum66',
      type: 'textarea',
      inputProps: { maxlength: 200 },
      showLine: false
    },
  ]

  const onSubmit = data => {
    console.log(data);
  }

  return (
    <View className={styles.leave}>
      <Form form={form} columns={columns} style={scrollStyle} />
      <BottomBtn
        btns={[
          {
            title: '提交',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
