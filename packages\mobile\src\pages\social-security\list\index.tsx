/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-08-17 10:11:12
 * @LastAuthor: 王正荣
 * @LastTime: 2021-11-22 15:01:15
 * @message:
 */
import { View, Text } from '@tarojs/components'
import { Fragment, useEffect, useState } from 'react'
import { getGlobalData, getScrollStyle } from '@utils'
import { pact } from '@apis/pact'
import { DateYearMonth, ListView, usePagination, withPage, Buttons, Modal } from '@components'
import dayjs from 'dayjs'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import styles from './index.module.scss'

const itemConfig = [
  { title: '缴费月份', key: 'serviceMonth' },
  // { title: '客户名称', key: 'custName' },
  { title: '企业金额', key: 'eAmt' },
  { title: '个人金额', key: 'pAmt' },
  { title: '合计', key: 'amt' }
]
const Index = () => {
  const { openId, empId } = getGlobalData<'account'>('account')
  const { type } = useRouter<{ type: 'social-security' | 'accumulation' }>().params
  const scrollStyle = getScrollStyle({ top: 256 })
  const [showData, setShowData] = useState<Object>()
  const [visible, setVisible] = useState(false)
  const [responseMessage, setResponseMessage] = useState('')
  const [month, setMonth] = useState<{ startMonth: string; endMonth: string }>({ startMonth: '', endMonth: '' })
  const list = usePagination(
    async page => {
      const result = await pact.busi.getSocialnsuranceAndfundList.request({
        openId,
        paramMap: {
          category: type === 'social-security' ? 1 : 2, // 社保公积金类型（1：社保；2：公积金）
          empId: Number(empId),
          pageNum: page,
          pageSize: 5,
          startMonth: dayjs(month.startMonth).format('YYYYMM'),
          endMonth: dayjs(month.endMonth).format('YYYYMM')
        }
      })
      if (result.message) {
        setVisible(true)
        setResponseMessage(result.message)
      } else {
        setShowData({
          eTotalAmt: result.eTotalAmt,
          pTotalAmt: result.pTotalAmt
        })
        return result.list
      }
    },
    { deps: [month], auto: !!month.startMonth }
  )
  useDidShow(() => {
    Taro.setNavigationBarTitle({ title: type === 'social-security' ? '社保查询' : '公积金查询' })
  })
  const detail = (serviceMonth: string, custName: string, eAmt: string, pAmt: string, amt: string) => {
    const data = { serviceMonth, custName, eAmt, pAmt, amt }
    Taro.navigateTo({ url: `/pages/social-security/detail/index?type=${type}&data=${JSON.stringify(data)}` })
  }
  return (
    <Fragment>
      <DateYearMonth
        onSelectHandle={e => {
          const { startDate, endDate } = e
          setMonth({ startMonth: startDate, endMonth: endDate })
        }}
        pickerProps={[{ fields: 'month'}, { fields: 'month' }]}
        showData={showData}
      />
      <ListView
        style={scrollStyle}
        itemSize={438}
        renderItem={item => (
          <View className={styles.item}>
            {itemConfig.map(config => (
              <View className={styles.text_wrap} key={config.key}>
                <Text className={styles.title}>{config.title}</Text>
                <Text className={styles.detail}>{item[config.key]}</Text>
              </View>
            ))}
            <View className={styles.btn_wrap}>
              <Buttons
                title='查看明细'
                icon='detail'
                onClick={() => detail(item.serviceMonth, item.custName, item.eAmt, item.pAmt, item.amt)}
              />
            </View>
          </View>
        )}
        {...list}
      />
      <Modal
        visible={visible}
        title='提示'
        onConfirm={() => {
          setVisible(false)
        }}
        content={responseMessage}
      />
    </Fragment>
  )
}

export default withPage(Index)
