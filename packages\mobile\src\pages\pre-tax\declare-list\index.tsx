/*
 * @Author: your name
 * @Date: 2021-08-31 15:48:40
 * @LastEditTime: 2021-10-15 17:28:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\declare-list\index.tsx
 */
/**
 * 税前抵扣申请- 申报列表
 */

import { useEffect, useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import { users } from '@apis/users'
import { BottomBtn, Form, FormItemProps, withPage,useForm } from '@components'
import Taro,{ useRouter } from '@tarojs/taro'
import { navigateTo, getGlobalData } from '@utils'
import { getScrollStyle } from '@utils/transforms'
import icon1 from '@assets/icon/icon_index_1.png'
import icon2 from '@assets/icon/icon_index_2.png'
import icon3 from '@assets/icon/icon_index_3.png'
import icon4 from '@assets/icon/icon_index_6.png'
import icon5 from '@assets/icon/icon_index_5.png'
import isEmpty from 'lodash/isEmpty'
import styles from './index.module.scss'

const Index = () => {
  const { empId } = getGlobalData<'account'>('account')
  // const empId  = '*********'
  const [childFlag, setClildFlag] = useState<any>(false)
  const [continueFlag, setContinueFlag] = useState<any>(false)
  const [houseLoanFlag, setHouseLoanFlag] = useState<any>(false)
  const [houseRentalFlag, setHouseRentalFlag] = useState<any>(false)
  const [supportFlag, setSupportFlag] = useState<any>(false)
  // *********
  useEffect(() => {
    users.user.getEventFlag
      .request({
        employeeId: empId
      })
      .then(res => {
        // console.log(res.data)
        setClildFlag(res.data?.childFlag)
        setContinueFlag(res.data?.continueFlag)
        setHouseLoanFlag(res.data?.houseLoanFlag)
        setHouseRentalFlag(res.data?.houseRentalFlag)
        setSupportFlag(res.data?.supportFlag)
        // !isEmpty(res.data?.childFlag) && setClildFlag(res.data?.childFlag)
        // !isEmpty(res.data?.continueFlag) && setContinueFlag(res.data?.continueFlag)
        // !isEmpty(res.data?.houseLoanFlag) && setHouseLoanFlag(res.data?.houseLoanFlag)
        // !isEmpty(res.data?.houseRentalFlag) && setHouseRentalFlag(res.data?.houseRentalFlag)
        // !isEmpty(res.data?.supportFlag) && setSupportFlag(res.data?.supportFlag)
        // console.log(childFlag, continueFlag, houseLoanFlag, houseRentalFlag, supportFlag, empId)
      })
  }, [empId])

  const list = [
    {
      title: '子女教育',
      img: icon1,
      onClick: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/chidrenEducation/index' }),
      onClicks: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/childrensLook/index' }),
      flag: childFlag
    },
    {
      title: '继续教育',
      img: icon2,
      onClick: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/education/index' }),
      onClicks: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/educationLook/index' }),
      flag: continueFlag
    },
    {
      title: '住房贷款利息/住房租金',
      img: icon3,
      onClick: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/houseLoan/index' }),
      onClicks: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/houseLook/index' }),
      flag: houseLoanFlag || houseRentalFlag
    },
    {
      title: '赡养老人',
      img: icon4,
      onClick: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/supportElder/index' }),
      onClicks: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/supportElder/index?readOnly=1' }),
      editClicks: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/supportElder/index' }),
      flag: supportFlag
    },
    {
      title: '个人信息',
      img: icon5,
      onClick: () => Taro.navigateTo({ url: '/pages/pre-tax/declare/personalIfo/index' }),
      onClicks: () => Taro.navigateTo({ url: '' })
    }
  ]

  return (
    <View className={styles.wrap}>
      <View>
        <a className={styles.link} href="https://mp.weixin.qq.com/s/-wxH_eNxdjesIgNIjEUZ1g">查看详情</a>
        {/* <Text className={styles.link} onClick={() => { Taro.navigateTo({ url: 'https://mp.weixin.qq.com/s/-wxH_eNxdjesIgNIjEUZ1g' })}}>查看详情</Text> */}
      </View>
      {list.map((item, index) => (
        <View className={styles.item} key={item.title}>
          <Image className={styles.img} src={item.img} />
          <Text className={styles.text}>{item.title}</Text>
          {item.flag ? (
            <View className={styles.btn_wrap}>
              <Text className={styles.btn} onClick={item.onClicks}>
                查看
              </Text>
            </View>
          ) : null}
          {index == 3 && item.flag ? (
            <View className={styles.btn_wrap}>
              <Text className={styles.btn} onClick={item.editClicks}>
                修改
              </Text>
            </View>
          ) : null}
          <View className={styles.btn_wrap}>
            <Text className={styles.btn} onClick={item.onClick}>
              去申请
            </Text>
          </View>
        </View>
      ))}
    </View>
  )
}

export default withPage(Index)
