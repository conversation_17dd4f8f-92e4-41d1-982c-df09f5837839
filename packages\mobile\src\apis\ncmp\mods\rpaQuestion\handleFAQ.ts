/**
 * @description handleFAQ
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<ObjectMap>;
export const path = '/wx-ncmp/rpa/faq/test';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
