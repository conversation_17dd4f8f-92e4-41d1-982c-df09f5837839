/*
 * @Author: your name
 * @Date: 2021-09-02 16:59:01
 * @LastEditTime: 2021-11-24 15:04:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\declare\houseLoan\index.tsx
 */
/**
 * 税前抵扣申请-住房贷款利息/住房租金
 */
import { useEffect, useState } from 'react'
import { View, Text, RadioGroup, Label, Radio } from '@tarojs/components'
import { BottomBtn, withPage, Modal } from '@components'
import { getGlobalData } from '@utils'
import { users } from '@apis/users'
import { useModal } from '@components/modal/useModal'

import Taro from '@tarojs/taro'
import styles from './index.module.scss'

const Index = () => {
  const employeeId = getGlobalData<'account'>('account').empId
  const [url, setUrl] = useState<string>('')
  const modal = useModal()

  const serchLoans = () => {
    // console.log(1)
    users.user.getHouseBasicInfo
      .request({
        employeeId: employeeId
      })
      .then(res => {
        // console.log(res)
        if (res.data?.length >= 1) {
          // console.log('住房贷款已经添加，如需修改请到查看列表！')
          const title = '提醒'
          const content = '住房贷款已经添加，如需修改请到查看列表！'
          setTimeout(() => modal.setModal({ title, content, visible: true }), 100)
          setTimeout(() => {
            Taro.navigateTo({ url: '/pages/pre-tax/declare/houseLook/index' })
          }, 2000)
        }
      })
  }

  return (
    <View className={styles.wrap}>
      <View className={styles.title}>
        <Text style={{ fontSize: '22px' }}>您将要选择以下哪一项抵扣</Text>
      </View>
      <RadioGroup
        className={styles.redio}
        onChange={e => {
          // console.log(e.detail.value)
          setUrl(e.detail.value)
        }}
      >
        <Label style={{ margin: '0 0 10px 0' }} key='1'>
          <Radio id='1' value='1' onClick={serchLoans}>
            住房贷款利息支出
          </Radio>
        </Label>
        <Label key='2'>
          <Radio id='2' value='2'>
            住房租金支出
          </Radio>
        </Label>
      </RadioGroup>
      <BottomBtn
        btns={[
          {
            title: '下一步',
            onClick: e => {
              // console.log(url)
              if (url == '1') {
                Taro.navigateTo({ url: '/pages/pre-tax/declare/houseLoan/housingLoans/index' })
              } else if (url == '2') {
                Taro.navigateTo({ url: '/pages/pre-tax/declare/houseLoan/housingRents/index' })
              }
            }
          }
        ]}
      />
      <Modal {...modal.modal} />
    </View>
  )
}

export default withPage(Index)
