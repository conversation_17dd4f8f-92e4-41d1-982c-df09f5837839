.opts {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.opts_title {
  display: flex;
  align-items: center;
  margin-top: 30px;
  margin-left: 20px;
}
.opts_container {
  font-size: 24px;
  padding: 10px 24px;
  margin: 18px;
  border-radius: 8px;
  border: 1px solid #ccc;
}
.opts_choose {
  font-size: 24px;
  color: #b51e25;
  margin-left: 30px;
}
.opts_city {
  margin: 18px;
  font-size: 24px;
}
.btn {
  width: 100vw;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 100px;
}
.rest {
  width: 50vw;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border: 1px solid #ccc;
}
.complete {
  width: 50vw;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border: 1px solid #ccc;
  background-color: #b51e25;
  color: #ffffff;
}
.active{
  background-color: #b51e25;
  color: #ffffff;
}