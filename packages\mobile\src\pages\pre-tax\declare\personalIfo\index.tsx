/*
 * @Author: your name
 * @Date: 2021-09-10 17:24:06
 * @LastEditTime: 2021-09-10 17:47:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\declare\personalIfo\index.tsx
 */
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import icon_home_6 from '@assets/appointment/icon-home-6.png'
import icon_home_4 from '@assets/appointment/icon-home-4.png'
import styles from './index.module.scss'

const list = [
  {
    title: '基本信息',
    img: icon_home_4,
    onClick: () => Taro.navigateTo({ url: '/pages/pre-tax/basicInformation/index' })
  }
]

const Index = () => {
  return (
    <View className={styles.wrap}>
      {list.map(item => (
        <View className={styles.item} key={item.title} onClick={item.onClick}>
          <Image className={styles.img} src={item.img} />
          <Text className={styles.text}>{item.title}</Text>
        </View>
      ))}
    </View>
  )
}

export default Index
