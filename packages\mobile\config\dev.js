module.exports = {
  env: {
    NODE_ENV: '"development"'
  },
  defineConstants: {},
  mini: {},
  h5: {
    devServer: {
      proxy: {
        '/yc-wepact-mobile': {
          target: 'https://wehr.ctgapp.com',
          changeOrigin: true,
          secure: true,
          logLevel: 'debug',
          onProxyReq: (proxyReq, req, res) => {
            console.log('Proxy request:', req.method, req.url)
          },
          onProxyRes: (proxyRes, req, res) => {
            // 添加CORS头
            proxyRes.headers['Access-Control-Allow-Origin'] = '*'
            proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
            proxyRes.headers['Access-Control-Allow-Headers'] =
              'Content-Type, Authorization, wxglobaltoken, wxGlobalToken, cmpToken'
            proxyRes.headers['Access-Control-Allow-Credentials'] = 'true'
            console.log('Proxy response:', proxyRes.statusCode, req.url)
          }
        },
        // 添加其他可能需要的API路径代理
        '/wx-ncmp': {
          target: 'https://wehr.ctgapp.com',
          changeOrigin: true,
          secure: true,
          logLevel: 'debug',
          onProxyReq: (proxyReq, req, res) => {
            console.log('Proxy request:', req.method, req.url)
          },
          onProxyRes: (proxyRes, req, res) => {
            proxyRes.headers['Access-Control-Allow-Origin'] = '*'
            proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
            proxyRes.headers['Access-Control-Allow-Headers'] =
              'Content-Type, Authorization, wxglobaltoken, wxGlobalToken, cmpToken'
            proxyRes.headers['Access-Control-Allow-Credentials'] = 'true'
            console.log('Proxy response:', proxyRes.statusCode, req.url)
          }
        },
        '/wx-upload': {
          target: 'https://wehr.ctgapp.com',
          changeOrigin: true,
          secure: true,
          logLevel: 'debug',
          onProxyReq: (proxyReq, req, res) => {
            console.log('Proxy request:', req.method, req.url)
          },
          onProxyRes: (proxyRes, req, res) => {
            proxyRes.headers['Access-Control-Allow-Origin'] = '*'
            proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
            proxyRes.headers['Access-Control-Allow-Headers'] =
              'Content-Type, Authorization, wxglobaltoken, wxGlobalToken, cmpToken'
            proxyRes.headers['Access-Control-Allow-Credentials'] = 'true'
            console.log('Proxy response:', proxyRes.statusCode, req.url)
          }
        },
        '/user-server': {
          target: 'https://wehr.ctgapp.com',
          changeOrigin: true,
          secure: true,
          logLevel: 'debug',
          onProxyReq: (proxyReq, req, res) => {
            console.log('Proxy request:', req.method, req.url)
          },
          onProxyRes: (proxyRes, req, res) => {
            proxyRes.headers['Access-Control-Allow-Origin'] = '*'
            proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
            proxyRes.headers['Access-Control-Allow-Headers'] =
              'Content-Type, Authorization, wxglobaltoken, wxGlobalToken, cmpToken'
            proxyRes.headers['Access-Control-Allow-Credentials'] = 'true'
            console.log('Proxy response:', proxyRes.statusCode, req.url)
          }
        }
      }
    }
  }
}
