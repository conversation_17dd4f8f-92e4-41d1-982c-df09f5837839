/**
 * @description 获取业务小类
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.BusinessSubType>;
export const path = '/wx-ncmp/policy/getSubType';
export const method = 'POST';
export const request = (
  data: defs.ncmp.BusinessSubType,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.BusinessSubType,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
