/*
 * @Author: your name
 * @Date: 2021-08-18 20:31:19
 * @LastEditTime: 2021-08-18 20:51:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \wehr_web2\packages\mobile\config\plugins\getSvg.ts
 */
/* eslint-disable import/no-commonjs */
import type { IPluginContext } from '@tarojs/service'
import { getAllFilesInFloder, processTypeEnum, printLog, fs } from '@tarojs/helper'

const REG_SVG = /\.svg(\?.*)?$/
const path = require('path');

const svgDir = path.resolve(__dirname, "../../src/assets");
const svgRNFile = path.resolve(__dirname, "../../src/assets/svg/index.rn.ts");
const svgFile = path.resolve(__dirname, "../../src/assets/svg/index.ts");

// // 读取单个文件
const readfile = (pathName: string) => {
  const pathString = pathName.replace(/\\/g, '/')
  const filename = path.basename(pathString, '.svg').replace(/-/g, '_')
  const relativePath = `@assets/${pathString.split('assets/')[1]}`
  return new Promise((resolve, reject) => {
    fs.readFile(pathString, "utf8", function (err, data) {
      printLog(processTypeEnum.COMPILE, filename)
      if (err) reject(err);
      resolve({
        rn: `export const ${filename} = ${JSON.stringify(data)}`,
        filename,
        other: `export { default as ${filename} } from ${JSON.stringify(relativePath)};`
      });
    });
  });
}

module.exports = function (ctx: IPluginContext) {
  ctx.onBuildStart(() => {
    printLog(processTypeEnum.START, '处理SVG文件');
    (async () => {
      const files = await getAllFilesInFloder(svgDir, ['.svg'])
      const svgFiles = files.filter(file => REG_SVG.test(file))
      svgFiles.map(filename => readfile(filename))
      Promise.all(svgFiles.map(filename => readfile(filename)))
        .then((data: any[]) => {
          fs.writeFile(path.resolve(__dirname, svgRNFile), data.map(item => item.rn).join('\n'), err => {
            if (err) throw err;
            printLog(processTypeEnum.CREATE, svgRNFile)
          });
          fs.writeFile(path.resolve(__dirname, svgFile), data.map(item => item.other).join('\n'), err => {
            if (err) throw err;
            printLog(processTypeEnum.CREATE, svgFile)
          });
        })
        .catch(err => {
          printLog(processTypeEnum.ERROR, err)
        });
    })()
  });

};

module.exports.default = module.exports;

