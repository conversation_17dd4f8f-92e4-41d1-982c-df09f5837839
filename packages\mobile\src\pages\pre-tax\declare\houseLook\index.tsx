/*
 * @Author: your name
 * @Date: 2021-09-10 14:40:39
 * @LastEditTime: 2021-11-23 11:10:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\declare\educationLook\index.tsx
 */
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { getGlobalData, setGlobalData, useSystemInfo } from '@utils'
import { getScrollStyle } from '@utils/transforms'
import Taro from '@tarojs/taro'
import icon_user from '@assets/icon/icon-user.png'
import isEmpty from 'lodash/isEmpty'
import { BottomBtn, Modal, withPage } from '@components'
import { users } from '@apis/users'
import useData from '@components/ListView/hooks/useData'
import styles from './index.module.scss'

const Index = () => {
  const empId = getGlobalData<'account'>('account').empId

  const [infolist, setInfolist] = useState<Array<defs.users.HouseBasicInfo> | undefined>()
  useEffect(() => {
    users.user.getHouseBasicInfo
      .request({
        employeeId: empId
      })
      .then(res => {
        // console.log(res)
        !isEmpty(res?.data) && setInfolist(res?.data)
      })
  }, [empId])

  //   编辑
  const toEdit = item => {
    // console.log('修改')
    if (item.type == 2) {
      Taro.navigateTo({ url: '/pages/pre-tax/declare/houseLoan/housingRents/index?id=' + item.id })
    } else {
      Taro.navigateTo({ url: '/pages/pre-tax/declare/houseLoan/housingLoans/index?id=' + item.id })
    }
  }

  const toDelete = item => {
    // console.log('删除')
    Taro.showModal({
      title: '删除',
      cancelText: '取消',
      cancelColor: 'xx',
      confirmText: '确认',
      confirmColor: 'xx',
      content: '一旦删除将不再继续抵扣，是否确认删除?',
      showCancel: true,
      success(result) {
        if (result.confirm) {
          // console.log('删除')
          if (item.type == 2) {
            users.user.deleteHouseRentalInfo.request({
              houseId: item.id
            }).then((res) => {
              // console.log(res)
              if(res?.code == 0) {
                users.user.getHouseBasicInfo
                .request({
                  employeeId: empId
                })
                .then(ress => {
                  // console.log(ress)
                  setInfolist(ress?.data)
                  Taro.navigateTo({
                    url: '/pages/pre-tax/declare-list/index'
                  })
                  Taro.showToast({
                    title: '删除成功',
                    icon: 'success',
                    duration: 2000
                  })
                })
              }
            })
          } else {
            users.user.deleteHouseLoanInfo.request({
              houseId: item.id
            }).then((res) => {
              // console.log(res)
              if(res?.code == 0) {
                users.user.getHouseBasicInfo
                .request({
                  employeeId: empId
                })
                .then(ress => {
                  // console.log(ress)
                  setInfolist(ress?.data)
                  Taro.navigateTo({
                    url: '/pages/pre-tax/declare-list/index'
                  })
                  Taro.showToast({
                    title: '删除成功',
                    icon: 'success',
                    duration: 2000
                  })
                })
              }
            })
          }
          
        } else if (result.cancel) {
          // console.log('不删除')
        }
      }
    })
    // if (item.type == 2) {
    //   users.user.deleteHouseRentalInfo.request({
    //     houseId: item.id
    //   }).then((res) => {
    //     console.log(res)
    //     if(res?.code == 0) {
    //       users.user.getHouseBasicInfo
    //       .request({
    //         employeeId: empId
    //       })
    //       .then(ress => {
    //         console.log(ress)
    //         !isEmpty(ress?.data) && setInfolist(ress?.data)
    //       })
    //     }
    //   })
    // } else {
    //   users.user.deleteHouseLoanInfo.request({
    //     houseId: item.id
    //   }).then((res) => {
    //     console.log(res)
    //     if(res?.code == 0) {
    //       users.user.getHouseBasicInfo
    //       .request({
    //         employeeId: empId
    //       })
    //       .then(ress => {
    //         console.log(ress)
    //         !isEmpty(ress?.data) && setInfolist(ress?.data)
    //       })
    //     }
    //   })
    // }
    // users.user.deleteChildrenEdu.request({
    //   childrenId: childrenId
    // }).then((res) => {
    //   console.log(res)
    //   if(res?.code == 0) {
    //     const newList = infolist.findIndex(item => item.childrenId === childrenId);
    //     setInfolist(newList)
    //   }
    // })
  }
  return (
    <View className={styles.wrap}>
      <View className={styles.group_item}>
        <View className={styles.title_secondary}>
          <View className={styles.title_mark}></View>
          申报子女
        </View>
        <View className={styles.infoUl}>
          <View className={styles.addSupport}>
            <View className={styles.names}>教育阶段/类型</View>
            <View className={styles.names}>提交日期</View>
          </View>
          <View className={styles.btns}></View>
        </View>

        {infolist ? (
          infolist.map(item => (
            <View key={item.id} className={styles.infoUl}>
              <View className={styles.addSupport}>
                <View className={styles.names}>
                  {item.type == 0 ? '商业贷款' : item.type == 1 ? '公积金贷款' : '住房租金'}
                </View>
                <View className={styles.names}>{item.updateDate}</View>
              </View>
              <View className={styles.btns}>
                <View className={styles.btn_wrap}>
                  <Text className={styles.btn} onClick={() => toDelete(item)}>删除</Text>
                </View>
                <View className={styles.btn_wrap}>
                  <Text className={styles.btn} onClick={() => toEdit(item)}>
                    编辑
                  </Text>
                </View>
              </View>
            </View>
          ))
        ) : (
          <View>暂无数据</View>
        )}
      </View>
    </View>
  )
}

export default withPage(Index)
