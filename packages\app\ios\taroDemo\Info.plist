<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>易智汇</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Allow $(PRODUCT_NAME) to use your camera</string>
	<key>NSContactsUsageDescription</key>
	<string>Allow $(PRODUCT_NAME) to access your contacts</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Allow $(PRODUCT_NAME) to use your location</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Allow $(PRODUCT_NAME) to access your microphone</string>
	<key>NSMotionUsageDescription</key>
	<string>Allow $(PRODUCT_NAME) to access your device's accelerometer</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Give $(PRODUCT_NAME) permission to save photos</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Give $(PRODUCT_NAME) permission to access your photos</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
  <key>NSAppTransportSecurity</key>
      <dict>
            <key>NSAllowsArbitraryLoads</key>
            <true/>
      </dict>
</dict>

</plist>
