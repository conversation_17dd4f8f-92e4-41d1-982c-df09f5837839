.wrap {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  border-radius: 12px;
  background-color: #ffffff;
  width: 630px;
}

.title {
  color: #333333;
  font-size: 36px;
  text-align: center;
  margin-top: 30px;
}

.content {
  width: 570px;
  margin-top: 30px;
  display: flex;
}

.content_text{
  color: #999999;
  font-size: 32px;
  text-align: center;
  line-height: 50px;
  width: 570px;
}

.footer {
  display: flex;
  justify-content: center;
  flex-direction: row;
  align-items: center;
  border: 0 solid rgb(213, 213, 214);
  border-top-width: 1px;
  margin-top: 30px;
  height: 90px;
  width: 100%;
  background-color: #fff;
  position: sticky;
  bottom: 0;
}
.btn {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  flex: 1;
  border: 0 solid rgb(213, 213, 214);
  border-right-width: 1px;
  height: 90px;
}

.btn_text {
   font-size: 32px;
   text-align: center;
   width: 100%;
   color: #333333;
}

.btn_text_primary{
  font-size: 32px;
  text-align: center;
  width: 100%;
  color: #3CC51F;

}

