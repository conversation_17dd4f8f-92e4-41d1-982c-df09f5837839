import { View, Image, Text } from "@tarojs/components";
import iconTip from "@assets/icon/icon-tip.png";
import styles from "./index.module.scss";

interface NoticebarProps {
  icon?: string;
  children?: any;
}
const Noticebar = (props: NoticebarProps) => {
  const { icon } = props;
  return (
    <View className={styles.notice_bar}>
      <Image className={styles.notice_icon} src={icon || iconTip} />
      <Text className={styles.notice_text}>{props.children}</Text>
    </View>
  );
};
export { Noticebar };
