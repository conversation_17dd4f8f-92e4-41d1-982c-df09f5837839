.btn {
    background-color: #b51e25;
    height: 88px;
    width: 710px;
    line-height: 88px;
    padding: 0 15px;
    margin: 20px;
    border: 0;
    border-radius: 4px;
    color: #fff;
    // white-space: nowrap;
    font-weight: 400;
    text-align: center;
    position: absolute;
    z-index: 10;
    bottom: 0;
  }
  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .list_text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    width: 100%;
    // background-color: #ccc;
    border: 1px solid #ccc;
    border-radius: 15px;
    margin: 0 0 10px 0;
  }
  .label_wrap {
    width: 25%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    justify-content: flex-start;
    flex-direction: row;
    margin: 0 0 0 30px;
  }
  .title {
    color: #333;
    font-size: 26px;
  }
  .list_item {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding: 0 30px 30px 30px;
  }
  .list_btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    padding: 0 30px;
  }
  .foot_btn {
    // width: 0px;
    // height: 60px;
    padding: 10px 20px;
    // line-height: 60px;
    background-color: red;
    color: #fff;
    border-radius: 10px;
    font-size: 26px;
    margin: 0 10px;
  }
  .white_cont {
    width: 100%;
    height: 400px;
  }
  