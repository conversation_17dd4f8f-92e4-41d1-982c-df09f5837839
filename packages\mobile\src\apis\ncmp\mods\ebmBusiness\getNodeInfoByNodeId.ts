/**
 * @description 根据节点ID查询材料列表和寄件信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** nodeId */
  nodeId: number;
}

export type Result = defs.ncmp.HttpResult<defs.ncmp.EbmBusinessMatShipResp>;
export const path = '/wx-ncmp/ebmbusiness/getNodeInfoByNodeId';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
