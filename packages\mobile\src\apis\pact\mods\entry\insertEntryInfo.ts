/**
 * @description 入职办理-新建入职办理接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.EntryResponseBean;
export const path = '/yc-wepact-mobile/entry/insertEntryInfo';
export const method = 'POST';
export const request = (
  data: defs.pact.EntryParam,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.EntryParam,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
