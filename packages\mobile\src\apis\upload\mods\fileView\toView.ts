/**
 * @description 查看预约业务办理附件NFS
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** accountId */
  accountId: string;
  /** fileName */
  fileName: string;
  /** type */
  type: string;
}

export type Result = any;
export const path = '/wx-upload/view/{type}/{accountId}/{fileName}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
