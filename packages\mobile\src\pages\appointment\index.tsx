import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { withPage } from '@components'
import icon_home_6 from '@assets/appointment/icon-home-6.png'
import icon_home_4 from '@assets/appointment/icon-home-4.png'
import icon_elesign_3 from '@assets/elesign/elesignature.png'
import icon_elesign_4 from '@assets/elesign/nullify.png'
import icon_elesign_5 from '@assets/elesign/view.png'
import styles from './index.module.scss'


const Index = () => {
  const list = [
    {
      title: '员工预约办理',
      img: icon_home_4,
      onClick: () => Taro.navigateTo({ url: '/pages/appointment/appointment-list/index' })
    },
    {
      title: '业务办理查询',
      img: icon_home_6,
      onClick: () => Taro.navigateTo({ url: '/pages/appointment/business-list/index' })
    },
    {
      title: '签署电子业务',
      img: icon_elesign_3,
      onClick: () => {
        Taro.navigateTo({ url: '/pages/appointment/ele-business/index?eleBusinessStatus=3'})
      }
    },
    {
      title: '作废电子业务',
      img: icon_elesign_4,
      onClick: () => {
        Taro.navigateTo({ url: '/pages/appointment/ele-business/index?eleBusinessStatus=4'})
      }
    },
    {
      title: '查看电子业务',
      img: icon_elesign_5,
      onClick: () => {
        Taro.navigateTo({ url: '/pages/appointment/ele-business/index' })
      }
    }
  ]
  return (
    <View className={styles.wrap}>
      {list.map(item => (
        <View className={styles.item} key={item.title} onClick={item.onClick}>
          <Image className={styles.img} src={item.img} />
          <Text className={styles.text}>{item.title}</Text>
        </View>
      ))}
    </View>
  )
}

export default withPage(Index)
