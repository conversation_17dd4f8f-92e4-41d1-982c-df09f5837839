/**
 * @description 获取外勤打卡记录）
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 公司id */
  custId: number;
  /** 日期 */
  date: string;
}

export type Result = defs.ncmp.HttpResult<Array<defs.ncmp.AcCheckLog>>;
export const path = '/wx-ncmp/eos/attendance/checkin/getCheckFieldList';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
