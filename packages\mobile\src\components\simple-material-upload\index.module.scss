.container {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin: 16px 20px;
  padding: 20px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.titleRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.titleLeft {
  display: flex;
  align-items: center;
}

.materialIndex {
  font-size: 32px;
  font-weight: 500;
  color: #333333;
  margin-right: 8px;
  font-family: PingFangSC-Medium;
}

.materialName {
  font-size: 32px;
  font-weight: 500;
  color: #333333;
  font-family: PingFangSC-Medium;
}

.typeLabel {
  background: #e8f4fd;
  color: #1890ff;
  font-size: 24px;
  padding: 4px 12px;
  border-radius: 12px;
  font-family: PingFangSC-Regular;
  border: 1px solid #91d5ff;
}

.fileIconArea {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 20px 0;
  min-height: 120px;
}

.fileIcon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
}

.fileIconText {
  font-size: 32px;
  margin-bottom: 4px;
}

.fileIconLabel {
  font-size: 20px;
  color: #666666;
  text-align: center;
  font-family: PingFangSC-Regular;
}

.deleteIcon {
  position: absolute;
  top: -8px;
  right: calc(50% - 48px);
  width: 24px;
  height: 24px;
  background: #ff4d4f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.deleteIconText {
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
}

.fileInfoArea {
  text-align: center;
  margin: 16px 0;
}

.fileName {
  font-size: 28px;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8px;
  font-family: PingFangSC-Medium;
}

.fileDescription {
  font-size: 24px;
  color: #666666;
  font-family: PingFangSC-Regular;
}

.placeholderText {
  font-size: 26px;
  color: #cccccc;
  font-family: PingFangSC-Regular;
}

.uploadProgress {
  margin: 16px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.progressBar {
  width: 100%;
  height: 4px;
  background: #e8e8e8;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b 0%, #ee5a52 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progressText {
  font-size: 24px;
  color: #666666;
  text-align: center;
  font-family: PingFangSC-Regular;
}

.errorMessage {
  margin: 16px 0;
  padding: 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;
}

.errorText {
  font-size: 26px;
  color: #ff4d4f;
  text-align: center;
  font-family: PingFangSC-Regular;
}

.bottomActions {
  margin-top: 20px;
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  gap: 16px;
  padding: 0 10px;
  width: 100%;
  box-sizing: border-box;
}

/* 当只有下载按钮时居中显示 */
.singleButton {
  justify-content: center !important;
}

.uploadBtn {
  background: #ff6b6b !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 25px !important;
  font-size: 26px !important;
  font-weight: 400 !important;
  font-family: PingFangSC-Regular !important;
  padding: 12px 24px !important;
  min-width: 140px !important;
  height: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex: 1 !important;
  max-width: 180px !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  line-height: 1 !important;

  &:active {
    background: #ee5a52 !important;
  }

  &:disabled {
    background: #d9d9d9 !important;
    color: #ffffff !important;
  }

  &::after {
    display: none !important;
  }
}

.downloadBtn {
  background: #f0f9ff !important;
  color: #1890ff !important;
  border: 1px solid #91d5ff !important;
  border-radius: 25px !important;
  font-size: 26px !important;
  font-weight: 400 !important;
  font-family: PingFangSC-Regular !important;
  padding: 12px 24px !important;
  min-width: 140px !important;
  height: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex: 1 !important;
  max-width: 180px !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  line-height: 1 !important;

  &:active {
    background: #e6f7ff !important;
  }

  &::after {
    display: none !important;
  }
}

.btnIcon {
  margin-right: 8px;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: block;
}

.btnText {
  font-size: 24px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  line-height: 1;
  white-space: nowrap;
}

// 小屏幕适配
@media (max-width: 375px) {
  .container {
    margin: 12px 16px;
    padding: 16px;
  }

  .materialIndex,
  .materialName {
    font-size: 28px;
  }

  .typeLabel {
    font-size: 22px;
    padding: 3px 10px;
  }

  .fileIcon {
    width: 70px;
    height: 70px;
  }

  .fileIconText {
    font-size: 28px;
  }

  .fileIconLabel {
    font-size: 18px;
  }

  .fileName {
    font-size: 26px;
  }

  .fileDescription {
    font-size: 22px;
  }

  .placeholderText {
    font-size: 24px;
  }

  .bottomActions {
    gap: 12px !important;
    padding: 0 8px !important;
    align-items: stretch !important;
  }

  .uploadBtn,
  .downloadBtn {
    font-size: 24px !important;
    padding: 10px 20px !important;
    min-width: 120px !important;
    height: 44px !important;
    border-radius: 22px !important;
    max-width: 160px !important;
    box-sizing: border-box !important;
  }

  .btnIcon {
    margin-right: 6px;
    width: 18px;
    height: 18px;
  }

  .btnText {
    font-size: 22px;
    line-height: 1;
  }

  .deleteIcon {
    width: 20px;
    height: 20px;
    right: calc(50% - 43px);
  }

  .deleteIconText {
    font-size: 14px;
  }
}
