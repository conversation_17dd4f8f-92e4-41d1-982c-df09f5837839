{"origins": [{"originUrl": "http://***********/user-server/v2/api-docs", "name": "users"}, {"originUrl": "http://***********/wx-upload/v2/api-docs", "name": "upload"}, {"originUrl": "http://***********/dynamics-form-service/v2/api-docs", "name": "dynamicsForms"}, {"originUrl": "http://***********/handler-service/v2/api-docs", "name": "handler"}, {"originUrl": "http://***********/yc-hs/v2/api-docs", "name": "hss"}, {"originUrl": "http://***********/yc-wepact-mobile/v2/api-docs", "name": "pact"}, {"originUrl": "http://***********/wx-ncmp/v2/api-docs", "name": "ncmp"}, {"originUrl": "http://***********/enterprise-auth/v2/api-docs", "name": "auth"}], "usingMultipleOrigins": true, "outDir": "../../src/apis", "templatePath": "./pont-template", "mocks": {"enable": true}}