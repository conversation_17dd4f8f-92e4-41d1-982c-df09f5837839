import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleJiLinColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const isEnterFund_jilin = form.watch('isEnterFund_jilin')
  if (column.name === 'isEnterFund_jilin'){
    form.register('isEnterFund_jilin', {value: column.defaultValue})
  }
  if (column.name === 'isEnterFund_jilin') {
    return { ...column, remind: ['1', '是'].includes(isEnterFund_jilin) && column.remind}
  }
  if (column.name === 'bankName_jilin' || column.name === 'bankNo_jilin') {
    return { ...column, isHidden:  ['1', '是', ''].includes(isEnterFund_jilin) }
  }
  if (column.name === 'pfundAccount_jilin') {
    return { ...column, rules:{required: true}, isHidden: ['0', '否', ''].includes(isEnterFund_jilin) }
  }
  return column
}

export { handleJiLinColumn }