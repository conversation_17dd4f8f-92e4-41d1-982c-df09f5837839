import { useRef, useState } from 'react'
import Taro, { useDidShow } from '@tarojs/taro'
import { AtIcon } from 'taro-ui'
import { Icon, Input, View} from '@tarojs/components'
import { BottomBtn, withPage, ListView, usePagination} from '@components'
import { getScrollStyle } from '@utils/index'
import classNames from 'classnames'
import { pact } from '@apis/pact'
import styles from './index.module.scss'


const Index = () => {
  const [selectedValues, setSelectedValues] = useState({
    citys: []
  })
  const [search, setSearch] = useState<any>('')
  const inputRef = useRef<any>(null)
  const scrollStyle = getScrollStyle({ bottom: 120, top: 200,})
  const { CITYS } = Taro.getCurrentInstance()?.preloadData || {}
  const list = usePagination(
    async () => {
      const result:any = await pact.cityInfo.getAllcitys.request({
        cityname: search
      })
      if (!search || '全部城市'.includes(search)){
        result.unshift({name: '全部城市', code: '', isAllCity: 1})
      } 
      return result
    },
    { deps: [search] }
  )
  const handleSelectedCity = item => {
    const idx = selectedValues.citys.findIndex((cattr: any) => {
      return cattr.code === item.code
    })
    let newFilterAttrs: any = []
    newFilterAttrs = selectedValues.citys.slice()
    if (idx !== -1) {
      newFilterAttrs.splice(idx, 1)
      setSelectedValues({
        ...selectedValues,
        citys: newFilterAttrs
      })
    } else {
      newFilterAttrs.push(item)
      let selectedCitys;
      if (!item.code){
        selectedCitys = newFilterAttrs.filter((it:any) => !it.code )
      } else {
        selectedCitys = newFilterAttrs.filter((it:any) => it.code )
      }
      setSelectedValues({
        ...selectedValues,
        citys: selectedCitys
      })
    }
  }
  const confirm = () => {
    Taro.preload({
      ...(Taro.getCurrentInstance().preloadData || {}),
      CITYS: selectedValues.citys,
      PROVINCE: [],
      SPECIALAREA: [],
    })
    Taro.navigateTo({
      url: `/pages/search_filter/index`
    })
  }
  useDidShow(() => {
    if (!CITYS) {
      return
    }
    setSelectedValues({
      ...selectedValues,
      citys: CITYS
    })
  })
  return (
    <View className={styles.wrap1}>
      <View className={styles.header}>
        <View className={styles.input_wrap}>
          <Input className={styles.input_C} placeholder='搜索城市' ref={inputRef} />
          <View
            className={styles.search}
            onClick={() => {
              const value: any = inputRef.current.tmpValue || inputRef.current.value
              setSearch(value)
            }}
          >
            <Icon size='20' type='search' color='#fff' />
          </View>
        </View>
      </View>
      <ListView 
        style={scrollStyle}
        itemSize={100}
        noPull
        renderItem={(item)=>
          <View key={item.code} className={styles.item} onClick={() => handleSelectedCity(item)}>
          <View
            className={classNames(
              selectedValues.citys.some((cAttr: any) => {
                return cAttr.code === item.code
              })
                ? styles.active
                : ''
            )}
          >
            {item.name}
          </View>
          <View>
            {selectedValues.citys.some((cAttr: any) => {
              return cAttr.code === item.code
            }) && <AtIcon size={24} value='check' color='#B51E25' />}
          </View>
        </View>
        }
        {...list}
      />
      <BottomBtn
        btns={[
          {
            title: '确定',
            onClick: () => {
              confirm()
            }
          }
        ]}
      />
    </View>
  )
}
export default withPage(Index, {needLogin: false})
