import { useEffect, useState } from 'react'
import { View, Text, Button, Image } from '@tarojs/components'
import { getGlobalData } from '@utils'
import { withPage } from '@components'
import { useRouter } from '@tarojs/taro'
import { ncmp } from '@apis/ncmp'
import no_data from '@assets/no-data.png'
import styles from './index.module.scss'

const typeMap = {
  '1': '未发起',
  '2': '拟定中',
  '3': '签署中',
  '4': '作废中',
  '5': '已完成',
  '6': '已过期',
  '7': '已撤回',
  '9': '已作废',
  '10': '已发起作废'
}
const getQuitMaterial = ncmp.elecSign.getQuitMaterial
const getQuitCert = ncmp.elecSign.getQuitCert
const Index = () => {
  const [list, setlist] = useState<any[]>([])
  const PlatformInfo = sessionStorage.getItem('PlatformInfo')
  const accountInfo = PlatformInfo && JSON.parse(PlatformInfo)
  let { empId } = getGlobalData<'account'>('account')
  if(!empId) {
    empId = accountInfo?.empId
  }
  const { pageTag = '' } = useRouter().params
  const fetchData = async (service: any) => {
    if (!empId) return
    await service
      .request({ empId }, { isToken: true })
      .then((res:any) => {
        if (res.code === '200') {
          Array.isArray(res?.resultObj) && setlist(res?.resultObj || [])
        } else {
          Taro.showModal({
            title: '提示',
            content: res.errorMsg || '系统异常!',
            showCancel: false
          })
        }
      })
      .catch(() => {
        const title = '系统异常！'
        Taro.showToast({ title, icon: 'none' })
      })
  }
  useEffect(() => {
    if (pageTag === '3') {
      fetchData(getQuitMaterial)
      return
    }
    if (pageTag === '4') {
      fetchData(getQuitCert)
      return
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const toEelSignaturePage = (params = {}) => {
    const service1 = pageTag === '3' ? ncmp.elecSign.getQuitMaterialUrl : ncmp.elecSign.getQuitCertlUrl
    service1
      .request(params, { isToken: true })
      .then(res => {
        if (res.code === '200') {
          const eleSinUrl = res?.resultObj?.eleSinUrl || ''
          window.location.href = eleSinUrl
        }
      })
      .catch(() => {
        const title = '系统异常！'
        Taro.showToast({ title, icon: 'none' })
      })
  }
  return (
    <View className={styles.wrap}>
      {list.length > 0 ? (
        list.map((item: any) => (
          <View
            className={styles.item}
            key={pageTag === '3' ? item?.materialEleName : item?.certificateElename}
            onClick={() => {
              toEelSignaturePage(item)
            }}
          >
            <Text className={styles.eleContractName}>
              {pageTag === '3' ? item?.materialEleName : item?.certificateElename}
            </Text>
            <Button className={styles.eleContractStatus}>
              {typeMap[pageTag === '3' ? item?.materialStatus : item?.certificateStatus]}
            </Button>
          </View>
        ))
      ) : (
        <View className={styles.empty}>
          <Image className={styles.no_data} src={no_data} />
          <Text className={styles.no_text}>您没有可以查看的电子合同</Text>
        </View>
      )}
    </View>
  )
}

export default withPage(Index)
