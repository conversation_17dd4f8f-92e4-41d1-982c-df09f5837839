import { FunctionComponent } from 'react'
import { View, Text } from '@tarojs/components'
import styles from './index.module.scss'

interface Labels {
  title?: string
  detail?: string
}

const Labels: FunctionComponent<Labels> = props => {
  const { title, detail } = props
  return (
    <View className={styles.item}>
      <Text className={styles.title}>{title}</Text>
      <Text className={styles.detail}>{detail}</Text>
    </View>
  )
}

export default Labels
