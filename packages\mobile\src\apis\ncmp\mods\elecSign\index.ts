/**
 * @description Elec Sign Controller
 */
import * as certFileUpload from './certFileUpload';
import * as certFileUploadByForm from './certFileUploadByForm';
import * as delQuitMaterial from './delQuitMaterial';
import * as deteleLaborSupplyCertHz from './deteleLaborSupplyCertHz';
import * as getEleContractList from './getEleContractList';
import * as getEleContractUrl from './getEleContractUrl';
import * as getHzSupplyCountByEmpId from './getHzSupplyCountByEmpId';
import * as getQuitCert from './getQuitCert';
import * as getQuitCertlUrl from './getQuitCertlUrl';
import * as getQuitMaterial from './getQuitMaterial';
import * as getQuitMaterialUrl from './getQuitMaterialUrl';
import * as getSignEleContract from './getSignEleContract';
import * as queryHzSupplyCert from './queryHzSupplyCert';
import * as signQuitMaterial from './signQuitMaterial';
import * as submitCert from './submitCert';
import * as toViewMinio from './toViewMinio';

export {
  certFileUpload,
  certFileUploadByForm,
  delQuitMaterial,
  deteleLaborSupplyCertHz,
  getEleContractList,
  getEleContractUrl,
  getHzSupplyCountByEmpId,
  getQuitCert,
  getQuitCertlUrl,
  getQuitMaterial,
  getQuitMaterialUrl,
  getSignEleContract,
  queryHzSupplyCert,
  signQuitMaterial,
  submitCert,
  toViewMinio,
};
