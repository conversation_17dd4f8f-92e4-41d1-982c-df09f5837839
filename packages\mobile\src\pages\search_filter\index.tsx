import { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import { ScrollView, View } from '@tarojs/components'
import { withPage } from '@components/page'
import { getScrollStyle } from '@utils/index'
import classNames from 'classnames'
import BottomBtn from '@components/bottom-btn'
import styles from './index.module.scss'

const Index = () => {
  const [currentStatusIndex, setCurrentStatusIndex] = useState<number>(0)
  const [yearAttrs, setYearAttrs] = useState<any>([])
  const currentYear = new Date().getFullYear()
  const [selectedValues, setSelectedValues] = useState<any>({
    yearAttrs: [],
    serviceAttrs: [],
    zCAttrs: [],
    statusAttrs: {}
  })
  const serviceAttrs = [
    {
      id: 3,
      name: '派遣外包员工、单位自有员工'
    },
    {
      id: 2,
      name: '单位自有员工'
    },
    {
      id: 1,
      name: '派遣外包员工'
    }
  ]
  const zCAttrs = [
    {
      id: 1,
      name: '国家政策'
    },
    {
      id: 2,
      name: '省份政策'
    },
    {
      id: 3,
      name: '城市政策'
    },
    {
      id: 4,
      name: '特区政策'
    }
  ]
  const statusAttrs = [
    {
      id: 0,
      value: 2,
      name: '生效'
    },
    {
      id: 1,
      value: 3,
      name: '失效'
    }
  ]
  const selectedValue: any = {
    yearAttrs: [{id: currentYear, name: currentYear}],
    serviceAttrs: serviceAttrs,
    zCAttrs: zCAttrs,
    statusAttrs: { id: 1, name: '生效' }
  }
  const scrollStyle = getScrollStyle({ bottom: 120, top: 30 })
  const { CITYS, PROVINCE, SPECIALAREA, SELECTEDVALUES } = Taro.getCurrentInstance()?.preloadData || {}

  useEffect(() => {
    const yearArray: any = []
    for (let i = 0; i <= 4; i++) {
      yearArray.push({ name: currentYear - i, id: currentYear - i })
    }
    setYearAttrs(yearArray)
    setCurrentStatusIndex(SELECTEDVALUES?.statusAttrs.id || 0)
    setSelectedValues({
      ...selectedValues,
      yearAttrs: SELECTEDVALUES?.yearAttrs || selectedValue.yearAttrs,
      serviceAttrs: SELECTEDVALUES?.serviceAttrs || selectedValue.serviceAttrs,
      zCAttrs: SELECTEDVALUES?.zCAttrs || selectedValue.zCAttrs,
      statusAttrs: statusAttrs[SELECTEDVALUES?.statusAttrs?.id || 0]
    })
  }, [])
  const filterAttrsHandler = (attr: any, attrsName: any) => {
    const idx = selectedValues[attrsName]?.findIndex((cattr: any) => {
      return cattr.id === attr.id
    })
    let newFilterAttrs: any = []
    newFilterAttrs = selectedValues[attrsName]?.slice()
    if (idx !== -1) {
      newFilterAttrs.length > 1 && newFilterAttrs?.splice(idx, 1)
      setSelectedValues({
        ...selectedValues,
        [attrsName]: newFilterAttrs
      })
    } else {
      newFilterAttrs.push(attr)
      setSelectedValues({
        ...selectedValues,
        [attrsName]: newFilterAttrs
      })
    }
    console.log('selectedValues', selectedValues)
  }
  const handleStatusClick = (item, index) => {
    setCurrentStatusIndex(index)
    setSelectedValues({
      ...selectedValues,
      statusAttrs: item
    })
  }
  const reset = () => {
    Taro.preload({
      ...(Taro.getCurrentInstance().preloadData || {}),
      CITYS: [],
      PROVINCE: [],
      SPECIALAREA: []
    })
    setCurrentStatusIndex(0)
    setSelectedValues({
      ...selectedValues,
      yearAttrs: selectedValue.yearAttrs,
      serviceAttrs: selectedValue.serviceAttrs,
      zCAttrs: selectedValue.zCAttrs,
      statusAttrs: statusAttrs[0]
    })
  }
  const confirm = () => {
    Taro.preload({
      ...(Taro.getCurrentInstance()?.preloadData || {}),
      SELECTEDVALUES: selectedValues,
      LEVEL1ID: undefined,
      LEVEL2ID: undefined
    })
    Taro.navigateTo({
      url: `/pages/policy_query/index`
    })
  }
  return (
    <View>
      <ScrollView style={scrollStyle} scrollY>
        <View>
          <View className={styles.opts_title}>所属年份</View>
          <View className={styles.opts}>
          {yearAttrs.map(attr => {
              return (
                <View
                  className={classNames(
                    styles.opts_container,
                    selectedValues.yearAttrs.some((cAttr: any) => {
                      return cAttr.id === attr.id
                    })
                      ? styles.active
                      : ''
                  )}
                  key={attr.id}
                  onClick={() => filterAttrsHandler(attr, 'yearAttrs')}
                >
                   {`${attr.name}年`}
                </View>
              )
            })}
          </View>
        </View>
        <View>
          <View className={styles.opts_title}>服务类型</View>
          <View className={styles.opts}>
            {serviceAttrs.map(attr => {
              return (
                <View
                  className={classNames(
                    styles.opts_container,
                    selectedValues.serviceAttrs.some((cAttr: any) => {
                      return cAttr.id === attr.id
                    })
                      ? styles.active
                      : ''
                  )}
                  key={attr.id}
                  onClick={() => filterAttrsHandler(attr, 'serviceAttrs')}
                >
                  {attr.name}
                </View>
              )
            })}
          </View>
        </View>
        <View>
          <View className={styles.opts_title}>生效状态</View>
          <View className={styles.opts}>
            {statusAttrs.map((attr, idx) => {
              return (
                <View
                  className={classNames(styles.opts_container, currentStatusIndex === idx && styles.active)}
                  key={attr.id}
                  onClick={() => handleStatusClick(attr, idx)}
                >
                  {attr.name}
                </View>
              )
            })}
          </View>
        </View>
        <View className={styles.opts_title}>政策范围</View>
        <View className={styles.opts}>
          {zCAttrs.map(attr => {
            return (
              <View
                className={classNames(
                  styles.opts_container,
                  selectedValues.zCAttrs.some((cAttr: any) => {
                    return cAttr.id === attr.id
                  })
                    ? styles.active
                    : ''
                )}
                key={attr.id}
                onClick={() => {
                  Taro.preload({
                    ...(Taro.getCurrentInstance().preloadData || {}),
                    CITYS: [],
                    PROVINCE: [],
                    SPECIALAREA: []
                  })
                  filterAttrsHandler(attr, 'zCAttrs')
                }}
              >
                {attr.name}
              </View>
            )
          })}
        </View>
        {selectedValues.zCAttrs.length === 1 && selectedValues.zCAttrs[0]?.id === 3 && (
          <>
            <View
              className={styles.opts_title}
              onClick={() => {
                Taro.preload({
                  ...(Taro.getCurrentInstance().preloadData || {}),
                  SELECTEDVALUES: selectedValues
                })
                Taro.navigateTo({
                  url: `/pages/search_city/index`
                })
              }}
            >
              <View>选择城市</View>
              <View className={styles.opts_choose}>点击选择</View>
            </View>
            <View className={styles.opts}>
              {CITYS?.length > 0 &&
                CITYS?.map(it => {
                  return (
                    it && (
                      <View className={styles.opts_container} key={it?.code}>
                        {it?.name}
                      </View>
                    )
                  )
                })}
            </View>
          </>
        )}
        {selectedValues.zCAttrs.length === 1 && selectedValues.zCAttrs[0]?.id === 2 && (
          <>
            <View
              className={styles.opts_title}
              onClick={() => {
                Taro.preload({
                  ...(Taro.getCurrentInstance().preloadData || {}),
                  SELECTEDVALUES: selectedValues
                })
                Taro.navigateTo({
                  url: `/pages/search_province/index`
                })
              }}
            >
              <View>选择省份</View>
              <View className={styles.opts_choose}>点击选择</View>
            </View>
            <View className={styles.opts}>
              {PROVINCE?.length > 0 &&
                PROVINCE?.map(it => {
                  return (
                    it && (
                      <View className={styles.opts_container} key={it?.KEY}>
                        {it?.SHORTNAME}
                      </View>
                    )
                  )
                })}
            </View>
          </>
        )}
        {selectedValues.zCAttrs.length === 1 && selectedValues.zCAttrs[0]?.id === 4 && (
          <>
            <View
              className={styles.opts_title}
              onClick={() => {
                Taro.preload({
                  ...(Taro.getCurrentInstance().preloadData || {}),
                  SELECTEDVALUES: selectedValues
                })
                Taro.navigateTo({
                  url: `/pages/search_specialArea/index`
                })
              }}
            >
              <View>选择特区</View>
              <View className={styles.opts_choose}>点击选择</View>
            </View>
            <View className={styles.opts}>
              {SPECIALAREA?.length > 0 &&
                SPECIALAREA?.map(it => {
                  return (
                    it && (
                      <View className={styles.opts_container} key={it?.KEY}>
                        {it?.SHORTNAME}
                      </View>
                    )
                  )
                })}
            </View>
          </>
        )}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '重置',
            onClick: () => {
              reset()
            }
          },
          {
            title: '确定',
            onClick: () => {
              confirm()
            }
          }
        ]}
      />
    </View>
  )
}
export default withPage(Index, {needLogin: false})
