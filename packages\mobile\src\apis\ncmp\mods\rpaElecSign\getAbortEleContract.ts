/**
 * @description 获取待作废的电子签合同, 告警code: getLinkForAbort
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpRpaResult<defs.ncmp.ElecSignResult>;
export const path = '/wx-ncmp/rpa/elecsign/getLinkForAbort';
export const method = 'POST';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
