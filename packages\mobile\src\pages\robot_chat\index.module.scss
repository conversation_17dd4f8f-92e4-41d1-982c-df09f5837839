.container {
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .loadingContainer {
    text-align: center;
  }
  
  .loadingSpinner {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .loadingText {
    margin-top: 16px;
    color: #666;
    font-size: 28px;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }