.header {
  position: absolute;
  padding: 20px;
  background-color: rgb(230, 230, 238);
  left: 0px;
  right: 0px;
  top: 0px;
  height: 150px;
}

.search {
  background-color: #b51e25;
  border-radius: 5px;
  width: 72px;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header_title {
  font-size: 28px;
}
.header_city {
  color: #b51e25;
  font-weight: 400;
  font-size: 28px;
}
.input_wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
}
.input_C {
  background-color: #ffffff;
  border-radius: 4px;
  height: 72px;
  width: 620px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
  font-size: 28px;
  color: #333;
}

.item {
  height: 100px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border: 0 solid #efeff4;
  border-bottom-width: 1px;
  padding-left: 40px;
  padding-right: 40px;
}

.item_title {
  font-size: 28px;
  color: #333;
  margin-left: 30px;
}
.active {
  color: #b51e25;
}
.empty {
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80vh;
  }
  .no_data {
    width: 160px;
    height: 103px;
  }
  
  .no_text {
    color: #d9d9d9;
    font-size: 28px;
  }