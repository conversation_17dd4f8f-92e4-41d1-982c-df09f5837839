.header {
  background-color: rgb(239, 239, 244);
  padding: 30px;
  border: 0px solid #d7d7d7;
  border-bottom-width: 1px;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}

.img1 {
  width: 26px;
  height: 26px;
  margin-top: 4px;
  margin-right: 10px;
}

.img2 {
  width: 26px;
  height: 26px;
  margin-top: 4px;
  margin-right: 10px;
}

.detail1 {
  font-size: 20px;
  width: 360px;
  margin-right: 8px;
  color: #666;
}

.detail2 {
  font-size: 20px;
  width: 250px;
  color: #666;
}

.title {
  font-size: 30px;
  display: flex;
  align-items: center;
  flex-direction: row;
  width: 690px;
  margin-bottom: 20px;
  color: #333;
}
.content {
  margin: 40px;
}
.infotitle {
  font-size: 30px;
  display: flex;
  align-items: center;
  flex-direction: row;
}
.flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  color: #333;
}
.title1{
  font-size: 30px;
  color: #000;
}
.text1{
  width: 50%;
  font-size: 24px;
}
.text2{
  font-size: 24px;
  padding-left:20px;
  word-break: break-all;
  text-align: right;
}
.empty {
  height: 100%;
  flex-direction: column;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -20%;
}
.no_data {
  width: 160px;
  height: 103px;
  margin-bottom: 40px;
}

.no_text {
  color: #d9d9d9;
  font-size: 28px;
}


