import Taro from "@tarojs/taro";
import styles from "./index.module.scss";
import SuccessImg from "@assets/attendance/success.png";

const Index = () => {
  const endtime = Taro.getCurrentInstance()?.router?.params.endtime;
  return (
    <div className={styles.containers}>
      <view className={styles.wrapper}>
        <view className={styles.box_wrap}>
          <img className={styles.success} src={SuccessImg} />
          <view className={styles.times}>{endtime}</view>
          <view className={styles.suc}>打卡成功</view>
        </view>
      </view>
    </div>
  );
};

export default Index;
