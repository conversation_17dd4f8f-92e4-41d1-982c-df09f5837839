import { useState, useEffect } from 'react'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { View, Image } from '@tarojs/components'
import { withPage } from '@components'
import { ncmp } from '@apis/ncmp'
import clockIn from '@assets/attendance/location_active.png'
import calendar from '@assets/attendance/calendar.png'
import leave from '@assets/attendance/leave-active.png'
import styles from './index.module.scss'


const Index = () => {
  const { custId, custName, empName } = useRouter().params
  const handleGetIsHasCheck = (pages: string) => {
    if (!custId) return;

    const navigateToPage = () => {
      Taro.navigateTo({ url: `/pages/attendance/${pages}/index?custId=${custId}` });
    };

    if (pages === 'leave') {
      navigateToPage();
      return;
    }

    ncmp.eosCheckIn.getIsHasCheck.request({ custId: Number(custId) }).then((res) => {
      if (res.code !== '200') return;

      const { hasLocation, hasRule } = res.resultObj;
      if (!hasLocation) {
        Taro.showToast({ title: '打卡前请先设置考勤地点', icon: 'none' });
        return;
      }
      if (!hasRule) {
        Taro.showToast({ title: '请联系系统管理员设置对应考勤规则', icon: 'none' });
        return;
      }
      navigateToPage();
    });
  }
  return (
    <View className={styles.home}>
      <View className={styles.companyName}>
        <View className={styles.name}>{empName}</View>
        <View className={styles.company}>{custName}</View>
      </View>
      <View className={styles.attendance}>
        <View className={styles.item} onClick={() => handleGetIsHasCheck('clockin')}><Image className={styles.img} src={clockIn} /><View className={styles.clockIn}>打卡</View></View>
        <View className={styles.item} onClick={() => handleGetIsHasCheck('clocking-in')}><Image className={styles.img} src={calendar} /><View className={styles.clockIn}>考勤</View></View>
        <View className={styles.item} onClick={() => handleGetIsHasCheck('leave')}><Image className={styles.img} src={leave} /><View className={styles.clockIn}>请假</View></View>
      </View>
    </View>
  )
}

export default withPage(Index)
