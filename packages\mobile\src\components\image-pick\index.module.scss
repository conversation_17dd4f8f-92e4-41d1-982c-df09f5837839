.wrap {
  width: 750;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  overflow: hidden;
  padding: 15px;
}


.item {
  position: relative;
  width: 210px;
  height: 280px;
  border: 1px solid #efeff4;
  border-radius: 8px;
  margin: 15px;
}

.content {
  margin: 20px;
  height:170px;
  width: 170px;
  border: 1px dashed #efeff4;
  display: flex;
  justify-content: center;
  align-items: center;
}

.title {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 26px;
  margin-top: 20px;
}

.add {
  width: 50px;
  height: 50px;
}

.delete {
  position: absolute;
  width: 50px;
  height: 50px;
  top: 60px;
  right: 10px;
  z-index: 10;
}


.img {
  height:170px;
  width: 170px;
}
