PODS:
  - boost-for-react-native (1.63.0)
  - B<PERSON><PERSON>inearGradient (2.5.6):
    - React
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - EXAV (8.6.0):
    - UMCore
    - UMFileSystemInterface
    - UMPermissionsInterface
  - EXBarCodeScanner (9.0.0):
    - UMCore
    - UMImageLoaderInterface
    - UMPermissionsInterface
    - ZXingObjC/OneD
    - ZXingObjC/PDF417
  - EXBrightness (8.4.0):
    - UMCore
    - UMPermissionsInterface
  - EXCamera (9.0.0):
    - UMBarCodeScannerInterface
    - UMCore
    - UMFaceDetectorInterface
    - UMFileSystemInterface
    - UMImageLoaderInterface
    - UMPermissionsInterface
  - EXConstants (9.2.0):
    - UMConstantsInterface
    - UMCore
  - EXFileSystem (9.2.0):
    - UMCore
    - UMFileSystemInterface
  - EXImageLoader (1.2.0):
    - React-Core
    - UMCore
    - UMImageLoaderInterface
  - EXImagePicker (9.1.1):
    - UMCore
    - UMFileSystemInterface
    - UMPermissionsInterface
  - EXKeepAwake (8.4.0):
    - UMCore
  - EXLocation (9.0.1):
    - UMCore
    - UMPermissionsInterface
    - UMTaskManagerInterface
  - EXPermissions (9.3.0):
    - UMCore
    - UMPermissionsInterface
  - EXSensors (9.2.0):
    - UMCore
    - UMSensorsInterface
  - FBLazyVector (0.64.2)
  - FBReactNativeSpec (0.64.2):
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.2)
    - RCTTypeSafety (= 0.64.2)
    - React-Core (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - Flipper (0.75.1):
    - Flipper-Folly (~> 2.5)
    - Flipper-RSocket (~> 1.3)
  - Flipper-DoubleConversion (1.1.7)
  - Flipper-Folly (2.5.3):
    - boost-for-react-native
    - Flipper-DoubleConversion
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.180)
  - Flipper-Glog (0.3.6)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.3.1):
    - Flipper-Folly (~> 2.5)
  - FlipperKit (0.75.1):
    - FlipperKit/Core (= 0.75.1)
  - FlipperKit/Core (0.75.1):
    - Flipper (~> 0.75.1)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
  - FlipperKit/CppBridge (0.75.1):
    - Flipper (~> 0.75.1)
  - FlipperKit/FBCxxFollyDynamicConvert (0.75.1):
    - Flipper-Folly (~> 2.5)
  - FlipperKit/FBDefines (0.75.1)
  - FlipperKit/FKPortForwarding (0.75.1):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.75.1)
  - FlipperKit/FlipperKitLayoutPlugin (0.75.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.75.1)
  - FlipperKit/FlipperKitNetworkPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.75.1):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.75.1):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - glog (0.3.5)
  - libevent (2.1.12)
  - libwebp (1.2.0):
    - libwebp/demux (= 1.2.0)
    - libwebp/mux (= 1.2.0)
    - libwebp/webp (= 1.2.0)
  - libwebp/demux (1.2.0):
    - libwebp/webp
  - libwebp/mux (1.2.0):
    - libwebp/demux
  - libwebp/webp (1.2.0)
  - OpenSSL-Universal (1.1.180)
  - RCT-Folly (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
    - RCT-Folly/Default (= 2020.01.13.00)
  - RCT-Folly/Default (2020.01.13.00):
    - boost-for-react-native
    - DoubleConversion
    - glog
  - RCTRequired (0.64.2)
  - RCTTypeSafety (0.64.2):
    - FBLazyVector (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTRequired (= 0.64.2)
    - React-Core (= 0.64.2)
  - React (0.64.2):
    - React-Core (= 0.64.2)
    - React-Core/DevSupport (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-RCTActionSheet (= 0.64.2)
    - React-RCTAnimation (= 0.64.2)
    - React-RCTBlob (= 0.64.2)
    - React-RCTImage (= 0.64.2)
    - React-RCTLinking (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - React-RCTSettings (= 0.64.2)
    - React-RCTText (= 0.64.2)
    - React-RCTVibration (= 0.64.2)
  - React-callinvoker (0.64.2)
  - React-Core (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/CoreModulesHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/Default (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/DevSupport (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-jsinspector (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTBlobHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTImageHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTTextHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-Core/RCTWebSocket (0.64.2):
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/Default (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsiexecutor (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - Yoga
  - React-CoreModules (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/CoreModulesHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTImage (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-cxxreact (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-jsinspector (= 0.64.2)
    - React-perflogger (= 0.64.2)
    - React-runtimeexecutor (= 0.64.2)
  - React-jsi (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-jsi/Default (= 0.64.2)
  - React-jsi/Default (0.64.2):
    - boost-for-react-native (= 1.63.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
  - React-jsiexecutor (0.64.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-perflogger (= 0.64.2)
  - React-jsinspector (0.64.2)
  - react-native-cameraroll (4.0.4):
    - React-Core
  - react-native-flipper (0.103.0):
    - React-Core
  - react-native-geolocation (2.0.2):
    - React
  - react-native-image-resizer (1.4.5):
    - React-Core
  - react-native-netinfo (5.9.10):
    - React-Core
  - react-native-pager-view (5.4.0):
    - React-Core
  - react-native-safe-area-context (3.2.0):
    - React-Core
  - react-native-segmented-control (2.2.2):
    - React-Core
  - react-native-slider (4.0.0-rc.3):
    - React-Core
  - react-native-webview (11.6.5):
    - React-Core
  - React-perflogger (0.64.2)
  - React-RCTActionSheet (0.64.2):
    - React-Core/RCTActionSheetHeaders (= 0.64.2)
  - React-RCTAnimation (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTAnimationHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTBlob (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTBlobHeaders (= 0.64.2)
    - React-Core/RCTWebSocket (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTImage (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTImageHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-RCTNetwork (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTLinking (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - React-Core/RCTLinkingHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTNetwork (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTNetworkHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTSettings (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - RCTTypeSafety (= 0.64.2)
    - React-Core/RCTSettingsHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-RCTText (0.64.2):
    - React-Core/RCTTextHeaders (= 0.64.2)
  - React-RCTVibration (0.64.2):
    - FBReactNativeSpec (= 0.64.2)
    - RCT-Folly (= 2020.01.13.00)
    - React-Core/RCTVibrationHeaders (= 0.64.2)
    - React-jsi (= 0.64.2)
    - ReactCommon/turbomodule/core (= 0.64.2)
  - React-runtimeexecutor (0.64.2):
    - React-jsi (= 0.64.2)
  - ReactCommon/turbomodule/core (0.64.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2020.01.13.00)
    - React-callinvoker (= 0.64.2)
    - React-Core (= 0.64.2)
    - React-cxxreact (= 0.64.2)
    - React-jsi (= 0.64.2)
    - React-perflogger (= 0.64.2)
  - RNCAsyncStorage (1.15.5):
    - React-Core
  - RNCClipboard (1.5.1):
    - React-Core
  - RNCMaskedView (0.1.11):
    - React
  - RNCPicker (1.16.4):
    - React-Core
  - RNFastImage (8.3.7):
    - React-Core
    - SDWebImage (~> 5.8)
    - SDWebImageWebPCoder (~> 0.6.1)
  - RNGestureHandler (1.10.3):
    - React-Core
  - RNReanimated (1.13.3):
    - React-Core
  - RNScreens (2.18.1):
    - React-Core
  - RNSVG (12.1.1):
    - React
  - RNSyanImagePicker (0.4.10):
    - React
    - TZImagePickerController
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.6.1):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.7)
  - TZImagePickerController (3.5.2)
  - UMAppLoader (1.3.0)
  - UMBarCodeScannerInterface (5.3.0)
  - UMCameraInterface (5.3.0)
  - UMConstantsInterface (5.3.0)
  - UMCore (5.5.1)
  - UMFaceDetectorInterface (5.3.0)
  - UMFileSystemInterface (5.3.0)
  - UMFontInterface (5.3.0)
  - UMImageLoaderInterface (5.3.0)
  - UMPermissionsInterface (5.3.0):
    - UMCore
  - UMReactNativeAdapter (5.6.0):
    - React-Core
    - UMCore
    - UMFontInterface
  - UMSensorsInterface (5.3.0)
  - UMTaskManagerInterface (5.3.0)
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)
  - ZXingObjC/Core (3.6.5)
  - ZXingObjC/OneD (3.6.5):
    - ZXingObjC/Core
  - ZXingObjC/PDF417 (3.6.5):
    - ZXingObjC/Core

DEPENDENCIES:
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXAV (from `../node_modules/expo-av/ios`)
  - EXBarCodeScanner (from `../node_modules/expo-barcode-scanner/ios`)
  - EXBrightness (from `../node_modules/expo-brightness/ios`)
  - EXCamera (from `../node_modules/expo-camera/ios`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXFileSystem (from `../node_modules/expo-file-system/ios`)
  - EXImageLoader (from `../node_modules/expo-image-loader/ios`)
  - EXImagePicker (from `../node_modules/expo-image-picker/ios`)
  - EXKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - EXLocation (from `../node_modules/expo-location/ios`)
  - EXPermissions (from `../node_modules/expo-permissions/ios`)
  - EXSensors (from `../node_modules/expo-sensors/ios`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (~> 0.75.1)
  - Flipper-DoubleConversion (= 1.1.7)
  - Flipper-Folly (~> 2.5.3)
  - Flipper-Glog (= 0.3.6)
  - Flipper-PeerTalk (~> 0.0.4)
  - Flipper-RSocket (~> 1.3)
  - FlipperKit (~> 0.75.1)
  - FlipperKit/Core (~> 0.75.1)
  - FlipperKit/CppBridge (~> 0.75.1)
  - FlipperKit/FBCxxFollyDynamicConvert (~> 0.75.1)
  - FlipperKit/FBDefines (~> 0.75.1)
  - FlipperKit/FKPortForwarding (~> 0.75.1)
  - FlipperKit/FlipperKitHighlightOverlay (~> 0.75.1)
  - FlipperKit/FlipperKitLayoutPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitLayoutTextSearchable (~> 0.75.1)
  - FlipperKit/FlipperKitNetworkPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitReactPlugin (~> 0.75.1)
  - FlipperKit/FlipperKitUserDefaultsPlugin (~> 0.75.1)
  - FlipperKit/SKIOSNetworkPlugin (~> 0.75.1)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - "react-native-cameraroll (from `../node_modules/@react-native-community/cameraroll`)"
  - react-native-flipper (from `../node_modules/react-native-flipper`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - react-native-image-resizer (from `../node_modules/react-native-image-resizer`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-segmented-control (from `../node_modules/@react-native-community/segmented-control`)"
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-community/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-community/masked-view`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNSyanImagePicker (from `../node_modules/react-native-syan-image-picker`)
  - UMAppLoader (from `../node_modules/unimodules-app-loader/ios`)
  - UMBarCodeScannerInterface (from `../node_modules/unimodules-barcode-scanner-interface/ios`)
  - UMCameraInterface (from `../node_modules/unimodules-camera-interface/ios`)
  - UMConstantsInterface (from `../node_modules/unimodules-constants-interface/ios`)
  - "UMCore (from `../node_modules/@unimodules/core/ios`)"
  - UMFaceDetectorInterface (from `../node_modules/unimodules-face-detector-interface/ios`)
  - UMFileSystemInterface (from `../node_modules/unimodules-file-system-interface/ios`)
  - UMFontInterface (from `../node_modules/unimodules-font-interface/ios`)
  - UMImageLoaderInterface (from `../node_modules/unimodules-image-loader-interface/ios`)
  - UMPermissionsInterface (from `../node_modules/unimodules-permissions-interface/ios`)
  - "UMReactNativeAdapter (from `../node_modules/@unimodules/react-native-adapter/ios`)"
  - UMSensorsInterface (from `../node_modules/unimodules-sensors-interface/ios`)
  - UMTaskManagerInterface (from `../node_modules/unimodules-task-manager-interface/ios`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - boost-for-react-native
    - CocoaAsyncSocket
    - Flipper
    - Flipper-DoubleConversion
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - libevent
    - libwebp
    - OpenSSL-Universal
    - SDWebImage
    - SDWebImageWebPCoder
    - TZImagePickerController
    - YogaKit
    - ZXingObjC

EXTERNAL SOURCES:
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXAV:
    :path: "../node_modules/expo-av/ios"
  EXBarCodeScanner:
    :path: "../node_modules/expo-barcode-scanner/ios"
  EXBrightness:
    :path: "../node_modules/expo-brightness/ios"
  EXCamera:
    :path: "../node_modules/expo-camera/ios"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  EXImageLoader:
    :path: "../node_modules/expo-image-loader/ios"
  EXImagePicker:
    :path: "../node_modules/expo-image-picker/ios"
  EXKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  EXLocation:
    :path: "../node_modules/expo-location/ios"
  EXPermissions:
    :path: "../node_modules/expo-permissions/ios"
  EXSensors:
    :path: "../node_modules/expo-sensors/ios"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-community/cameraroll"
  react-native-flipper:
    :path: "../node_modules/react-native-flipper"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-image-resizer:
    :path: "../node_modules/react-native-image-resizer"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-segmented-control:
    :path: "../node_modules/@react-native-community/segmented-control"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-community/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-community/masked-view"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNSyanImagePicker:
    :path: "../node_modules/react-native-syan-image-picker"
  UMAppLoader:
    :path: "../node_modules/unimodules-app-loader/ios"
  UMBarCodeScannerInterface:
    :path: "../node_modules/unimodules-barcode-scanner-interface/ios"
  UMCameraInterface:
    :path: "../node_modules/unimodules-camera-interface/ios"
  UMConstantsInterface:
    :path: "../node_modules/unimodules-constants-interface/ios"
  UMCore:
    :path: "../node_modules/@unimodules/core/ios"
  UMFaceDetectorInterface:
    :path: "../node_modules/unimodules-face-detector-interface/ios"
  UMFileSystemInterface:
    :path: "../node_modules/unimodules-file-system-interface/ios"
  UMFontInterface:
    :path: "../node_modules/unimodules-font-interface/ios"
  UMImageLoaderInterface:
    :path: "../node_modules/unimodules-image-loader-interface/ios"
  UMPermissionsInterface:
    :path: "../node_modules/unimodules-permissions-interface/ios"
  UMReactNativeAdapter:
    :path: "../node_modules/@unimodules/react-native-adapter/ios"
  UMSensorsInterface:
    :path: "../node_modules/unimodules-sensors-interface/ios"
  UMTaskManagerInterface:
    :path: "../node_modules/unimodules-task-manager-interface/ios"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost-for-react-native: 39c7adb57c4e60d6c5479dd8623128eb5b3f0f2c
  BVLinearGradient: e3aad03778a456d77928f594a649e96995f1c872
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: cf9b38bf0b2d048436d9a82ad2abe1404f11e7de
  EXAV: 1f679b57367f8889b5ea15067413c9e2d95039fb
  EXBarCodeScanner: 1a76d3dc0dae2799af6012e3e4966100e4221a94
  EXBrightness: 42f80704aa91706f4230c218789e32b798ab2fae
  EXCamera: 05687ee8f0618820d3b45e54b129a1a535537e8c
  EXConstants: f486f6b4a36b86e47ab258d4d2b7b2699786fdce
  EXFileSystem: afe9b1fd937d30270bf5108ba44e2f4a1d1ad694
  EXImageLoader: 7153fb1307ac643299a9072b71da0d276f4c7789
  EXImagePicker: f2fe9ee19a97b225658e80733896275a198c60f2
  EXKeepAwake: 615c5a6fca5c2afedf611bb6f7f2ca323246de94
  EXLocation: 90232c6f2773b04a3c146bfda9f181035722c10a
  EXPermissions: 30cbe5b72bd209b65c00884180ad058a60bb413d
  EXSensors: d7829297e383600b38d4bf22b2137097e6748358
  FBLazyVector: e686045572151edef46010a6f819ade377dfeb4b
  FBReactNativeSpec: 17d58d35790bbdf4a63fdfa77c39f0744b19a192
  Flipper: d3da1aa199aad94455ae725e9f3aa43f3ec17021
  Flipper-DoubleConversion: 38631e41ef4f9b12861c67d17cb5518d06badc41
  Flipper-Folly: 755929a4f851b2fb2c347d533a23f191b008554c
  Flipper-Glog: 1dfd6abf1e922806c52ceb8701a3599a79a200a6
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: 127954abe8b162fcaf68d2134d34dc2bd7076154
  FlipperKit: 8a20b5c5fcf9436cac58551dc049867247f64b00
  glog: 73c2498ac6884b13ede40eda8228cb1eee9d9d62
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 6bac0a99ba5a6bb7449a951ff22c484201985f68
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  RCT-Folly: ec7a233ccc97cc556cf7237f0db1ff65b986f27c
  RCTRequired: 6d3e854f0e7260a648badd0d44fc364bc9da9728
  RCTTypeSafety: c1f31d19349c6b53085766359caac425926fafaa
  React: bda6b6d7ae912de97d7a61aa5c160db24aa2ad69
  React-callinvoker: 9840ea7e8e88ed73d438edb725574820b29b5baa
  React-Core: b5e385da7ce5f16a220fc60fd0749eae2c6120f0
  React-CoreModules: 17071a4e2c5239b01585f4aa8070141168ab298f
  React-cxxreact: 9be7b6340ed9f7c53e53deca7779f07cd66525ba
  React-jsi: 67747b9722f6dab2ffe15b011bcf6b3f2c3f1427
  React-jsiexecutor: 80c46bd381fd06e418e0d4f53672dc1d1945c4c3
  React-jsinspector: cc614ec18a9ca96fd275100c16d74d62ee11f0ae
  react-native-cameraroll: 88f4e62d9ecd0e1f253abe4f685474f2ea14bfa2
  react-native-flipper: 169e8ba429b73ad637ce007337ce4b415e783799
  react-native-geolocation: c956aeb136625c23e0dce0467664af2c437888c9
  react-native-image-resizer: d9fb629a867335bdc13230ac2a58702bb8c8828f
  react-native-netinfo: 30fb89fa913c342be82a887b56e96be6d71201dd
  react-native-pager-view: 54970cc27fdef14db2dcfedadb091d711e88065d
  react-native-safe-area-context: f0906bf8bc9835ac9a9d3f97e8bde2a997d8da79
  react-native-segmented-control: 65df6cd0619b780b3843d574a72d4c7cec396097
  react-native-slider: ae891b9fca8c9b4a99691f6d45731f0ef2bb1866
  react-native-webview: 2e8fe70dc32b50d3231c23043f8e8b5a5525d346
  React-perflogger: 25373e382fed75ce768a443822f07098a15ab737
  React-RCTActionSheet: af7796ba49ffe4ca92e7277a5d992d37203f7da5
  React-RCTAnimation: 6a2e76ab50c6f25b428d81b76a5a45351c4d77aa
  React-RCTBlob: 02a2887023e0eed99391b6445b2e23a2a6f9226d
  React-RCTImage: ce5bf8e7438f2286d9b646a05d6ab11f38b0323d
  React-RCTLinking: ccd20742de14e020cb5f99d5c7e0bf0383aefbd9
  React-RCTNetwork: dfb9d089ab0753e5e5f55fc4b1210858f7245647
  React-RCTSettings: b14aef2d83699e48b410fb7c3ba5b66cd3291ae2
  React-RCTText: 41a2e952dd9adc5caf6fb68ed46b275194d5da5f
  React-RCTVibration: 24600e3b1aaa77126989bc58b6747509a1ba14f3
  React-runtimeexecutor: a9904c6d0218fb9f8b19d6dd88607225927668f9
  ReactCommon: 149906e01aa51142707a10665185db879898e966
  RNCAsyncStorage: 56a3355a10b5d660c48c6e37325ac85ebfd09885
  RNCClipboard: 41d8d918092ae8e676f18adada19104fa3e68495
  RNCMaskedView: 0e1bc4bfa8365eba5fbbb71e07fbdc0555249489
  RNCPicker: c620feabdfb8b131fc1be42b407be5bacc147fea
  RNFastImage: a7384db75df352500261e8e8f1ac2026def26102
  RNGestureHandler: a479ebd5ed4221a810967000735517df0d2db211
  RNReanimated: 514a11da3a2bcc6c3dfd9de32b38e2b9bf101926
  RNScreens: f7ad633b2e0190b77b6a7aab7f914fad6f198d8d
  RNSVG: 551acb6562324b1d52a4e0758f7ca0ec234e278f
  RNSyanImagePicker: aff3afbb646220de319eb00806589b9899b069c9
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: d0dac55073088d24b2ac1b191a71a8f8d0adac21
  TZImagePickerController: f1e4ed5ca1ddfc7e48d20867ff6a5c1e41a4b579
  UMAppLoader: 5db5dc03176c6238da9c8b3657a370137882a4ad
  UMBarCodeScannerInterface: 017672479f93de88d94c3a50dfb66b5348b65989
  UMCameraInterface: 22bd2c4bf15dcf68530368fa5704af7490818457
  UMConstantsInterface: ff612789417aace6fd01a9c35616bc87dcdc8d97
  UMCore: 94e4725ab2efbbe247b5910a2a954a2dab64f7eb
  UMFaceDetectorInterface: 9bc6a197ad9d4d16ba13bfdf2340dd7f24964308
  UMFileSystemInterface: e7795274eee76beaadaf6015bcc7f5da3054b925
  UMFontInterface: bc5dd878b2f6fffdac80e41fd36893c619683712
  UMImageLoaderInterface: 78ab0fd45c2bcaee61957fa23858bf31f2bcb122
  UMPermissionsInterface: 4f21b0c2aaf953cf041109ff44231b2a69e22836
  UMReactNativeAdapter: 61ae88818058b7849bbf5b79bf97872cb3c8e0eb
  UMSensorsInterface: 49bea41dcfcc041f0bbab27cfe4a6006f1dde0c0
  UMTaskManagerInterface: 6cad5929dbc0a5b986ad4d77caac497d4de730a7
  Yoga: 575c581c63e0d35c9a83f4b46d01d63abc1100ac
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a
  ZXingObjC: fdbb269f25dd2032da343e06f10224d62f537bdb

PODFILE CHECKSUM: 6dad79225b628186b444ffb2206db12e5a8847df

COCOAPODS: 1.10.1
