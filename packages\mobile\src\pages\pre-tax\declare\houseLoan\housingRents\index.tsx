/**
 * 税前抵扣申请-住房租金支出
 */
import { useEffect, useState } from 'react'
import { View } from '@tarojs/components'
import { users } from '@apis/users'
import Taro, { useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils/transforms'
import { navigateTo, getGlobalData } from '@utils'
import { BottomBtn, withPage, useForm, FormItemProps, Form } from '@components'
import isEmpty from 'lodash/isEmpty'
import styles from './index.module.scss'

const info = [
  { key: 0, value: '个人' },
  { key: 1, value: '组织' }
]
const cardType = [
  { key: '1', value: '居民身份证' },
  { key: '2', value: '中国护照' },
  { key: '3', value: '港澳居民来往内地通行证' },
  { key: '4', value: '港澳居民居住证' },
  { key: '5', value: '台湾居民来往大陆通行证' },
  { key: '6', value: '台湾居民居住证' },
  { key: '7', value: '外国护照' },
  { key: '8', value: '外国人永久居留身份证' },
  { key: '9', value: '外国人工作许可证（A类）' },
  { key: '10', value: '外国人工作许可证（B类）' },
  { key: '11', value: '外国人工作许可证（C类）' },
  { key: '12', value: '其他个人证件' }
]

const Index = () => {
  const { id = '' } = useRouter().params
  const [flag, setFlag] = useState(false)
  const employeeId = getGlobalData<'account'>('account').empId
  const { type = 'create' } = useRouter<{ type: 'create' | 'update' }>().params
  const form = useForm()
  const cityId = form.watch('cityId')
  const lessorCardType = form.watch('lessorCardType')
  const { result } = users.baseData.getBaseData.useRequest({ pageNum: '1', typeId: '911' })
  const openBankOptions = result?.data || ([] as defs.users.BaseData[])
  const onSubmit = (values: any) => {
    // console.log('values----', values)
    users.user.saveHouseRentalInfo.request(values).then(res => {
      // console.log(res)
      if (res?.code == 0) {
        // console.log('保存成功')
        Taro.navigateTo({
          url: '/pages/pre-tax/declare/submitSuccessfully/index'
        })
      }
    })
  }

  useEffect(() => {
    const values = form.getValues()
    form.reset({
      ...values,
      employeeId: employeeId,
      workCity: cityId,
      deductionAmount: 2000,
      deductionType: 2,
      deductionMonth: ''
    })
  }, [employeeId, cityId])

  useEffect(() => {
    form.setValue('deductionMonth', '')
  }, [])

  useEffect(() => {
    if (id !== '') {
      users.user.getHouseRentalInfo
        .request({
          employeeId: employeeId
        })
        .then(res => {
          // console.log(res,'res')

          users.user.getCityInfo
            .request({
              cityName: ''
            })
            .then(cityRes => {
              // console.log(cityRes,'cityRes')
              !isEmpty(cityRes?.data) &&
                cityRes?.data.map((item, index) => {
                  if (item.cityId == res?.data?.workCity) {
                    form.setValue('cityName', item.cityName)
                  }
                })
            })

          !isEmpty(res?.data) &&
            form.reset({
              ...res.data,
              cityId: res.data?.workCity,
              houseId: res.data?.houseId,
              deductionAmount: res.data?.deductionAmount,
              deductionMonth: ''
            })
            form.setValue('houseId', id)
        })
    }else {
      form.setValue('houseId', '')
      form.setValue('deductionMonth', '')
    }
  }, [id])

  const scrollStyle = getScrollStyle({ bottom: 120 })
  const columns: FormItemProps[] = [
    {
      name: 'cityId',
      type: 'page_choice',
      title: '主要工作城市',
      rules: { required: true },
      pageOptions: {
        keys: ['cityId', 'cityName', 'provinceId', 'provinceName', 'workCity'],
        labelKey: 'cityName',
        scen: '2',
        url: '/city',
        appId: '',
      }
    },
    {
      name: 'houseAddress',
      type: 'text',
      title: '住房坐落地址',
      rules: { required: true, maxLength: 25 }
    },
    {
      name: 'huoseLeaseType',
      type: 'single',
      title: '类型',
      options: info,
      rules: { required: true }
    },
    {
      name: 'lessorName',
      type: 'text',
      title: '出租方姓名（组织名称）',
      rules: { required: true }
    },
    {
      name: 'lessorCardType',
      type: 'select',
      title: '出租方证件类型',
      options: cardType,
      rules: { required: true }
    },
    {
      name: 'lessorCardNum',
      type: 'id_card',
      title: '身份证件号码（统一社会信用代码）',
      rules: {
        required: true,
        pattern: lessorCardType == 1 ? {
          value: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/,
          message: '请输入正确的身份证格式'
        }: {
          value: /([^\.\d]|^)(\d+)([^\.\d]|$)/,
          message: ''
        }
      }
    },
    {
      name: 'rentalNum',
      type: 'text',
      title: '住房租凭合同编号',
      rules: { required: true, maxLength: 25 }
    },
    {
      name: 'startLeaseDate',
      type: 'date',
      title: '租凭期起',
      rules: { required: true }
    },
    {
      name: 'endLeaseDate',
      type: 'date',
      title: '租凭期止',
      rules: { required: true }
    }
  ]
  return (
    <View className={styles.wrap}>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '保存',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
