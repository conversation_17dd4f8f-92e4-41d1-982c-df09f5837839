import { View, Button, Input, ScrollView } from '@tarojs/components'
import { useRouter } from '@tarojs/taro'
import { useState, useRef, useEffect } from 'react'
import { BottomBtn, withPage } from '@components'
import { getGlobalData, getScrollStyle } from '@utils'
import styles from './index.module.scss'
import PDF from './pdf'

const Index = () => {
  const { url, fileName } = useRouter().params
  const [numPages, setNumPages] = useState<number>(1)
  const [pageNumber, setPageNumber] = useState(1)
  const inputRef = useRef<any>()
  const scrollStyle = getScrollStyle({ bottom: 120 })

  useEffect(() => {
    url && Taro.showLoading({ title: '' })
  }, [url])

  const onDocumentLoadSuccess = (pages: any) => {
    setNumPages(pages.numPages)
    Taro.hideLoading()
  }

  const handlePrevious = () => {
    if (pageNumber > 1) {
      setPageNumber(pageNumber - 1)
    }
  }

  const handleNext = () => {
    if (pageNumber < numPages) {
      setPageNumber(pageNumber + 1)
    }
  }

  const handleGoto = () => {
    const value = Number(inputRef.current.value)
    if (value <= pageNumber) {
      setPageNumber(value)
    } else if (value > pageNumber) {
      setPageNumber(numPages)
    }
  }

  const downloadFile = (newUrl: any, name: string) => {
    const link = document.createElement('a')
    link.href = newUrl
    link.download = name
    document.body.appendChild(link)
    link.click()
    window.URL.revokeObjectURL(link.href)
    link.remove()
  }

  return (
    <View className={styles.wrap}>
      <ScrollView style={scrollStyle} scrollY>
        {url && (
          <View>
            <PDF
              file={url}
              onDocumentLoadSuccess={onDocumentLoadSuccess}
              onDocumentLoadFail={() => {
                console.log('文档加载失败-------')
                Taro.hideLoading()
              }}
              page={pageNumber}
              scale={Number(getGlobalData<'systemInfo'>('systemInfo').screenWidth) / 595}
            />
            <View className={styles.buttonWrap}>
              <Button className={pageNumber > 1 ? styles.button : styles.button_disabled} onClick={handlePrevious}>
                上一页
              </Button>
              <Input className={styles.input} value={String(pageNumber)} ref={inputRef} />
              <Button className={pageNumber < numPages ? styles.button : styles.button_disabled} onClick={handleNext}>
                下一页
              </Button>
              <Button className={styles.button} onClick={handleGoto}>
                跳转
              </Button>
              <View className={styles.text}>共{numPages}页</View>
            </View>
          </View>
        )}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '下载',
            onClick: () => {
              if (url && fileName) {
                downloadFile(url, fileName)
              }
            }
          },
          {
            title: '返回',
            type: 'ghost',
            onClick: () => {
              Taro.navigateBack({ delta: 2 })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
