import { ScrollView, View, Text, Input, Button, Modal} from '@tarojs/components'
import { Fragment, useEffect, useState, useRef} from 'react'
import { BottomBtn, withPage, FormLabel, ImagePick } from '@components'
import { getGlobalData, getScrollStyle, BaseUrl, chooseImage } from '@utils'
import { isEmail } from '@utils/is-type'
import isEmpty from 'lodash/isEmpty'
import debounce from 'lodash/debounce';
import { upload } from '@apis/upload'
import { pact } from '@apis/pact'
import { ncmp } from '@apis/ncmp'
import Taro, { useRouter } from '@tarojs/taro'
import styles from './index.module.scss'

const TYPE = {
  '0' : '否',
  '1' : '是'
}
const Index = () => {
  // const [visible, setVisible] = useState<boolean>(false)
  const [files, setFiles] = useState<defs.upload.CtEbmBookingImageDTO[]>()
  const [materialList, setMaterialList] = useState<defs.ncmp.BusinessSubType[]>()
  const [businesssubTypes, setBusinesssubTypes] = useState<defs.ncmp.BusinessSubType[]>()
  const [stewardInfo, setStewardInfo] = useState<Array<defs.pact.ButlerResponseData>>()
  const inputRef = useRef<any>()
  const { title, categoryName, bussNameClassName, busTypeName, busSubtypeName, busSubtypeId = '', uuid = '' } = useRouter().params || {}
  const { openId, accountId, empId} = getGlobalData<'account'>('account')
  const scrollStyle = getScrollStyle({ bottom: 120 })
  useEffect(() => {
    title && Taro.setNavigationBarTitle({ title })
    fetchAppointmentFile()
    getMaterial()
    businessSubTypeDetail()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [title])
  useEffect(() => {
    pact.per.getSteward
      .request({
        empId,
        openId
      })
      .then(res => {
        !isEmpty(res?.data) && setStewardInfo(res?.data)
      })
  }, [empId, openId])
  const getMaterial = () => {
    ncmp.policy.getMaterial
    .request({busSubtypeIds:busSubtypeId})
    .then(res => {
      if (res.code == '200') {
        !isEmpty(res?.resultObj) && setMaterialList(res?.resultObj as defs.ncmp.BusinessSubType[])
      } else {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      }
    })
    .catch(() => {
      Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
    })
  }
  const businessSubTypeDetail = () => {
    ncmp.policy.getSubTypeByIds
    .request({busSubtypeIds:busSubtypeId})
    .then(res => {
      if (res.code == '200') {
        !isEmpty(res?.resultObj) &&  setBusinesssubTypes(res?.resultObj as defs.ncmp.BusinessSubType[])
      } else {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      }
    })
    .catch(() => {
      Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
    })
  }
  const sendMail = (emailAddress:string) => {
    ncmp.policy.sendMaterialMail
    .request({accountId,openId, busSubtypeIds: busSubtypeId, emailAddress})
    .then(res => {
      if (res.code == '200') {
        Taro.showToast({title: '电子邮件发送成功'})
      } else {
        Taro.showToast({ icon: 'none', title: res.errorMsg || '' })
      }
    })
    .catch(() => {
      Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
    })
  }
  const fetchAppointmentFile = () => {
    upload.fileUploader.fetchAppointmentFile.request({ uuid, accountId }).then(res => {
      if (res.code == '200') {
        setFiles(res.resultObj)
      } else {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      }
    })
  }
  const uploadImage = () => {
    const data = {
      url: '/wx-upload/appointment/uploadAppointmentFileByForm',
      bookImageId: 0,  // 是否更新图片
      businessId: uuid,
    }
    chooseImage(data, fetchAppointmentFile)
  }
  //  删除图片
  const onDelete = (index: number) => {
    const file = files?.[index]
    if (!file) return
    Taro.showModal({
      title: '提示',
      content: '是否进行删除操作?',
      showCancel: true,
      success: ({ confirm }) => {
        if (confirm) {
          upload.fileUploader.deleteAppointmentFile.request({ bookImageId: file.ebmBookingImageId! }).then(res => {
            if (res.code == '200') {
              Taro.showToast({ title: '删除成功' })
              fetchAppointmentFile()
            } else {
              Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
            }
          })
        }
      }
    })
  }
  const renderItem = () => {
    return (
      materialList?.map((item:any, index) => (
        <View className={styles.group} key={index}>
          <Text className={styles.text}>{`材料编号：${item.materialsId}`}</Text>
          <Text className={styles.text}>{`材料名称：${item.materialsName}`}</Text>
          <Text className={styles.text}>{`是否原件：${item.isOriginal === '0' ? TYPE[item.isOriginal] : TYPE[item.isOriginal]}`}</Text>
          <Text className={styles.text}>{`材料分数：${item.materialsAccount}`}</Text>
          <Text className={styles.text}>{`是否返还给申请人：${item.isReturn === '0' ? TYPE[item.isReturn] : TYPE[item.isReturn]}`}</Text>
        </View>
      ))
    )
  }
  const downloadFile = (path: string, name: string) => {
    const link = document.createElement('a');
    // link.href = window.URL.createObjectURL(res);
    link.href = `${BaseUrl}/wx-ncmp/policy/downloadPolicyFile?filePath=${path}&fileName=${name}`
    link.download = `${name}`;
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(link.href);
    link.remove();
  };
  const exportFile = (filePath: string, fileName: string) => {
    if (!filePath || !fileName) {
      Taro.showToast({title: '无文件下载', icon: 'none'})
      return
    }
    downloadFile(filePath, fileName)
    // ncmp.policy.downloadPolicyFile
    //   .request({ filePath, fileName}, {header: {'content-type': 'application/octet-stream'}})
    //   .then((res) => {
    //     const blob = new Blob([res])
    //     if (res) {
    //       downloadFile(blob, fileName);
    //     }
    //   });
  };
  return (
    <Fragment>
      <ScrollView style={scrollStyle} scrollY>
        <FormLabel level={2} title='办理业务' />
        <View className={styles.group}>
          <Text className={styles.text}>{`所属类型：${categoryName}`}</Text>
          <Text className={styles.text}>{`业务项目：${bussNameClassName}`}</Text>
          <Text className={styles.text}>{`业务大类：${busTypeName}`}</Text>
          <Text className={styles.text}>{`业务小类：${busSubtypeName}`}</Text>
        </View>
        <FormLabel level={2} title='个人办理所需资料信息' />
        {renderItem()}
        <View className={styles.group}>
          {
            businesssubTypes?.map((item:any, index) => (
              <Fragment key={index}>
                <View className={styles.flex}>
                  <Text className={styles.text}>样本下载</Text>
                  <Text>{item?.pSampleTemplatePathName}</Text>
                  {/* <Text onClick={() => exportFile(item?.pSampleTemplatePath, item?.pSampleTemplatePathName)}>{item?.pSampleTemplatePathName}</Text> */}
                </View>
                <View className={styles.flex}>
                  <Text className={styles.text}>空表下载</Text>
                  <Text>{item?.pBlankTemplatePathName}</Text>
                  {/* <Text onClick={() => exportFile(item?.pBlankTemplatePath, item?.pBlankTemplatePathName)}>{item?.pBlankTemplatePathName}</Text> */}
                </View>
              </Fragment>
            ))
          }
        </View>
        <View className={styles.mail_box}>
          <View className={styles.input} >
            <Input type='text'placeholder='请输入邮箱地址' ref={inputRef} />
          </View>
          <Button className={styles.button} onClick={debounce(() => {
            const inputValue = inputRef.current.value
            if (isEmail(inputValue)) {
              sendMail(inputValue)
              return
            }
            Taro.showToast({title: '请输入正确邮箱地址', icon: 'none'})
          }, 1000)}
          >发送</Button>
        </View>
        <View className={styles.text_mg}> 
          <Text className={styles.text1}>
            1.如果材料无法下载,请输入邮箱地址, 样本文件及空表文件会发送至您填写的邮箱中
          </Text>
        </View>
        <View className={styles.text_mg} onClick={() => Taro.makePhoneCall({ phoneNumber: `${stewardInfo?.[0]?.contact || ''}` })}>
          <Text className={styles.text1}>
            2.如依旧无法收到邮件,请联系您的专属客服索取材料<Text className={styles.red_text}>{`${stewardInfo?.[0]?.contact || ''}`}</Text>
          </Text>
        </View>
        <FormLabel level={2} title='资料上传' />
        <ImagePick
          files={files?.map(item => `${BaseUrl}/wx-upload/view${item.relPath}`)}
          maxCount={15}
          onAdd={uploadImage}
          onDelete={onDelete}
        />
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () =>  Taro.navigateBack()
          }
        ]}
      />
      {/* <Modal
        visible={visible}
        title='提示'
        onConfirm={() => {
          setVisible(false)
        }}
        content='是否下载'
      /> */}
    </Fragment>
  )
}

export default withPage(Index, { needSignature: true })
