/**
 * @description 获取华住补证件主记录
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.HzSupplyCert>;
export const path = '/wx-ncmp/elecsign/getHzSupplyCountByEmpId';
export const method = 'POST';
export const request = (data: object, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: object,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
