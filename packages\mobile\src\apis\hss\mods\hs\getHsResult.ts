/**
 * @description 申报结果查询
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openid */
  openid: string;
  /** year */
  year: string;
}

export type Result = defs.hss.GeneralRespBean<defs.hss.HsResult>;
export const path = '/yc-hs/hsInfo/getHsResult/{openid}/{year}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
