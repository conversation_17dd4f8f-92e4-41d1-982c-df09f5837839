import { useEffect, useState } from 'react'
import { CSSTransition } from 'react-transition-group'
import Taro from '@tarojs/taro'

export default function H5CSSTranstionHoc(props) {
  const [show, setShow] = useState(false)
  useEffect(() => {
    setShow(true)
    const lastPageEl = document.getElementById(Taro.getCurrentPages()[Taro.getCurrentPages().length - 2]?.path)
    if (lastPageEl?.style) {
      lastPageEl.style.display = 'block'
    }
    setTimeout(() => {
      if (lastPageEl?.style) {
        lastPageEl.style.display = 'none'
      }
    }, 500)
    return () => setShow(false)
  }, [])
  return (
    <CSSTransition
      in={show}
      timeout={500}
      classNames={Taro.getCurrentPages().length > 1 ? 'global-page' : ''}
      // unmountOnExit
      // onEntering={handleEntering}
      appear
    >
      {props?.children}
    </CSSTransition>
  )
}
