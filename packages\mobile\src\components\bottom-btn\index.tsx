import { FunctionComponent } from 'react'
import { View, Text } from '@tarojs/components'
import BoxShadow from '../box-shadow'
import SafeAreaView from '../safe-area-view'
import styles from './index.module.scss'

interface BottomBtn {
  // TODO: 根据type 设置样式
  btns: {
    title: string
    type?: 'primary' | 'ghost'
    onClick: Function
    hide?: boolean
    disabled?: boolean
  }[]
}

const BottomBtn: FunctionComponent<BottomBtn> = props => {
  const { btns } = props
  // const { disabled = false } = btns
  return (
    <BoxShadow
      shadowColor='#000'
      shadowOffset={{
        width: 0,
        height: -1
      }}
      shadowOpacity={0.1}
      shadowRadius={1}
      elevation={5}
      boxShadow='0px 2px 8px 0px rgba(211,215,218,1)'
    >
      <SafeAreaView className={styles.wrap}>
        {btns.map(btn => {
          if (!btn.hide)
            return (
              <View
                key={btn.title}
                className={btn.disabled ? styles.btn_disabled : btn.type === 'ghost' ? styles.btn_ghost : styles.btn}
                onClick={() => {
                  !btn.disabled && btn.onClick()
                }}
              >
                <Text className={styles.text}>{btn.title}</Text>
              </View>
            )
        })}
      </SafeAreaView>
    </BoxShadow>
  )
}

export default BottomBtn
