import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleLongYanColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const isFirstSecurity = form.watch('isEnterFund_longyan')
  if (column.name === 'isEnterFund_longyan'){
    form.register('isEnterFund_longyan', {value: ['1', '是'].includes(column.defaultValue) ? '是' : '否'})
  }
  if(column.name === 'isFirstSecurity') {
    return { ...column, remind: ['1', '是'].includes(isFirstSecurity) && column.remind}
  }
  if (column.name === 'pfundAccount_longyan') {
    return { ...column, rules:{required: true}, isHidden: !['1', '是'].includes(isFirstSecurity)}
  }
  return column;
}

export { handleLongYanColumn }