/*
 * @Author: your name
 * @Date: 2021-08-20 17:59:00
 * @LastEditTime: 2021-09-10 18:04:23
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \wehr_web2\packages\mobile\src\utils\navgation.ts
 */
import Taro from '@tarojs/taro'
import { route } from '../../config/config'

type backOptions = (Taro.navigateBack.Option | Taro.navigateBackMiniProgram.Option) & { mini: boolean }
const createUrl = (key: routeKey, options: Partial<navigateToOption>) => {
  const { prexfix = 'index', rootPageName = 'pages' } = route
  const { query: queryObj = {} } = options
  let query = ''
  for (let k in queryObj) {
    if (queryObj[k] && Object.prototype.toString.call(queryObj[k]) !== '[object Object]') {
      query += !query?.length ? `?${k}=${queryObj[k]}` : `&${k}=${queryObj[k]}`
    }
  }
  return `/${rootPageName}${key}/${prexfix}${query}`
}
export const navigateTo = (
  key: routeKey,
  query?: any,
  options?: Omit<navigateToOption, 'query'>,
  originOptions?: navigateToMiniCommonOption & navigateToCommonOption
) => {
  const url = createUrl(key, { query, ...options })
  originOptions?.appId
    ? Taro.navigateToMiniProgram({ path: url, ...originOptions })
    : Taro.navigateTo({ url, ...originOptions })
}

export const navigateBack = (options: backOptions) =>
  options.mini ? Taro.navigateBackMiniProgram(options) : Taro.navigateBack(options)
