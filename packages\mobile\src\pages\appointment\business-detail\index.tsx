/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-16 15:43:09
 * @LastAuthor: 王正荣
 * @LastTime: 2021-12-02 10:54:00
 * @message:
 */
import { View, ScrollView, Image, Text } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { getGlobalData, getScrollStyle,BaseUrl } from '@utils'
import { pact } from '@apis/pact'
import { upload } from '@apis/upload'
import { BottomBtn, withPage, Labels, FormLabel } from '@components'
import Taro, { useRouter } from '@tarojs/taro'
import Nodata from '@assets/icon/icon-file.png'
import PDFIcon from '@assets/appointment/Pdf.png'
import styles from './index.module.scss'

const procStatusMap = new Map<string, string>([
  ['10', '办理中'],
  ['20', '办理中'],
  ['50', '办理中'],
  ['60', '办理中'],
  ['30', '退回跟进'],
  ['40', '退回跟进'],
  ['70', '办理完成'],
  ['80', '办理完成'],
  ['90', '办理完成'],
  ['100', '申请办理'],
  ['110', '确认办理']
])

const resultMap = new Map<string, string>([
  ['1', '全部成功'],
  ['2', '全部失败'],
  ['3', '部分成功'],
  ['4', '未反馈']
])


const Index = () => {
  const { openId, empId } = getGlobalData<'account'>('account')
  const { businessId } = useRouter<{ businessId: string }>().params
  const [detail, setDetail] = useState<defs.pact.BusinessDetailData>()
  const [feedbackInfo, setFeedbackInfo] = useState<Array<defs.pact.FeedbackData>>()
  const [updateData, setUpdateData] = useState<Array<defs.upload.CtEbmBookingImageDTO>>()
  const scrollStyle = getScrollStyle({ bottom: 120 })

  useEffect(() => {
    pact.busi.getBusinessDetail.request({
      openId, businessId
    })
      .then(res => {
        setDetail(res.data)
      })

    pact.busi.getFeedbackInfo.request({
      empId, openId, businessId
    })
      .then((res) => {
        setFeedbackInfo(res.data)
      })

    upload.fileUploader.fetchBusinessFile.request({
      businessId: Number(businessId),
      empId
    })
      .then((res) => {
        setUpdateData(res.resultObj)
      })
  }, [openId, businessId, empId])



  const previewPhoto = (photoUrl: string) => {
    Taro.previewImage({
      current: photoUrl,
      urls: [photoUrl]
    })
  }

  const previewPDF = (url: string, fileName: any) => {
    Taro.navigateTo({
      url: `/pages/pdf/index?url=${url}&fileName=${fileName}`
    })
  }

  const returnFeedbackModule = () => {
    if (feedbackInfo && feedbackInfo?.length > 0) {
      return feedbackInfo?.map((item) => (
        <Labels key={item.cetfId} detail={item?.fbContext} />
      ))
    } else if (updateData && updateData?.length > 0) {
      return (
        <View className={styles.boxWrap}>
          {updateData.map((item) => {
            if (item.relPath) {
              const url = `${BaseUrl}/wx-upload/view${item.relPath}`
              return (
                <View>
                  {item.relPath.indexOf('pdf') > -1 &&
                    <View key={item.ebmBookingImageId} className={styles.photoBox}>
                      <View className={styles.boxTitle}>
                        资料
                      </View>
                      <View className={styles.imgWrap} onClick={() => previewPDF(url, item.fileName)}>
                        <Image className={styles.imgIcon} src={PDFIcon} />
                      </View>
                    </View>
                  }
                  {item.relPath.indexOf('jpg') > -1 &&
                    <View key={item.ebmBookingImageId} className={styles.photoBox}>
                      <View className={styles.boxTitle}>
                        资料
                      </View>
                      <View className={styles.imgWrap} onClick={() => previewPhoto(url)}>
                        <Image className={styles.img} src={url} />
                      </View>
                    </View>
                  }
                  {item.relPath.indexOf('png') > -1 &&
                    <View key={item.ebmBookingImageId} className={styles.photoBox}>
                      <View className={styles.boxTitle}>
                        资料
                      </View>
                      <View className={styles.imgWrap} onClick={() => previewPhoto(url)}>
                        <Image className={styles.img} src={url} />
                      </View>
                    </View>
                  }
                </View>
              )
            }
          })}
        </View>
      )
    } else {
      return <View className={styles.empty}>
        <Image className={styles.nodata} src={Nodata} />
        <Text className={styles.notext}>暂无数据</Text>
      </View>
    }
  }

  return (
    <View>
      <ScrollView style={scrollStyle} scrollY>
        <View>
          <Labels title='业务大类' detail={detail?.busTypeName} />
          <Labels title='业务小类' detail={detail?.busSubTypeName} />
          <View className={styles.group_margin}></View>
        </View>
        <View>
          <Labels title='经办人' detail={detail?.pOperator} />
          <Labels title='代办人' detail={detail?.pAgent} />
          <Labels title='办理人类型' detail={detail?.processorType} />
          <Labels title='办理进度' detail={detail?.procStatus && procStatusMap.get(detail?.procStatus)} />
          <Labels title='办理结果' detail={detail?.result && resultMap.get(detail?.result)} />
          <Labels title='办理开始时间' detail={detail?.procStartDt?.substring(0, 10)} />
          <View className={styles.group_margin}></View>
        </View>
        <View className={styles.group}>
          <FormLabel level={2} title='反馈信息' />
          {returnFeedbackModule()}
        </View>
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回业务办理列表',
            onClick: () => {
              Taro.navigateBack({ delta: 1 })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
