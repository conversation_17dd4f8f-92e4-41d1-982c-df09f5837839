import { CommonEventFunction, Picker, Text, Image, View } from '@tarojs/components'
import { useState, useEffect } from 'react'
import dayjs from 'dayjs'
import { PickerDateProps } from '@tarojs/components/types/Picker'
import icon_calendar from '@assets/icon/icon-calendar.png';

import styles from './index.module.scss'

export const stdDateFormat = 'YYYY-MM-DD'
export const stdDateFormatNoDash = 'YYYYMMDD'
export const stdMonthFormat = 'YYYY-MM'
export const stdMonthFormatMoDash = 'YYYYMM'

export interface TimePickerProps {
  fields?: 'year' | 'month' | 'day'
  onChange?: CommonEventFunction<PickerDateProps.ChangeEventDetail>
  value?: string
  name?: string
}
const tody = dayjs().format(stdDateFormat)
const TimePicker = (props: TimePickerProps) => {
  const { onChange, value, ...rest } = props
  const [date, setDate] = useState<string>(tody)
  const _onChange = (event) => {
    const { value: _value } = event.detail
    setDate(_value)
    if (onChange) onChange(event)
  }
  useEffect(() => {
    setDate(value === undefined ? tody : value)
  }, [value])
  return (
    <Picker mode='date' onChange={_onChange} value={date} {...rest}>
      <View className={styles.time_picker}>
        <Text className={styles.text}>{date}</Text>
        <Image className={styles.icon} src={icon_calendar} />
      </View>
    </Picker>
  )
}

export { TimePicker }
