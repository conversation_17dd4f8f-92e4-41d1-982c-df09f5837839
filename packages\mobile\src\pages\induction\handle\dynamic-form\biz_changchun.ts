import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleChangChunColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
    const isEnterFund_changchun = form.watch('isEnterFund_changchun')
    const isMakeupStopSs = form.watch('isMakeupStopSs')
    const isChangchunBabyHealth = form.watch('isChangchunBabyHealth')
    const isChangchunHealth = form.watch('isChangchunHealth')
    if ((column.name === 'isChangchunHealth' && (isChangchunHealth === '1' || column.defaultValue === '1'))){
        return { ...column, remind: '请确认您的省内医疗保险、工伤保险都处于停缴状态，以保证我司续保成功'}
    }
    if ((column.name === 'isChangchunHealth' && (isChangchunHealth === '0' || column.defaultValue === '0'))){
        return { ...column, remind: ''}
    }
    if ((column.name === 'isEnterFund_changchun' && (isEnterFund_changchun === '1' || column.defaultValue === '1'))){
        return { ...column, remind: '请联系上家单位将住房公积金转移至我司，我司信息：北京易才人力资源顾问有限公司长春分公司 单位编号：100032671'}
    }
    if ((column.name === 'isEnterFund_changchun' && (isEnterFund_changchun === '0' || column.defaultValue === '0'))){
        return { ...column, remind: ''}
    }
    if ((column.name === 'isChangchunBabyHealth' && (isChangchunBabyHealth === '1' || column.defaultValue === '1'))){
        return { ...column, remind: '请确认您的养老处于停缴状态、失业金处于停领状态，以保证续保成功。'}
    }
    if (column.name === 'isMakeupStopSs' && (isMakeupStopSs === '1' || column.defaultValue === '1')){
        return { ...column, remind: '请及时通过微信小程序“长春社会保险”申请补缴或自行前往社保局补缴，如有疑问，可联系0431-9612333咨询。补缴完成后请及时停缴，以保证续保成功。'}
    }
    return column
}
  
export { handleChangChunColumn }