/*
 * @Author: your name
 * @Date: 2021-08-25 17:59:28
 * @LastEditTime: 2021-09-03 15:31:06
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\appointment\appointmentBusniess\index.tsx
 */
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
// import home4 from "@assets/appointment/icon-home-4.svg";
// import home6 from "@assets/appointment/icon-home-6.svg";
import styles from './index.module.scss'

const AppointmentBusniess = () => {
  return (
    <View className={styles['appointment-busniess']}>
      <View
        className={styles['s-list-index__item']}
        style={{
          borderBottom: 'solid 1px #e9ecef'
        }}
        onClick={() => {
          Taro.navigateTo({ url: '/pages/appointment/appointmentManage/index' })
        }}
      >
        <Image src={home4} className={styles.icon} />
        <Text>员工预约办理</Text>
      </View>
      <View
        className={styles['s-list-index__item']}
        onClick={() => {
          Taro.navigateTo({ url: '/pages/appointment/appointmentQuery/index' })
        }}
      >
        <Image src={home6} className={styles.icon} />
        <Text>业务办理查询</Text>
      </View>
    </View>
  )
}

export default AppointmentBusniess
