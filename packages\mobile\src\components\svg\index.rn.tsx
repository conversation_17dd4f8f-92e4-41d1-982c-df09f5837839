import React,{ FunctionComponent } from "react";
import { ImageProps } from "@tarojs/components/types/Image";

import RNSvg from "./rn-svg";

const Svg: FunctionComponent<ImageProps> = props => {
  const { src, className, style,...rest } = props;
  const _style = { ...(className as Object), ...(style as Object) } as any;
  return (
    <RNSvg
      // source={src}
      svgXmlData={src}
      width={_style.width}
      height={_style.height}
      style={{ ...(className as Object), ...(style as Object) }}
      className={className}
      color
      {...rest}
    />
  );
};

export default React.memo(Svg);
