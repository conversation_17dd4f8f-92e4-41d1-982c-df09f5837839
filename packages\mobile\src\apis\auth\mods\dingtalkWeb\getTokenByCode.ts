/**
 * @description 根据钉钉免登录授权返回的code获取token和cropid
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 钉钉免登录授权返回的code */
  code: string;
  /** 钉钉免登录授权返回的corpId */
  corpId: string;
}

export type Result = defs.auth.GlobalResult<defs.auth.TokenByCode>;
export const path = '/enterprise-auth/web/ding/getTokenByCode';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
