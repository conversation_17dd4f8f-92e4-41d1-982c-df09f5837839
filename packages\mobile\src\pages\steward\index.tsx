/*
 * @Author: your name
 * @Date: 2021-09-10 18:03:10
 * @LastEditTime: 2021-09-10 18:04:18
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\steward\index.tsx
 */
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { Fragment, useEffect, useState } from 'react'
import { getGlobalData, getScrollStyle } from '@utils'
import isEmpty from 'lodash/isEmpty'
import { pact } from '@apis/pact'
import Taro from '@tarojs/taro'
import { BottomBtn, withPage } from '@components'
import styles from './index.module.scss'

const Index = () => {
  const { empId, openId } = getGlobalData<'account'>('account')
  const [stewardInfo, setStewardInfo] = useState<Array<defs.pact.ButlerResponseData>>()
  useEffect(() => {
    pact.per.getSteward
      .request({
        empId,
        openId
      })
      .then(res => {
        !isEmpty(res?.data) && setStewardInfo(res?.data)
      })
  }, [empId, openId])
  const scrollStyle = getScrollStyle({ bottom: 120 })
  return (
    <Fragment>
      <ScrollView style={scrollStyle} scrollY>
        <Image src={require('@assets/steward/img-banner-2.jpg')} className={styles.banner} onClick={() => {}} />
        {stewardInfo?.map(item => (
          <View key={item.providerType} className={styles.steward_cont}>
            <View className={styles.steward_item}>
              <Text className={styles.steward_title}>管家姓名</Text>
              <Text className={styles.steward_text}>{item.stewardName}</Text>
            </View>
            <View className={styles.steward_item}>
              <Text className={styles.steward_title}>联系方式</Text>
              <Text className={styles.steward_text}>{item.contact}</Text>
            </View>
            <View className={styles.steward_item}>
              <Text className={styles.steward_title}>地址</Text>
              <Text className={styles.steward_text}>{item.address}</Text>
            </View>
            <View className={styles.steward_item}>
              <Text className={styles.steward_title}>邮箱</Text>
              <Text className={styles.steward_text}>{item.email}</Text>
            </View>
          </View>
        ))}
        <View className={styles.foot_item} onClick={() => Taro.makePhoneCall({ phoneNumber: '************' })}>
          <Text>
            易才客户服务热线：<Text className={styles.red_text}>************</Text>
          </Text>
        </View>
        <View className={styles.foot_item}>
          <Text>
            服务时间：<Text className={styles.red_text}>9:00-12:00/13:00-18:00</Text>
          </Text>
        </View>
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回首页',
            onClick: () => {
              Taro.navigateBack({ delta: 1 })
            }
          }
        ]}
      />
    </Fragment>
  )
}

export default withPage(Index)
