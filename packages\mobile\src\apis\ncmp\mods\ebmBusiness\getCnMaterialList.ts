/**
 * @description 获取材料列表
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** busCityConfigId */
  busCityConfigId: string;
}

export type Result = defs.ncmp.HttpResult<
  Array<defs.ncmp.EbmBusinessCnMaterialDTO>
>;
export const path = '/wx-ncmp/ebmbusiness/getCnMaterialList';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
