/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-15 16:52:53
 * @LastAuthor: 王正荣
 * @LastTime: 2021-11-26 17:23:30
 * @message: 
 */
import { View, Text } from '@tarojs/components'
import { Fragment, useState } from 'react'
import { getGlobalData, getScrollStyle } from '@utils'
import { users } from '@apis/users'
import { ListView, usePagination, withPage, Buttons, DateYearMonthSalary, Modal } from '@components'
import dayjs from 'dayjs'
import Taro from '@tarojs/taro'
import styles from './index.module.scss'

const itemConfig = [
  { title: '所属计税月', key: 'taxMonth' },
  { title: '居民/非居民', key: 'taxPayerType' },
  { title: '缴税义务人支付地', key: 'payAddress' },
  { title: '工资薪金合计', key: 'f9' },
  { title: '劳务合计', key: 'f33' },
  { title: '年终奖合计', key: 'f18' }
]
const Index = () => {
  const { openId: cmpToken, empId } = getGlobalData<'account'>('account')
  const scrollStyle = getScrollStyle({ top: 286 })
  const [visible, setVisible] = useState(false)
  const [responseMessage, setResponseMessage] = useState('')
  const [showData, setShowData] = useState<Object>()
  const [month, setMonth] = useState<{ startMonth: string; endMonth: string }>({ startMonth: '', endMonth: '' })
  const list = usePagination(
    async page => {
      await users.psnCustTotal.getPsnCustTotal.request({
        cmpToken,
        empId,
        startMonth: dayjs(month.startMonth).format('YYYYMM'),
        endMonth: dayjs(month.endMonth).format('YYYYMM')
      })
        .then((res) => {
          if (res.data?.length) {
            setShowData({
              f9: res.data[0]?.f9,
              f18: res.data[0]?.f18,
              f33: res.data[0]?.f33
            })
          }
        })
      if (page > 1) {
        return []
      }
      const result = await users.psnCust.getPsnCust.request({
        cmpToken,
        empId,
        // pageNum: String(page),
        // id: 5,
        startMonth: dayjs(month.startMonth).format('YYYYMM'),
        endMonth: dayjs(month.endMonth).format('YYYYMM')
      })
      if (result.message) {
        setVisible(true)
        setResponseMessage('您所在公司未开通工资查询权限，如有疑问请与贵公司人事联系！')
      } else {
        return result.data
      }
    },
    { deps: [month], auto: !!month.startMonth }
  )
  const detail = (dataId: string) => {
    Taro.navigateTo({ url: `/pages/salary-files/detail/index?dataId=${dataId}` })
  }
  return (
    <Fragment>
      <DateYearMonthSalary
        onSelectHandle={e => {
          const { startDate, endDate } = e
          setMonth({ startMonth: startDate, endMonth: endDate })
        }}
        pickerProps={[{ fields: 'month' }, { fields: 'month' }]}
        showData={showData}
      />
      <ListView
        style={scrollStyle}
        itemSize={502}
        renderItem={item => (
          <View className={styles.item}>
            {itemConfig.map(config => (
              <View className={styles.text_wrap} key={config.key}>
                <Text className={styles.title}>{config.title}</Text>
                <Text className={styles.detail}>
                  {config.key !== 'taxPayerType' ? item[config.key] :
                    (item[config.key] === '1' ? '居民' : '非居民')
                  }
                </Text>
              </View>
            ))}
            <View className={styles.btn_wrap}>
              <Buttons
                title='查看明细'
                icon='detail'
                onClick={() => detail(item.dataId)}
              />
            </View>
          </View>
        )}
        {...list}
      />
      <Modal
        visible={visible}
        title='提示'
        onConfirm={() => {
          setVisible(false)
        }}
        content={responseMessage}
      />
    </Fragment>
  )
}

export default withPage(Index)

