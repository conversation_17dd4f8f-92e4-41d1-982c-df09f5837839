import { FunctionComponent } from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'

import AddImg from '@assets/getadd.png'
import delImg from '@assets/icon/icon-delete.png'
import styles from './index.module.scss'

interface ImagePick {
  onDelete?: (index: number) => void
  onAdd: () => void
  files?: string[]
  // 最大添加个数
  maxCount?: number
  // 是否可以添加 default:true
  canAdd?: boolean
  // 是否可以删除 default:true
  canDelete?: boolean
  // 是否可以预览 default:true
  canPreview?: boolean
}

const ImagePick: FunctionComponent<ImagePick> = props => {
  const { files = [], maxCount = 1, canDelete = true, canPreview = true, onAdd, onDelete } = props
  return (
    <View className={styles.wrap}>
      {files?.map((file, index) => (
        <View className={styles.item} key={index} onClick={() => {}}>
          <Text className={styles.title}>资料</Text>
          <View className={styles.content}>
            {canDelete && (
              <Image
                className={styles.delete}
                src={delImg}
                onClick={e => {
                  canDelete && onDelete?.(index)
                }}
              />
            )}
            {/* <Image className={styles.add} src={AddImg} /> */}
            <Image
              className={styles.img}
              src={file}
              onClick={() => canPreview && Taro.previewImage({ urls: files, current: file })}
            />
          </View>
        </View>
      ))}
      {files.length < maxCount && (
        <View
          className={styles.item}
          onClick={() => {
            if (files.length < maxCount) {
              onAdd?.()
            } else {
              Taro.showModal({ title: '提示', content: `最多可以上传${maxCount}张图片` })
            }
          }}
        >
          <Text className={styles.title}>资料</Text>
          <View className={styles.content}>
            <Image className={styles.add} src={AddImg} />
          </View>
        </View>
      )}
    </View>
  )
}

export default ImagePick
