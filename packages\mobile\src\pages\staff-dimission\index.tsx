// import { useEffect, useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import { withPage } from '@components'
import { getGlobalData } from '@utils'
import Taro from '@tarojs/taro'
import { ncmp } from '@apis/ncmp'
import icon_elesign_1 from '@assets/elesign/elesignature1.png'
import icon_elesign_2 from '@assets/elesign/void-elesign.png'
import icon_elesign_3 from '@assets/elesign/elesign.png'
import icon_elesign_4 from '@assets/elesign/view-elesign.png'
import styles from './index.module.scss'

const list = [
  {
    title: '签署电子离职材料',
    img: icon_elesign_1,
    key: '1'
  },
  {
    title: '作废电子离职材料',
    img: icon_elesign_2,
    key: '2'
  },
  {
    title: '查看电子离职材料',
    img: icon_elesign_3,
    key: '3'
  },
  {
    title: '查看电子离职证明',
    img: icon_elesign_4,
    key: '4'
  }
]

const Index = () => {
  const { empId, accountId, openId } = getGlobalData<'account'>('account');
  const toEelSignaturePage = (key: string) => {
    switch (key) {
      case '1':
        ncmp.elecSign.signQuitMaterial.request({empId}, {isToken: true}).then(res => {
            if (res.code === '200') {
                const eleSinUrl = res?.resultObj?.eleSinUrl || ''
                if (!eleSinUrl) {
                  const title = '没有需要签署的电子离职材料'
                  Taro.showToast({ title, icon: 'none' })
                  return
                }
                window.location.href = eleSinUrl
              }
        }).catch(() => {
          const title = '系统异常！'
          Taro.showToast({ title, icon: 'none' })
        })
        return
      case '2':
        ncmp.elecSign.delQuitMaterial.request({empId}, {isToken: true}).then(res => {
            if (res.code === '200') {
                const eleSinUrl = res?.resultObj?.eleSinUrl || ''
                if (!eleSinUrl) {
                  const title = '没有需要作废的电子离职材料'
                  Taro.showToast({ title, icon: 'none' })
                  return
                }
                window.location.href = eleSinUrl
              }
        }).catch(() => {
          const title = '系统异常！'
          Taro.showToast({ title, icon: 'none' })
        })
        return
      case '3':
        getEleContractList(key)
        return
      case '4':
        getEleContractList(key)
        return
    }
  }
  const getEleContractList = (key) => {
    Taro.navigateTo({ url: `pages/staff-dimission/eleContractList/index?pageTag=${key}` })
  }
  return (
    <View className={styles.wrap}>
      {list.map(item => (
        <View
          className={styles.item}
          key={item.title}
          onClick={() => {
            toEelSignaturePage(item.key)
          }}
        >
          <Image className={styles.img} src={item.img} />
          <Text className={styles.text}>{item.title}</Text>
        </View>
      ))}
    </View>
  )
}

export default withPage(Index)
