/*
 * @Author: your name
 * @Date: 2021-09-08 15:27:38
 * @LastEditTime: 2021-11-23 10:00:39
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\country\index.tsx
 */
import { useState, useRef, useEffect } from 'react'
import { View, Text, Input, ScrollView, Icon } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import { BottomBtn, withPage } from '@components'
import { getScrollStyle } from '@utils/transforms'
import { users } from '@apis/users'
import styles from './index.module.scss'

const Index = () => {
  const { eventName } = useRouter<{ eventName: string }>().params
  const [country, setCountry] = useState<defs.users.CountryInfo>()
  const [search, setSearch] = useState('')
  const inputRef = useRef<any>()
  const { result } = users.user.getCountryInfo.useRequest(
    {
      countryName: search
    },
    {
      deps: [search]
    }
  )
  const scrollStyle = getScrollStyle({ bottom: 120, top: 200 })
  useEffect(() => {}, [])
  const handleSumit = () => {
    if (country?.countryName) {
      console.log(eventName)
      let obj:any = {}
      const countryIds = eventName
      if(eventName == 'eduCountry') {
        obj = {
          [countryIds]: country.countryId,
          eduCountryName: country.countryName
        }
      }else {
        obj = {
          [countryIds]: country.countryId,
          countryName: country.countryName
        }
      }
      // const obj: any = {
      //   [countryIds]: country.countryId,
      //   countryName: country.countryName
      // }
      console.log(obj)
      // Taro.eventCenter.trigger(eventName, { countryIds: country.countryId, countryName: country.countryName })
      Taro.eventCenter.trigger(eventName, obj)
      Taro.navigateBack()
      return
    } else {
      Taro.showToast({
        title: '请选择国家',
        icon: 'none'
      })
    }
  }
  return (
    <View className={styles.wrap}>
      <View className={styles.header}>
        <Text className={styles.header_title}>
          已选国家：
          <Text className={styles.header_city}>{country?.countryName}</Text>
        </Text>
        <View className={styles.input_wrap}>
          <Input className={styles.input} placeholder='请输入国家名称' ref={inputRef} />
          <View
            className={styles.search}
            onClick={() => {
              const value = inputRef.current.tmpValue || inputRef.current.value
              setSearch(value)
            }}
          >
            <Icon size='20' type='search' color='#fff' />
          </View>
        </View>
      </View>
      <ScrollView style={scrollStyle} scrollY>
        {result?.data?.map(item => (
          <View key={item.countryId} className={styles.item} onClick={() => setCountry(item)}>
            <Text className={styles.item_title}>{item.countryName}</Text>
          </View>
        ))}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '确定',
            onClick: handleSumit
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
