/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-08-29 16:11:48
 * @LastAuthor: 王正荣
 * @LastTime: 2021-09-06 16:05:53
 * @message: 
 */
import { View, Image, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import add from '@assets/bankCard/add2.png'
import { withPage } from '@components/page'
import styles from './index.module.scss'

const detail = () => {
  Taro.navigateTo({ url: '/pages/bank/edit/index?type=create' })
}

const Index = () => {
  return (
    <View className={styles.wrap}>
      <View className={styles.sun_layout_main}>
        <View className={styles.box_ticket_item}>
          <View>
            <View className={styles.list_text}>
              <View className={styles.list_text_item}>
                <Button className={styles.sun_btn} onClick={detail}>
                  <View className={styles.imgcss}>
                    <Image className={styles.img} src={add} />
                  </View>
                </Button>
              </View>
              <View className={styles.list_text_item}>
                <View className={styles.centercss}>
                  暂无银行卡信息，请添加银行卡
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  )
}

export default withPage(Index)
