.home {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.banner {
  width: 750px;
  height: 308px;
  margin-bottom: 30px;
}
.wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: wrap;
}
.item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 250px;
  margin-top: 30px;
  margin-bottom: 30px;
}

.item_img {
  width: 100px;
  height: 100px;
}
.item_title {
  margin-top: 30px;
  font-size: 24px;
  color: #333333;
}

.svg {
  width: 60px;
  height: 60px;
  margin-left: 100px;
  margin-top: 100px;
}
.checkbox {
  width: 5px;
  height: 5px
}
.tips{
  font-size: 24px;
  margin-left: 12px;
}
.arrow {
  height: 14px;
  width: 35px;
  margin-left: 17px;
}
.picker {
  position: relative;
  padding: 15px 30px;
  width: 86vw;
  background-color: #ffffff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #ccc;
  border-radius: 8px;
  margin: 30px 20px;
}
.text{
  font-size: 28px;
  width: 90vw;
  margin: 10px 0px;
}