/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-08-24 17:29:28
 * @LastAuthor: 王正荣
 * @LastTime: 2021-09-17 15:56:08
 * @message:
 */
import { useEffect, useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import bgTop from '@assets/appointment/bg-top.jpg'
import icon_search from '@assets/icon/icon-search.png'
import dayjs from 'dayjs'
import { TimePicker, TimePickerProps } from '.'
import styles from './index.module.scss'

interface DateRangePickerProps {
  // 默认name是startDate,endDate
  pickerProps?: [TimePickerProps, TimePickerProps]
  onSearchHandle?: ({ }: any) => void
  children?: any
  bgType?: 1 | 2, // 1高度 256 2 高度 286;
  sendMonth?: string
}
const DateRangePicker = (props: DateRangePickerProps) => {
  const { pickerProps, onSearchHandle, bgType = 1, sendMonth} = props
  const std = pickerProps?.[0]?.name || 'startDate'
  const end = pickerProps?.[0]?.name || 'endDate'
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  const formSumbit = () => {
    if (dayjs(startDate).isAfter(dayjs(endDate))) {
      return Taro.showToast({
        title: '对不起，您选择的起始时间大于结束时间',
        mask: true,
        icon: 'none'
      })
    }
    onSearchHandle && onSearchHandle({ startDate, endDate })
  }
  const value1 = pickerProps?.[0]?.value
  const value2 = pickerProps?.[1]?.value

  useEffect(() => {
    if (value2 !== undefined && value1 !== undefined) {
      setStartDate(value1)
      setEndDate(value2)
      // onSearchHandle && onSearchHandle({ startDate: value1, endDate: value2 })
    } else {
      // 默认选中当月
      const thisMonth = dayjs().format('YYYY-MM')
      setStartDate(dayjs().subtract(6,'month').format('YYYY-MM'))
      // setStartDate(thisMonth)
      setEndDate(thisMonth)
      onSearchHandle && onSearchHandle({ startDate: thisMonth, endDate: thisMonth })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value1, value2])
  useEffect(() => {
    if (!sendMonth) return
    setStartDate(sendMonth)
    setEndDate(sendMonth)
  }, [sendMonth])

  return (
    <View>
      <View className={styles.wrap}>
        <Image className={styles.bg_img} src={bgTop} style={{ height: Taro.pxTransform(bgType === 1 ? 256 : 286) }} />
        <View className={styles.content}>
          <View className={styles.date_range_picker}>
            <TimePicker
              {...pickerProps?.[0]}
              name={std}
              value={startDate}
              onChange={e => setStartDate(e.detail.value)}
            />
            <Text className={styles.search_line}></Text>
            <TimePicker {...pickerProps?.[1]} name={end} value={endDate} onChange={e => setEndDate(e.detail.value)} />
            <View className={styles.search_btn} onClick={() => formSumbit()}>
              <Image className={styles.search_icon} src={icon_search} />
            </View>
          </View>
          {props.children}
        </View>
      </View>
    </View>
  )
}
export { DateRangePicker }
