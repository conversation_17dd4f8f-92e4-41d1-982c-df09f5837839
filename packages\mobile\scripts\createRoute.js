const fs = require('fs')
const path = require('path')
const options = require('../config/config')
const originPath = path.resolve(__dirname, '..', 'src/pages')
const rootPageName = options.route.rootPageName || 'pages'
const prefix = options.route.prefix || 'index'
const INDEX_PAGE = () => {
  const suffixOption = options.route.suffix || []
  const suffix = ['.ts', '.js', '.tsx', '.jsx'].concat(suffixOption)
  return suffix.map(fileSuffix => `${prefix}${fileSuffix}`)
}
const isIndexPage = (inputPath, fileName) =>
  fs.statSync(path.resolve(inputPath)).isFile() && INDEX_PAGE(options).includes(fileName)

const createRoute = innerPath => {
  const rootPageName = options.route.rootPageName || 'pages'
  const filePath = innerPath.replace(/\\/g, '/')
  const startIndexOf = filePath.indexOf(rootPageName)
  const endIndexOf = filePath.lastIndexOf('.')
  return filePath.slice(startIndexOf, endIndexOf)
}
const getDir = (pathArg, pageList) => {
  const inputPath = options.route.inputPath || pathArg
  try {
    const fileDirList = fs.readdirSync(inputPath)
    fileDirList.forEach(async fileDir => {
      //内部路由
      const innerPath = path.resolve(inputPath, `${fileDir}`)
      if (isIndexPage(innerPath, fileDir)) {
        //
        const returnPath = createRoute(innerPath)
        pageList.push(returnPath)
      } else {
        getDir(innerPath, pageList)
      }
    })
  } catch (error) {}
  // return pageList
}
const customRoutes = {}
const pages = []
;(async () => {
  getDir(originPath, pages)
  pages.forEach(page => {
    customRoutes[`/${page}`] = page.replace(rootPageName, '').replace(new RegExp('.' + prefix + '$'), '')
  })
  fs.writeFileSync(
    path.resolve(__dirname, '..', `config/customRoutes.js`),
    `module.exports = ${JSON.stringify(customRoutes || {})}`
  )
})()
