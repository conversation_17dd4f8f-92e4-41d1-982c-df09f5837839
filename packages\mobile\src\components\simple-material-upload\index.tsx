import { FunctionComponent, useState, useEffect } from 'react'
import { View, Text, Image, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import * as imageConversion from 'image-conversion'
import { BaseUrl } from '../../../config/env'
import { getGlobalData } from '@utils'
import { upload } from '@apis/upload'
import styles from './index.module.scss'

// 材料信息接口
export interface MaterialInfo {
  materialsId: string
  materialsName: string
  isOriginal: string // '0': 复印件, '1': 原件
  materialsAccount: number
  isReturn: string // '0': 不返还, '1': 返还
  isRequired?: boolean
}

// 文件信息接口
export interface FileInfo {
  fileName: string
  filePath: string
  fileUrl: string
  bookImageId?: number
  uploadTime?: string
}

// 组件属性接口
interface SimpleMaterialUploadProps {
  material: MaterialInfo
  files?: FileInfo[]
  uuid: string
  onFileChange?: (files: FileInfo[]) => void
  onUploadStart?: () => void
  onUploadSuccess?: (file: FileInfo) => void
  onUploadError?: (error: string) => void
  onDeleteSuccess?: () => void
}

// 上传状态枚举
enum UploadStatus {
  IDLE = 'idle',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error'
}

const SimpleMaterialUpload: FunctionComponent<SimpleMaterialUploadProps> = ({
  material,
  files = [],
  uuid,
  onFileChange,
  onUploadStart,
  onUploadSuccess,
  onUploadError,
  onDeleteSuccess
}) => {
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>(UploadStatus.IDLE)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [errorMessage, setErrorMessage] = useState('')
  const { openId, accountId } = getGlobalData<'account'>('account')

  // 获取材料类型标签
  const getTypeLabel = () => {
    return material.isOriginal === '1' ? '原件' : '电子版'
  }



  // 压缩图片
  const compressImage = async (file: File): Promise<Blob> => {
    try {
      return await imageConversion.compressAccurately(file, { 
        size: 300, 
        width: 750,
        type: imageConversion.EImageType.JPEG,
        quality: 0.8
      })
    } catch (error) {
      console.error('图片压缩失败:', error)
      throw new Error('图片压缩失败')
    }
  }

  // 上传文件
  const uploadFile = async (compressedFile: Blob, originalFileName: string) => {
    const PlatformInfo = sessionStorage.getItem('PlatformInfo')
    const globalToken = PlatformInfo ? JSON.parse(PlatformInfo).globalToken : undefined
    
    return new Promise<any>((resolve, reject) => {
      Taro.uploadFile({
        url: `${BaseUrl}/wx-upload/appointment/uploadAppointmentFileByForm`,
        name: 'file',
        filePath: '', // Taro会自动处理
        header: { 
          wxGlobalToken: globalToken || undefined 
        },
        formData: {
          file: compressedFile,
          openId,
          accountId,
          businessId: uuid,
          bookImageId: 0,
          fileName: originalFileName,
          materialsId: material.materialsId
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (data.code === '200') {
              resolve(data.resultObj)
            } else {
              reject(new Error(data.errorMsg || '上传失败'))
            }
          } catch (error) {
            reject(new Error('响应解析失败'))
          }
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '网络错误'))
        }
      })
    })
  }

  // 选择并上传图片
  const handleUpload = async () => {
    try {
      setUploadStatus(UploadStatus.UPLOADING)
      setUploadProgress(0)
      setErrorMessage('')
      onUploadStart?.()

      const res = await Taro.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera']
      })

      if (res.tempFiles && res.tempFiles.length > 0) {
        const file = res.tempFiles[0].originalFileObj as File
        const originalFileName = file.name || `material_${material.materialsId}_${Date.now()}.jpg`

        // 压缩图片
        setUploadProgress(30)
        const compressedFile = await compressImage(file)

        // 上传文件
        setUploadProgress(60)
        const result = await uploadFile(compressedFile, originalFileName)

        setUploadProgress(100)
        setUploadStatus(UploadStatus.SUCCESS)

        // 构造文件信息
        const newFile: FileInfo = {
          fileName: result.fileName || originalFileName,
          filePath: result.relPath || '',
          fileUrl: result.url || `${BaseUrl}/wx-upload/view${result.relPath}`,
          bookImageId: result.bookImageId,
          uploadTime: new Date().toISOString()
        }

        onUploadSuccess?.(newFile)
        onFileChange?.([...files, newFile])

        Taro.showToast({
          title: '上传成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('上传失败:', error)
      setUploadStatus(UploadStatus.ERROR)

      // 设置友好的错误提示，不暴露具体的接口错误信息
      const friendlyErrorMsg = '上传失败，请重试'
      setErrorMessage(friendlyErrorMsg)

      // 显示友好的错误提示
      Taro.showToast({
        title: friendlyErrorMsg,
        icon: 'none',
        duration: 2000
      })

      // 通知父组件上传失败（传递原始错误供调试）
      onUploadError?.(error)
    }
  }
  // 删除文件
  const handleDelete = async (fileIndex: number) => {
    try {
      const fileToDelete = files[fileIndex]
      if (fileToDelete.bookImageId) {
        await upload.fileUploader.deleteAppointmentFile.request({
          bookImageId: fileToDelete.bookImageId
        })
      }

      const newFiles = files.filter((_, index) => index !== fileIndex)
      onFileChange?.(newFiles)
      onDeleteSuccess?.()

      Taro.showToast({
        title: '删除成功',
        icon: 'success'
      })
    } catch (error) {
      Taro.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  }



  // 下载文件
  const handleDownload = (file: FileInfo) => {
    if (file.fileUrl) {
      Taro.downloadFile({
        url: file.fileUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            Taro.showToast({
              title: '下载成功',
              icon: 'success'
            })
          }
        },
        fail: () => {
          Taro.showToast({
            title: '下载失败',
            icon: 'none'
          })
        }
      })
    }
  }

  return (
    <View className={styles.container}>
      {/* 材料标题行 */}
      <View className={styles.titleRow}>
        <View className={styles.titleLeft}>
          <Text className={styles.materialIndex}>{material.materialsAccount}.</Text>
          <Text className={styles.materialName}>{material.materialsName}</Text>
        </View>
        <Text className={styles.typeLabel}>{getTypeLabel()}</Text>
      </View>

      {/* 文件图标区域 */}
      <View className={styles.fileIconArea}>
        <View
          className={styles.fileIcon}
          onClick={() => {
            if (files.length > 0 && files[0].fileUrl) {
              Taro.previewImage({
                current: files[0].fileUrl,
                urls: [files[0].fileUrl]
              })
            }
          }}
        >
          <Text className={styles.fileIconText}>📄</Text>
          <Text className={styles.fileIconLabel}>{material.materialsName}</Text>
        </View>

        {/* 删除按钮 - 仅在有文件时显示 */}
        {files.length > 0 && (
          <View className={styles.deleteIcon} onClick={() => handleDelete(0)}>
            <Text className={styles.deleteIconText}>✕</Text>
          </View>
        )}
      </View>

      {/* 文件信息或占位文字 */}
      <View className={styles.fileInfoArea}>
        {files.length > 0 ? (
          <>
            <Text className={styles.fileName}>{files[0].fileName}</Text>
            <Text className={styles.fileDescription}>
              条码单号：{files[0].bookImageId || 'JD' + Date.now()}
            </Text>
          </>
        ) : (
          <Text className={styles.placeholderText}>请上传材料</Text>
        )}
      </View>

      {/* 上传进度 */}
      {uploadStatus === UploadStatus.UPLOADING && (
        <View className={styles.uploadProgress}>
          <View className={styles.progressBar}>
            <View
              className={styles.progressFill}
              style={{ width: `${uploadProgress}%` }}
            />
          </View>
          <Text className={styles.progressText}>{uploadProgress}%</Text>
        </View>
      )}

      {/* 错误信息 */}
      {uploadStatus === UploadStatus.ERROR && errorMessage && (
        <View className={styles.errorMessage}>
          <Text className={styles.errorText}>{errorMessage}</Text>
        </View>
      )}
      {/* 底部操作按钮 */}
      <View className={`${styles.bottomActions} ${files.length > 0 ? styles.singleButton : ''}`}>
        {/* 上传按钮 - 仅在没有文件时显示 */}
        {files.length === 0 && (
          <Button
            className={styles.uploadBtn}
            onClick={handleUpload}
            disabled={uploadStatus === UploadStatus.UPLOADING}
          >
            <Image src={require('@assets/order-upload.png')} className={styles.btnIcon} />
            <Text className={styles.btnText}>
              {uploadStatus === UploadStatus.UPLOADING ? '上传中...' : '上传材料'}
            </Text>
          </Button>
        )}

        {/* 下载示例按钮 - 始终显示 */}
        <Button
          className={styles.downloadBtn}
          onClick={() => handleDownload(files[0])}
        >
          <Image src={require('@assets/dow.png')}  className={styles.btnIcon} />
          <Text className={styles.btnText}>下载示例</Text>
        </Button>
      </View>
    </View>
  )
}

export default SimpleMaterialUpload
