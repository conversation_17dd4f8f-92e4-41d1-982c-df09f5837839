/**
 * @description 签署电子离职材料
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
export const path = '/wx-ncmp/elecsign/signQuitMaterial';
export const method = 'POST';
export const request = (
  data: defs.ncmp.EcQuit,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.EcQuit,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
