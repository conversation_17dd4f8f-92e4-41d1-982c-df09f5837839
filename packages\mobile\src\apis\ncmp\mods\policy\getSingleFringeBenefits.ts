/**
 * @description 获取政策
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.FringeBenefitsVO>;
export const path = '/wx-ncmp/policy/getSingleFringeBenefits';
export const method = 'POST';
export const request = (
  data: defs.ncmp.FringeBenefitsQuery,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.FringeBenefitsQuery,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
