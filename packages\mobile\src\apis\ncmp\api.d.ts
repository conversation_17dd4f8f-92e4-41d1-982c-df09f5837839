type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};

declare namespace defs {
  export namespace ncmp {
    export class AcCheckLog {
      /** 外勤签到地址（实际打卡） */
      checkAdd?: string;

      /** 打卡记录id */
      checkLogId?: number;

      /** 外勤签到备注,补签备注 */
      checkRemark?: string;

      /** 打卡时间 */
      checkTime?: string;

      /** 打卡类型（1上班 2下班 3外勤） */
      checkType?: number;

      /** 创建者 */
      createBy?: number;

      /** 创建时间 */
      createDt?: string;

      /** 客户id */
      custId?: number;

      /** 数据来源(1正常打卡2补签3导入) */
      dataSource?: number;

      /** 部门id */
      deptId?: number;

      /** 设备型号 */
      deviceDes?: string;

      /** 设备id字符 */
      deviceId?: string;

      /** 员工id */
      eosEmpId?: number;

      /** 证件号码 */
      idCardNum?: string;

      /** 证件类型 */
      idCardType?: number;

      /** 删除标志（0代表存在 2代表删除） */
      isDeleted?: number;

      /** 纬（实际打卡） */
      latitude?: string;

      /** 办公地址id */
      locSetId?: number;

      /** 打卡地点描述 */
      locSetName?: string;

      /** 经度（实际打卡） */
      longitude?: string;

      /** 外勤签到图片url */
      picUrl?: string;

      /** 备注 */
      remark?: string;

      /** 更新者 */
      updateBy?: number;

      /** 更新时间 */
      updateDt?: string;
    }

    export class AcHolApply {
      /** 申请id */
      applyId?: number;

      /** 申请时间 */
      applyTime?: string;

      /** 孩子出生日期 */
      childBirthday?: string;

      /** 婴儿数量 */
      childNumber?: number;

      /** 孩子预产期 */
      childPreborn?: string;

      /** 创建人 */
      createBy?: string;

      /** 创建时间 */
      createDt?: string;

      /** 客户id */
      custId?: number;

      /** 部门id */
      deptId?: number;

      /** 结束日期 */
      endTime?: string;

      /** 员工id */
      eosEmpId?: number;

      /** 审批结束时间 */
      examOverTime?: string;

      /** 审批是否结束（0未结束/1已结束） */
      examStepIsOver?: number;

      /** 请假折合的天数 */
      holDays?: number;

      /** 请假时数 */
      holHours?: number;

      /** 请假类型id */
      holId?: number;

      /** 请假名称 */
      holName?: string;

      /** 是否删除（0否 1是） */
      isDeleted?: number;

      /** length */
      length?: number;

      /** marriageRegistrationDay */
      marriageRegistrationDay?: string;

      /** 备注 */
      remark?: string;

      /** 流水号 */
      serialNum?: string;

      /** 副本版本号 */
      sonVer?: number;

      /** start */
      start?: number;

      /** 开始日期 */
      startTime?: string;

      /** 审批状态（1审批通过 2待审批 3驳回 4撤销） */
      state?: number;

      /** 更新人 */
      updateBy?: string;

      /** 更新时间 */
      updateDt?: string;

      /** 显示时长 */
      viewHours?: string;
    }

    export class AveSalary {
      /** 月度平均工资 */
      avgPayMon?: string;

      /** 年度平均工资 */
      avgPayYear?: string;

      /** 执行时间 */
      execStartTime?: string;

      /** 最低工资（小时）分6档填入 1档 */
      minHour1?: string;

      /** minHour2 */
      minHour2?: string;

      /** minHour3 */
      minHour3?: string;

      /** minHour4 */
      minHour4?: string;

      /** minHour5 */
      minHour5?: string;

      /** minHour6 */
      minHour6?: string;

      /** 最低工资（小时）分6档填入 1档适用区县 */
      minHourCounty1?: string;

      /** minHourCounty2 */
      minHourCounty2?: string;

      /** minHourCounty3 */
      minHourCounty3?: string;

      /** minHourCounty4 */
      minHourCounty4?: string;

      /** minHourCounty5 */
      minHourCounty5?: string;

      /** minHourCounty6 */
      minHourCounty6?: string;

      /** 最低工资（月度）分6档填入，1档 */
      minMon1?: string;

      /** minMon2 */
      minMon2?: string;

      /** minMon3 */
      minMon3?: string;

      /** minMon4 */
      minMon4?: string;

      /** minMon5 */
      minMon5?: string;

      /** minMon6 */
      minMon6?: string;

      /** 最低工资（月度）分6档填入，1档 适用区县 */
      minMonCounty1?: string;

      /** minMonCounty2 */
      minMonCounty2?: string;

      /** minMonCounty3 */
      minMonCounty3?: string;

      /** minMonCounty4 */
      minMonCounty4?: string;

      /** minMonCounty5 */
      minMonCounty5?: string;

      /** minMonCounty6 */
      minMonCounty6?: string;

      /** 省份/城市 */
      pcName?: string;

      /** 所属年份 */
      yearDate?: string;
    }

    export class AveSalaryStr {
      /** str1 */
      str1?: string;

      /** str2 */
      str2?: string;

      /** str3 */
      str3?: string;
    }

    export class BusNameClass {
      /** 业务项目id */
      bussNameClassId?: string;

      /** 业务项目名称 */
      bussNameClassName?: string;
    }

    export class BusinessSubType {
      /** 小类id */
      busSubtypeId?: string;

      /** 小类名称 */
      busSubtypeName?: string;

      /** 大类id */
      busTypeId?: string;

      /** 大类名称 */
      busTypeName?: string;

      /** 业务项目id */
      bussNameClassId?: string;

      /** 业务项目名称 */
      bussNameClassName?: string;

      /** 所属类型 */
      categoryId?: string;

      /** 雇员id */
      empId?: string;

      /** 是否可预约 */
      isBooked?: string;

      /** 个人空表模板 */
      pBlankTemplatePath?: string;

      /** 个人空表模板名 */
      pBlankTemplatePathName?: string;

      /** 个人样本模板 */
      pSampleTemplatePath?: string;

      /** 个人样本模板名 */
      pSampleTemplatePathName?: string;

      /** 业务说明 */
      remark?: string;
    }

    export class BusinessSubTypeMailQuery {
      /** accountId */
      accountId?: string;

      /** busSubtypeIds */
      busSubtypeIds?: string;

      /** emailAddress */
      emailAddress?: string;

      /** openId */
      openId?: string;
    }

    export class BusinessType {
      /** 大类id */
      busTypeId?: string;

      /** 大类名称 */
      busTypeName?: string;

      /** 业务项目名称id */
      busnameClassId?: string;

      /** 所属类型 */
      categoryId?: string;

      /** 城市id */
      cityId?: string;

      /** 城市名称 */
      cityName?: string;
    }

    export class City {
      /** cityId */
      cityId?: string;

      /** cityName */
      cityName?: string;
    }

    export class EbmApplicationNode {
      /** 实际办理天数 */
      actualProcessDays?: number;

      /** 业务办理申请ID */
      applicationId?: number;

      /** 主键 */
      applicationNodeId?: number;

      /** 业务办理内容ID */
      busCityConfigId?: number;

      /** 节点ID */
      busCityNodeConfigId?: number;

      /** 节点名称 */
      busCityNodeConfigName?: string;

      /** 业务节点顺序 */
      busCityNodeNum?: number;

      /** createBy */
      createBy?: string;

      /** createDt */
      createDt?: string;

      /** 失败原因 */
      failureReason?: string;

      /** 政府性收费金额 */
      governFee?: string;

      /** 是否支付津贴待遇(1是、0否) */
      isAllowance?: string;

      /** 是否支付津贴待遇(1是、0否) */
      isAllowanceName?: string;

      /** 是否收取材料(1是、0否) */
      isCollectMaterial?: string;

      /** 是否收取材料(1是、0否) */
      isCollectMaterialName?: string;

      /** isDeleted */
      isDeleted?: string;

      /** 是否有政府性收费（1是、0否） */
      isGovernFee?: string;

      /** 是否有政府性收费（1是、0否） */
      isGovernFeeName?: string;

      /** 是否已发送提醒 0-未提醒 1-已提醒 */
      isReminded?: number;

      /** mimicBy */
      mimicBy?: string;

      /** 办理结果 1 成功 2失败 */
      nodeResult?: number;

      /** 节点业务进度SELECT * FROM BD_BASE_DATA WHERE TYPE = 12336 */
      nodeStatus?: number;

      /** 办理过程 */
      processDescription?: string;

      /** proxyBy */
      proxyBy?: string;

      /** 提交后道办理时间 */
      submitToBackendTime?: string;

      /** 办理周期(正整数 天) */
      transactPeriod?: string;

      /** updateBy */
      updateBy?: string;

      /** updateDt */
      updateDt?: string;
    }

    export class EbmApplicationNodeMaterial {
      /** 主键 */
      applicationMaterialId?: number;

      /** 员工业务办理节点ID */
      applicationNodeId?: number;

      /** 材料收集进度 */
      collectionProgress?: string;

      /** createBy */
      createBy?: string;

      /** createDt */
      createDt?: string;

      /** isDeleted */
      isDeleted?: string;

      /** 是否通知上传 0 否 1 是 */
      isNotifyUpload?: number;

      /** 是否原件(1是、0否) */
      isOriginal?: number;

      /** 原件是否已寄出 0 否 1 是 */
      isOriginalSent?: number;

      /** 是否返还材料(1是、0否) */
      isReturnMaterial?: string;

      /** 材料确认情况 1 已确认、2 材料修改、3补充材料 */
      materialConfirmStatus?: number;

      /** 材料份数 */
      materialCount?: number;

      /** 材料模版存文件id */
      materialFileId?: string;

      /** 材料名称 */
      materialName?: string;

      /** 材料上传ID */
      materialUploadId?: number;

      /** 材料ID */
      materialsId?: number;

      /** mimicBy */
      mimicBy?: string;

      /** proxyBy */
      proxyBy?: string;

      /** 用印签字方(1易才章、2客户章、3人员签名 */
      signatoryParty?: string;

      /** 模版名称 */
      templateName?: string;

      /** updateBy */
      updateBy?: string;

      /** updateDt */
      updateDt?: string;
    }

    export class EbmApplicationNodeShipment {
      /** 员工业务办理节点材料上传ID逗号分隔多个 */
      applicationMaterialIds?: string;

      /** 员工业务办理节点ID */
      applicationNodeId?: number;

      /** 快递公司 select * from BD_BASE_DATA where type =200 */
      courierCompany?: number;

      /** createBy */
      createBy?: string;

      /** createDt */
      createDt?: string;

      /** isDeleted */
      isDeleted?: string;

      /** mimicBy */
      mimicBy?: string;

      /** proxyBy */
      proxyBy?: string;

      /** 收件人 */
      recipient?: string;

      /** 收件人电话 */
      recipientPhone?: string;

      /** 主键 */
      shipmentId?: number;

      /** 寄件时间 */
      shipmentTime?: string;

      /** 寄件类型 1 员工来件 ， 2客户HR来件，3客服至客服件，4客服退回员工 */
      shipmentType?: number;

      /** 本次寄件勾选的材料id */
      tickMaterialsId?: string;

      /** 快递单号 */
      trackingNumber?: string;

      /** updateBy */
      updateBy?: string;

      /** updateDt */
      updateDt?: string;
    }

    export class EbmApplicationNodeVoucher {
      /** 员工业务办理节点ID */
      applicationNodeId?: number;

      /** createBy */
      createBy?: string;

      /** createDt */
      createDt?: string;

      /** isDeleted */
      isDeleted?: string;

      /** mimicBy */
      mimicBy?: string;

      /** proxyBy */
      proxyBy?: string;

      /** updateBy */
      updateBy?: string;

      /** updateDt */
      updateDt?: string;

      /** 凭证文件ID */
      voucherFileId?: number;

      /** 主键 */
      voucherId?: number;

      /** 凭证数据来源 1 HRO 2 EOS */
      voucherType?: number;
    }

    export class EbmBusinessApplicationDTO {
      /** 主键 申请id */
      applicationId?: number;

      /** 接单客服ID */
      assigneeCsId?: number;

      /** 接单客服名称 */
      assigneeCsName?: string;

      /** 派单部门名称 */
      assignerDepartmentName?: string;

      /** 后道客服 */
      bankendCsId?: number;

      /** 后道客服名称 */
      bankendCsName?: string;

      /** 各地业务内容ID */
      busCityConfigId?: number;

      /** 业务内容 */
      busContent?: string;

      /** 业务项目 */
      busnameClassId?: string;

      /** 业务项目 */
      busnameClassName?: string;

      /** 业务类型 */
      categoryId?: string;

      /** 业务类型 */
      categoryName?: string;

      /** 收取金额 */
      chargeAmount?: string;

      /** 城市名称 */
      cityName?: string;

      /** 是否客户端显示(客户端可见1 ，不可见0) */
      clientShowState?: string;

      /** 是否客户端显示(客户端可见1 ，不可见0) */
      clientShowStateName?: string;

      /** 创建人名称 */
      createByName?: string;

      /** 创建时间 */
      createDt?: string;

      /** 当前办理节点 */
      currentNode?: number;

      /** 客户编号 */
      custId?: number;

      /** 客户名称 */
      custName?: string;

      /** 缴费实体ID */
      custPayEntityId?: string;

      /** 缴费实体名称 */
      custPayEntityName?: string;

      /** 唯一号 */
      empCode?: string;

      /** 上下岗ID */
      empHireSepId?: number;

      /** 员工ID */
      empId?: number;

      /** 姓名 */
      empName?: string;

      /** 到期天数 */
      expireDays?: number;

      /** 办结人 */
      finishByName?: string;

      /** 办结时间 */
      finishDate?: string;

      /** 身份证号 */
      idCardNum?: string;

      /** 是否收取业务费用 */
      isChargeBusiness?: number;

      /** 是否收取业务费用名称 */
      isChargeBusinessName?: string;

      /** 是否收取客户费用 0 否 1是 */
      isChargeCust?: number;

      /** 是否收取客户费用名称 */
      isChargeCustName?: string;

      /** 是否确认办理 0 否 1是 */
      isConfirmHandle?: number;

      /** 是否单立户  0否 1是 */
      isIndependent?: number;

      /** 是否单立户名称 */
      isIndependentName?: string;

      /** 是否自助办理（1是、0否） */
      isSelfTransact?: number;

      /** 材料列表 */
      materialList?: Array<defs.ncmp.EbmApplicationNodeMaterial>;

      /** 是否需要联系员工提交材料 0 否 1 是 */
      needEmpMaterial?: number;

      /** 是否需要联系员工提交材料名称 */
      needEmpMaterialName?: string;

      /** 节点信息 */
      node?: defs.ncmp.EbmApplicationNode;

      /** nodeList */
      nodeList?: Array<defs.ncmp.EbmApplicationNode>;

      /** 进度 */
      progress?: string;

      /** 进度名称 */
      progressName?: string;

      /** 项目客服 */
      projectCsId?: number;

      /** 项目客服名称 */
      projectCsName?: string;

      /** 备注 */
      remark?: string;

      /** 办理结果 1 全部成功 2  全部失败 */
      result?: number;

      /** 办理结果名称 */
      resultName?: string;

      /** 新增时入离职状态 */
      sepStatus?: number;

      /** shipmentList */
      shipmentList?: Array<defs.ncmp.EbmApplicationNodeShipment>;

      /** 业务来源 1 HRO 2 EOS 3 微信端 */
      sourceType?: number;

      /** 业务来源名称 */
      sourceTypeName?: string;

      /** 新增时实做状态 */
      ssStatus?: number;

      /** 业务状态 1办理中 ，2办理完成 ，3取消  4终止 */
      status?: number;

      /** 业务状态名称 */
      statusName?: string;

      /** 手机号 */
      telephone?: string;

      /** 办理对象 */
      transObject?: number;

      /** 办理对象名称 */
      transObjectName?: string;

      /** 办理属性 */
      transProperty?: number;

      /** 办理属性名称 */
      transPropertyName?: string;

      /** 办理对象(1客户、2员工) */
      transactObject?: number;

      /** 办理对象(1客户、2员工) */
      transactObjectName?: string;

      /** 办理属性(1流程业务、2单次业务) */
      transactProperty?: number;

      /** 办理属性(1流程业务、2单次业务) */
      transactPropertyName?: string;

      /** 办理方式(1客户办理、2员工办理 ,逗号分隔字符串 ) */
      transactType?: string;

      /** 办理方式(1客户办理、2员工办理 ,逗号分隔字符串 ) */
      transactTypeStr?: string;

      /** 办理方式(1客户办理、2员工办理 ,逗号分隔字符串 ) */
      transactTypeStrName?: string;

      /** voucherList */
      voucherList?: Array<defs.ncmp.EbmApplicationNodeVoucher>;

      /** 微信端业务进度查询 0 不开通 1 开通 */
      wechatProgressQuery?: number;

      /** 是否微信显示(微信端可见1 ，不可见0) */
      wxShowState?: string;

      /** 是否微信显示(微信端可见1 ，不可见0) */
      wxShowStateName?: string;
    }

    export class EbmBusinessCityConfigDTO {
      /** 各地业务办理id */
      busCityConfigId?: string;

      /** busConfigId */
      busConfigId?: string;

      /** 业务内容 */
      busContent?: string;

      /** 业务办理说明 */
      busContentDesc?: string;

      /** 业务项目 */
      busnameClassId?: string;

      /** 业务项目 */
      busnameClassName?: string;

      /** 业务类型 */
      categoryId?: string;

      /** 业务类型 */
      categoryName?: string;

      /** 城市id */
      cityId?: string;

      /** 城市Name */
      cityName?: string;

      /** 是否客户端显示(客户端可见1 ，不可见0) */
      clientShowState?: string;

      /** 是否客户端显示(客户端可见1 ，不可见0) */
      clientShowStateName?: string;

      /** 实做要求  ，3 在缴4停缴5过期 */
      doRequire?: string;

      /** 实做要求  ，3 在缴4停缴5过期 */
      doRequireName?: string;

      /** 员工自助办理途径 */
      empSelfContent?: string;

      /** 是否有效：  0 无效 1 有效 */
      isCityValid?: string;

      /** 是否有效：  0 无效 1 有效 */
      isCityValidName?: string;

      /** 订单要求，状态1入职未生效2在职3离职 ) */
      orderRequire?: string;

      /** 订单要求，状态1入职未生效2在职3离职 ) */
      orderRequireName?: string;

      /** 缴纳产品要求(200:养老保险，201:医疗保险，202: 失业保险，203:工伤保险，204:生育保险，240: 住房公积金)    存逗号分隔的字符串 */
      productRequire?: string;

      /** 缴纳产品要求(200:养老保险，201:医疗保险，202: 失业保险，203:工伤保险，204:生育保险，240: 住房公积金)    存逗号分隔的字符串 */
      productRequireName?: string;

      /** 办理对象(1客户、2员工) */
      transactObject?: string;

      /** 办理对象(1客户、2员工) */
      transactObjectName?: string;

      /** 办理属性(1流程业务、2单次业务) */
      transactProperty?: string;

      /** 办理属性(1流程业务、2单次业务) */
      transactPropertyName?: string;

      /** 办理方式(1易才办理、2员工办理 ,逗号分隔字符串 ) */
      transactType?: string;

      /** 办理方式(1易才办理、2员工办理 ,逗号分隔字符串 ) */
      transactTypeName?: string;

      /** 是否微信显示(微信端可见1 ，不可见0) */
      wxShowState?: string;

      /** 是否微信显示(微信端可见1 ，不可见0) */
      wxShowStateName?: string;
    }

    export class EbmBusinessCnMaterialDTO {
      /** 各地业务内容id */
      busCityConfigId?: string;

      /** 节点id */
      busCityNodeConfigId?: string;

      /** 主键ID */
      busCnMaterialId?: string;

      /** 是否原件 */
      isOriginal?: string;

      /** 是否原件名称 */
      isOriginalName?: string;

      /** 是否返还材料(1是、0否) */
      isReturnMaterial?: string;

      /** 是否返还材料(1是、0否) */
      isReturnMaterialName?: string;

      /** 材料模版(存文件id) */
      materialFileId?: string;

      /** 材料模版(存文件名称) */
      materialFileName?: string;

      /** 材料数量 */
      materialNum?: string;

      /** 材料编号 */
      materialsId?: string;

      /** 材料名称 */
      materialsName?: string;

      /** 用印签字方(1易才章、2客户章、3人员签名) */
      signatoryParty?: string;

      /** 用印签字方名称 */
      signatoryPartyName?: string;
    }

    export class EbmBusinessMatShipResp {
      /** 材料信息 */
      ebmApplicationNodeMaterialList?: Array<defs.ncmp.EbmApplicationNodeMaterial>;

      /** 寄件信息 */
      ebmApplicationNodeShipmentList?: Array<defs.ncmp.EbmApplicationNodeShipment>;
    }

    export class EbmBusinessResp {
      /** 主键 申请id */
      applicationId?: number;

      /** 主键 节点id */
      applicationNodeId?: number;

      /** 各地业务内容ID */
      busCityConfigId?: number;

      /** 节点ID */
      busCityNodeConfigId?: number;

      /** 业务内容 */
      busContent?: string;

      /** 业务项目 */
      busnameClassId?: string;

      /** 业务项目 */
      busnameClassName?: string;

      /** 业务类型 */
      categoryId?: string;

      /** 业务类型 */
      categoryName?: string;

      /** 员工ID */
      empId?: number;

      /** 办理结果 1 成功 2失败 */
      nodeResult?: number;

      /** 办理结果 1 成功 2失败 */
      nodeResultName?: number;

      /** 节点业务进度SELECT * FROM BD_BASE_DATA WHERE TYPE = 12336 */
      nodeStatus?: number;

      /** 节点业务进度SELECT * FROM BD_BASE_DATA WHERE TYPE = 12336 */
      nodeStatusName?: number;

      /** 提交后道办理时间 */
      submitToBackendTime?: string;
    }

    export class EcBusiness {
      /** 业务Id */
      businessId?: string;

      /** 城市码 */
      cityCode?: string;

      /** 电子业务Id */
      eleBusinessId?: string;

      /** 电子业务名称 */
      eleBusinessName?: string;

      /** 电子签署标志(1未发起、2拟定中、3员工已签署、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废、11客户已签署、12员工已作废) */
      eleBusinessStatus?: number;

      /** 电子业务状态名称 */
      eleBusinessStatusName?: string;

      /** 上下岗id */
      empHireSepId?: string;

      /** 员工id */
      empId?: string;

      /** 姓名 */
      empName?: string;

      /** 身份证号码 */
      idCardNum?: string;

      /** 手机号码 */
      phoneNumber?: string;
    }

    export class EcQuit {
      /** 离职证明电子合同id */
      certificateEleId?: string;

      /** 离职证明电子合同名称 */
      certificateElename?: string;

      /** 电子离职证明状态:1未发起、5已完成、6已过期、9已作废 */
      certificateStatus?: number;

      /** 电子离职证明状态名称:1未发起、5已完成、6已过期、9已作废 */
      certificateStatusName?: string;

      /** 签署URL */
      eleSinUrl?: string;

      /** 上下岗id */
      empHireSepId?: string;

      /** empId */
      empId?: string;

      /** 姓名 */
      empName?: string;

      /** 身份证号码 */
      idCardNum?: string;

      /** 离职材料电子合同id */
      materialEleId?: string;

      /** 离职材料电子合同名称 */
      materialEleName?: string;

      /** 电子离职材料状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废  */
      materialStatus?: number;

      /** 电子离职材料状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废  */
      materialStatusName?: string;

      /** 手机号码 */
      phoneNumber?: string;

      /** 离职任务编号 */
      quitTaskId?: number;
    }

    export class EleContract {
      /** 城市码 */
      cityCode?: string;

      /** 电子合同Id */
      eleContractId?: string;

      /** 电子合同名称 */
      eleContractName?: string;

      /** 电子合同状态 */
      eleContractStatus?: string;

      /** 签署URL */
      eleSinUrl?: string;

      /** 雇员Id */
      empId?: string;

      /** 姓名 */
      empName?: string;

      /** 身份证号码 */
      idCardNum?: string;

      /** 劳动合同id */
      laborContractId?: string;

      /** 手机号码 */
      phoneNumber?: string;
    }

    export class ElecSignResult {
      /** 电子签链接 */
      eleSinUrl?: string;

      /** 管家地址 */
      stewardAddr?: string;

      /** 管家姓名 */
      stewardName?: string;

      /** 管家联系方式 */
      stewardTel?: string;

      /** 管家油箱 */
      stewardmail?: string;
    }

    export class EmpFeeCity {
      /** 城市名称 */
      cityName?: string;

      /** 客户名称 */
      custName?: string;
    }

    export class EmpFeeProcess {
      /** 产品名称列表 */
      products?: string;

      /** 参保状态 */
      statusWx?: string;
    }

    export class FringeBenefitsQuery {
      /** busnameSubtypeId */
      busnameSubtypeId?: string;

      /** categoryId */
      categoryId?: string;
    }

    export class FringeBenefitsVO {
      /** accordingFileNumber1 */
      accordingFileNumber1?: string;

      /** accordingFileNumber2 */
      accordingFileNumber2?: string;

      /** accordingFileNumber3 */
      accordingFileNumber3?: string;

      /** benefitsId */
      benefitsId?: number;

      /** busnameClassId */
      busnameClassId?: number;

      /** busnameClassName */
      busnameClassName?: string;

      /** busnameSubtypeId */
      busnameSubtypeId?: number;

      /** busnameSubtypeName */
      busnameSubtypeName?: string;

      /** busnameTypeId */
      busnameTypeId?: number;

      /** busnameTypeName */
      busnameTypeName?: string;

      /** categoryId */
      categoryId?: number;

      /** categoryName */
      categoryName?: string;

      /** cityId */
      cityId?: number;

      /** cityName */
      cityName?: string;

      /** createBy */
      createBy?: number;

      /** createDt */
      createDt?: string;

      /** crossCityHandle */
      crossCityHandle?: number;

      /** crossCityHandleArea */
      crossCityHandleArea?: string;

      /** crossCityHandleText */
      crossCityHandleText?: string;

      /** crossProvinceHandle */
      crossProvinceHandle?: number;

      /** crossProvinceHandleArea */
      crossProvinceHandleArea?: string;

      /** crossProvinceHandleText */
      crossProvinceHandleText?: string;

      /** crossRegionHandle */
      crossRegionHandle?: number;

      /** crossRegionHandleArea */
      crossRegionHandleArea?: string;

      /** crossRegionHandleText */
      crossRegionHandleText?: string;

      /** eHandleCondition */
      eHandleCondition?: string;

      /** eHandleOfflineProcess */
      eHandleOfflineProcess?: string;

      /** eHandleProcess1 */
      eHandleProcess1?: string;

      /** eHandleProcess2 */
      eHandleProcess2?: string;

      /** eHandleProcess3 */
      eHandleProcess3?: string;

      /** effectiveDate1 */
      effectiveDate1?: string;

      /** effectiveDate2 */
      effectiveDate2?: string;

      /** effectiveDate3 */
      effectiveDate3?: string;

      /** fileName1 */
      fileName1?: string;

      /** fileName2 */
      fileName2?: string;

      /** fileName3 */
      fileName3?: string;

      /** handleForm */
      handleForm?: string;

      /** handleType */
      handleType?: number;

      /** handleTypeText */
      handleTypeText?: string;

      /** handleWindow1 */
      handleWindow1?: string;

      /** handleWindow2 */
      handleWindow2?: string;

      /** handleWindow3 */
      handleWindow3?: string;

      /** isBooked */
      isBooked?: string;

      /** isDeleted */
      isDeleted?: number;

      /** isValid */
      isValid?: string;

      /** makReservations */
      makReservations?: number;

      /** makReservationsText */
      makReservationsText?: string;

      /** orderStatus */
      orderStatus?: number;

      /** orderStatusText */
      orderStatusText?: string;

      /** otherHandleInfo1 */
      otherHandleInfo1?: string;

      /** otherHandleInfo2 */
      otherHandleInfo2?: string;

      /** otherHandleInfo3 */
      otherHandleInfo3?: string;

      /** otherHandleInfo4 */
      otherHandleInfo4?: string;

      /** otherHandleInfo5 */
      otherHandleInfo5?: string;

      /** otherHandleInfo6 */
      otherHandleInfo6?: string;

      /** otherPolicyInfo1 */
      otherPolicyInfo1?: string;

      /** otherPolicyInfo2 */
      otherPolicyInfo2?: string;

      /** otherPolicyInfo3 */
      otherPolicyInfo3?: string;

      /** otherPolicyInfo4 */
      otherPolicyInfo4?: string;

      /** otherPolicyInfo5 */
      otherPolicyInfo5?: string;

      /** otherPolicyInfo6 */
      otherPolicyInfo6?: string;

      /** otherPolicyInfo7 */
      otherPolicyInfo7?: string;

      /** otherPolicyInfo8 */
      otherPolicyInfo8?: string;

      /** otherPolicyInfo9 */
      otherPolicyInfo9?: string;

      /** pHandleCondition */
      pHandleCondition?: string;

      /** pHandleOfflineProcess */
      pHandleOfflineProcess?: string;

      /** pHandleProcess1 */
      pHandleProcess1?: string;

      /** pHandleProcess2 */
      pHandleProcess2?: string;

      /** pHandleProcess3 */
      pHandleProcess3?: string;

      /** payee */
      payee?: string;

      /** personCategoryId */
      personCategoryId?: number;

      /** personCategoryName */
      personCategoryName?: string;

      /** policyFileId1 */
      policyFileId1?: string;

      /** policyFileId2 */
      policyFileId2?: string;

      /** policyFileId3 */
      policyFileId3?: string;

      /** policyFileName1 */
      policyFileName1?: string;

      /** policyFileName2 */
      policyFileName2?: string;

      /** policyFileName3 */
      policyFileName3?: string;

      /** policySource1 */
      policySource1?: string;

      /** policySource2 */
      policySource2?: string;

      /** policySource3 */
      policySource3?: string;

      /** policyUrl1 */
      policyUrl1?: string;

      /** policyUrl2 */
      policyUrl2?: string;

      /** policyUrl3 */
      policyUrl3?: string;

      /** processDifference */
      processDifference?: string;

      /** ssStatus */
      ssStatus?: number;

      /** ssStatusText */
      ssStatusText?: string;

      /** statutoryDeadline */
      statutoryDeadline?: string;

      /** supplementaryInfo1 */
      supplementaryInfo1?: string;

      /** supplementaryInfo2 */
      supplementaryInfo2?: string;

      /** supplementaryInfo3 */
      supplementaryInfo3?: string;

      /** supplementaryInfo4 */
      supplementaryInfo4?: string;

      /** supplementaryInfo5 */
      supplementaryInfo5?: string;

      /** supplementaryInfo6 */
      supplementaryInfo6?: string;

      /** termsContent1 */
      termsContent1?: string;

      /** termsContent2 */
      termsContent2?: string;

      /** termsContent3 */
      termsContent3?: string;

      /** tollHandle */
      tollHandle?: number;

      /** tollHandleText */
      tollHandleText?: string;

      /** tollStandard */
      tollStandard?: string;

      /** updateBy */
      updateBy?: number;

      /** updateDt */
      updateDt?: string;

      /** windowAddress */
      windowAddress?: string;
    }

    export class HttpResult<T0 = any> {
      /** 返回状态code */
      code?: string;

      /** 错误信息 */
      errorMsg?: string;

      /** 具体的返回结果 */
      resultObj?: T0;

      /** suc */
      suc?: boolean;
    }

    export class HttpRpaResult<T0 = any> {
      /** 具体返回内容 */
      msg_response?: defs.ncmp.InnerResponse<defs.ncmp.AveSalaryStr>;

      /** Web API调用成功或失败 ， success->成功 ， 其他->失败 */
      parse_result?: string;
    }

    export class HzSupplyCert {
      /** 审批状态 */
      auditStatus?: string;

      /** 证件id */
      certId?: string;

      /** 证件名称 */
      certName?: string;

      /** 文件名称 */
      fileName?: string;

      /** 文件路径 */
      filePath?: string;

      /** 是否必填 1是 0否 */
      isMust?: string;

      /** 上传文件id */
      mediaId?: string;

      /** openId */
      openId?: string;

      /** 驳回原因 */
      rejectReason?: string;

      /** 主键 */
      supplyCertId?: string;

      /** 批次id */
      supplyId?: string;

      /** 上传方 1客服 2个人 */
      uploadType?: string;
    }

    export class InnerResponse<T0 = any> {
      /** 移出场景内的变量 */
      remove?: T0;

      /** 存进场景内的变量 */
      update?: T0;
    }

    export class Map<T0 = any, T1 = any> {}

    export class MaterialsPackage {
      /** 小类id */
      busSubtypeId?: string;

      /** 大类id */
      busTypeId?: string;

      /** 是否是单位办理材料1 是 0否 */
      isEMaterial?: string;

      /** 是否原件 */
      isOriginal?: string;

      /** 是否是个人办理材料1 是 0否 */
      isPMaterial?: string;

      /** 是否返还给申请人 */
      isReturn?: string;

      /** 材料数量 */
      materialsAccount?: string;

      /** 材料编号 */
      materialsId?: string;

      /** 材料名称 */
      materialsName?: string;

      /** 材料包id */
      packageId?: string;
    }

    export class PersonCategory {
      /** 人员分类id */
      personCategoryId?: string;

      /** 人员分类名称 */
      personCategoryName?: string;
    }

    export class PolicyDetail {
      /** 生效日期 */
      effectiveDate?: string;

      /** 失效日期 */
      expirationDate?: string;

      /** 所属年份 */
      policyTemplateInfoYear?: number;

      /** 服务类型名称 */
      serviceTypeName?: string;

      /** templateGroupRespList */
      templateGroupRespList?: Array<defs.ncmp.PolicyGroup>;

      /** 适用范围:1国家 2省份3城市4特区 */
      templateScope?: number;
    }

    export class PolicyField {
      /** 客户端是否显示 0：不显示,1：显示 */
      clientShowState?: number;

      /** 字段编码 */
      fieldCode?: string;

      /** 字段名称 */
      fieldName?: string;

      /** 字段值 */
      fieldValue?: string;

      /** 附件名称 */
      fileName?: string;

      /** HRO端是否显示 0：不显示,1：显示 */
      hroShowState?: number;

      /** 是否删除 0否 1是 */
      isDeleted?: string;

      /** 字段是否必填 0否 1是 */
      isMust?: number;

      /** 字段选项 */
      items?: string;

      /** 政策模板字段ID */
      policyTemplateFieldId?: string;

      /** 政策模板分组ID */
      policyTemplateGroupId?: string;

      /** 显示顺序 */
      seqNum?: number;

      /** 模板状态  状态:0初始 1有效 2无效 */
      templateState?: number;

      /** 字段类型:1.文本 2.多行文本 3.日期 4.下拉菜单 5.链接 6.附件 7.多选项 8.数字(整数) 9.数字(小数) */
      type?: number;

      /** 超链接名称 */
      urlName?: string;

      /** wx端是否显示 0：不显示,1：显示 */
      wxShowState?: number;
    }

    export class PolicyGroup {
      /** 客户端是否显示 0：不显示,1：显示 */
      clientShowState?: number;

      /** 字段列表 */
      fields?: Array<defs.ncmp.PolicyField>;

      /** 分组编号 */
      groupCode?: string;

      /** HRO端是否显示 0：不显示,1：显示 */
      hroShowState?: number;

      /** 是否删除 0否 1是 */
      isDeleted?: string;

      /** 政策模板分组ID */
      policyTemplateGroupId?: string;

      /** 政策模板ID */
      policyTemplateId?: string;

      /** 显示顺序 */
      seqNum?: number;

      /** 分组名称 */
      templateGroupName?: string;

      /** 模板状态  状态:0初始 1有效 2无效 */
      templateState?: number;

      /** wx端是否显示 0：不显示,1：显示 */
      wxShowState?: number;
    }

    export class PolicyLevelTree {
      /** children */
      children?: Array<defs.ncmp.PolicyLevelTree>;

      /** id */
      id?: string;

      /** name */
      name?: string;

      /** pId */
      pId?: string;
    }

    export class PrivacyParam {
      /** openId */
      openId?: string;

      /** 隐私协议当前最新版本id */
      privacyId?: number;

      /** 隐私协议当前最新版本号 */
      version?: number;
    }

    export class PrivacyRegisterDTO {
      /** 隐私协议当前最新版本的内容,只有当用户没签最新协议时才返回 */
      content?: string;

      /** 隐私协议当前最新版本id,如果返回-1，说明压根没有维护的记录，这时候不能让他同意 */
      privacyId?: number;

      /** 隐私协议登记记录主表id,只有当用户已经签过了最新协议后才返回 */
      privacyRegisterId?: number;

      /** registed */
      registed?: boolean;

      /** 隐私协议当前最新版本号,如果返回-1，说明压根没有维护的记录，这时候不能让他同意 */
      version?: number;
    }

    export class QueryPolicyEncyclopedia {
      /** 城市id集合 */
      cityIds?: Array<string>;

      /** endIndex */
      endIndex?: number;

      /** 政策生效状态 0初始 1待生效 2 生效 3失效 */
      infoState?: number;

      /** 政策生效状态 0初始 1待生效 2 生效 3失效 */
      infoStates?: Array<number>;

      /** 是否全部城市:0否，1是 */
      isAllCity?: number;

      /** 是否全部省份:0否，1是 */
      isAllProvince?: number;

      /** 是否全部特区i:0否，1是 */
      isAllSpecialArea?: number;

      /** 一级名称Id */
      level1Id?: string;

      /** 一级名称 */
      level1Name?: string;

      /** 二级名称Id */
      level2Id?: string;

      /** 二级名称 */
      level2Name?: string;

      /** 是否分页查询（true:分页；false:不分页） */
      pageQuery?: boolean;

      /** 政策模板详情ID */
      policyTemplateInfoId?: number;

      /** 所属年份 */
      policyTemplateInfoYear?: string;

      /** 所属年份 */
      policyTemplateInfoYears?: Array<string>;

      /** 政策标题Id */
      policyTitleId?: string;

      /** 省份城市 */
      provinceCity?: string;

      /** 省份id集合 */
      provinceIds?: Array<string>;

      /** 适用范围:1国家 2省份3城市4特区 */
      queryScopes?: Array<string>;

      /** 服务类型 */
      serviceType?: number;

      /** 服务类型名称 */
      serviceTypeName?: string;

      /** 服务类型集合 */
      serviceTypes?: Array<number>;

      /** 特区id集合 */
      specialAreaIds?: Array<string>;

      /** startIndex */
      startIndex?: number;

      /** 政策详情名称 */
      templateInfoName?: string;

      /** 适用范围:1国家 2省份3城市 */
      templateScope?: string;
    }

    export class RpaEmpFee {
      /** 合计金额 */
      amt?: string;

      /** 没权限提示文本信息 */
      authorityHintMsg?: string;

      /** 社保组类型 1社保 2公积金 */
      category?: string;

      /** 客户id */
      custId?: string;

      /** 客户名称 */
      custName?: string;

      /** 企业金额 */
      eAmt?: string;

      /** 企业基数 */
      eBase?: string;

      /** empFeeProcessList */
      empFeeProcessList?: Array<defs.ncmp.EmpFeeProcess>;

      /** 雇员id */
      empId?: string;

      /** 是否授权 1是 0否 */
      ifAuthority?: boolean;

      /** 是否有数据 1是 0否 */
      ifHasData?: boolean;

      /** 个人金额 */
      pAmt?: string;

      /** 个人基数 */
      pBase?: string;

      /** 参保项目与状态 */
      productStatusName?: string;

      /** 服务月 */
      serviceMonth?: string;
    }

    export class Salary {
      /** 没权限提示文本信息 */
      authorityHintMsg?: string;

      /** 客户名称 */
      custName?: string;

      /** empId */
      empId?: string;

      /** 收入合计 */
      f1?: string;

      /** 本次扣税 */
      f10?: string;

      /** 扣款合计 */
      f2?: string;

      /** 实发合计 */
      f3?: string;

      /** 是否授权 true是 false否 */
      ifAuthority?: boolean;

      /** 是否有数据 true是 false否 */
      ifHasData?: boolean;

      /** sendId */
      sendId?: string;

      /** sendMonth */
      sendMonth?: string;

      /** 发放状态 */
      wageBatchStatusName?: string;

      /** 报税状态 */
      wageTaxStatus?: string;
    }

    export class SsProcess {
      /** 办理时间 */
      createdt?: string;

      /** 社保组 */
      groupName?: string;

      /** 社保组产品 */
      products?: string;

      /** 办理状态 */
      statusWx?: string;
    }

    export class SsProcessDto {
      /** empId */
      empId?: string;

      /** 1 社保，2 公积金 */
      groupType?: string;
    }

    export class StewardInfo {
      /** 地址 */
      address?: string;

      /** 联系方式 */
      contact?: string;

      /** 雇员id（不用） */
      empId?: string;

      /** 邮箱 */
      mail?: string;

      /** 供应商类型（不用） */
      providerType?: string;

      /** 管家姓名 */
      stewardName?: string;
    }

    export class TokenCheck {
      /** 当前用户类型 1:认证用户 2:匿名用户 */
      userType?: number;
    }

    export class WechatHiresepDTO {
      /** 注册账号对应的ID */
      accountId?: string;

      /** 办理城市id */
      cityId?: string;

      /** 城市名称 */
      cityName?: string;

      /** 微信入职办理主表id */
      empHiresepMainId?: string;

      /** 低代码表单的json数据 */
      jsonStr?: string;

      /** 关联照片信息的uuid */
      uuid?: string;
    }
  }
}

declare namespace API {
  export namespace ncmp {
    /**
     * 考勤记录API
     */
    export namespace acEmpRecord {
      /**
        * 查询日考勤记录
查询日考勤记录
        * /wx-ncmp/eos/ac/empRecord/detail/logList
        */
      export namespace detailList {
        export class Params {
          /** custId */
          custId: number;
          /** 年月（202404） */
          stMonth: string;
        }

        export type Response = defs.ncmp.HttpResult<ObjectMap>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 查询月考勤记录
查询月考勤记录
        * /wx-ncmp/eos/ac/empRecord/stmonth/logList
        */
      export namespace monthList {
        export class Params {
          /** custId */
          custId: number;
          /** 年月（202404） */
          stMonth: string;
        }

        export type Response = defs.ncmp.HttpResult<ObjectMap>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * 假期申请相关接口
     */
    export namespace acHolApply {
      /**
        * 撤销假期申请
撤销假期申请
        * /wx-ncmp/eos/hol/apply/cancel
        */
      export namespace cancel {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          ObjectMap<string, ObjectMap>
        >;
        export const request: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.AcHolApply>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.AcHolApply>;
      }

      /**
        * 分页查询假期列表
分页查询假期列表
        * /wx-ncmp/eos/hol/apply/getDetail
        */
      export namespace getDetail {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          ObjectMap<string, ObjectMap>
        >;
        export const request: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.AcHolApply>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.AcHolApply>;
      }

      /**
        * 分页查询假期列表
分页查询假期列表
        * /wx-ncmp/eos/hol/apply/getHolGroupItem
        */
      export namespace getHolGroupItem {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          ObjectMap<string, ObjectMap>
        >;
        export const request: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.AcHolApply>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.AcHolApply>;
      }

      /**
        * 获取申请时长
获取申请时长
        * /wx-ncmp/eos/hol/apply/getHolHours
        */
      export namespace getHolHours {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          ObjectMap<string, ObjectMap>
        >;
        export const request: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.AcHolApply>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.AcHolApply>;
      }

      /**
        * 获取假期列表
获取假期列表
        * /wx-ncmp/eos/hol/apply/getHolidayList
        */
      export namespace getHolidayList {
        export class Params {
          /** custId */
          custId: number;
        }

        export type Response = defs.ncmp.HttpResult<
          ObjectMap<string, ObjectMap>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 分页查询假期列表
分页查询假期列表
        * /wx-ncmp/eos/hol/apply/page
        */
      export namespace page {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          ObjectMap<string, ObjectMap>
        >;
        export const request: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.AcHolApply>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.AcHolApply>;
      }

      /**
        * 保存申请
保存申请
        * /wx-ncmp/eos/hol/apply/save
        */
      export namespace save {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          ObjectMap<string, ObjectMap>
        >;
        export const request: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.AcHolApply,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.AcHolApply>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.AcHolApply>;
      }
    }

    /**
     * 员工业务办理服务
     */
    export namespace ebmBusiness {
      /**
        * 业务内容下拉列表
业务内容下拉列表，传cityId，所属类型categoryId，所属项目busnameClassId
        * /wx-ncmp/ebmbusiness/getBusContentDropdownList
        */
      export namespace getBusContentDropdownList {
        export class Params {
          /** busnameClassId */
          busnameClassId?: string;
          /** categoryId */
          categoryId?: string;
          /** cityId */
          cityId?: string;
        }

        export type Response = defs.ncmp.HttpResult<
          Array<defs.ncmp.EbmBusinessCityConfigDTO>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取材料列表
获取材料列表 传busCityConfigId 各地业务的主键
        * /wx-ncmp/ebmbusiness/getCnMaterialList
        */
      export namespace getCnMaterialList {
        export class Params {
          /** busCityConfigId */
          busCityConfigId: string;
        }

        export type Response = defs.ncmp.HttpResult<
          Array<defs.ncmp.EbmBusinessCnMaterialDTO>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据节点ID查询材料列表和寄件信息
       * /wx-ncmp/ebmbusiness/getNodeInfoByNodeId
       */
      export namespace getNodeInfoByNodeId {
        export class Params {
          /** nodeId */
          nodeId: number;
        }

        export type Response =
          defs.ncmp.HttpResult<defs.ncmp.EbmBusinessMatShipResp>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 更新员工业务办理节点信息
       * /wx-ncmp/ebmbusiness/node/update
       */
      export namespace updateNodeInfo {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<number>;
        export const request: (
          data?: defs.ncmp.EbmBusinessMatShipResp,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EbmBusinessMatShipResp,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EbmBusinessMatShipResp>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.ncmp.EbmBusinessMatShipResp
        >;
      }

      /**
       * 保存员工业务办理申请
       * /wx-ncmp/ebmbusiness/saveApplication
       */
      export namespace saveApplication {
        export class Params {}

        export type Response = defs.ncmp.HttpResult;
        export const request: (
          data?: defs.ncmp.EbmBusinessApplicationDTO,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EbmBusinessApplicationDTO,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EbmBusinessApplicationDTO>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.ncmp.EbmBusinessApplicationDTO
        >;
      }

      /**
        * 查询业务办理列表
查询业务办理列表
        * /wx-ncmp/ebmbusiness/selectEbmBusinessList
        */
      export namespace selectEbmBusinessList {
        export class Params {
          /** endDt */
          endDt?: string;
          /** startDt */
          startDt?: string;
        }

        export type Response = defs.ncmp.HttpResult<
          Array<defs.ncmp.EbmBusinessResp>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Elec Business Controller
     */
    export namespace elecBusiness {
      /**
        * 作废电子业务材料
作废电子业务材料，传empId
        * /wx-ncmp/elecbusiness/delElecBusinessMaterial
        */
      export namespace delElecBusinessMaterial {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
        export const request: (
          data?: defs.ncmp.EcBusiness,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EcBusiness,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EcBusiness>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EcBusiness>;
      }

      /**
        * 获取电子业务列表
查看电子业务接口，传empId
        * /wx-ncmp/elecbusiness/getElecBusinessList
        */
      export namespace getEleContractList {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          Array<defs.ncmp.EleContract>
        >;
        export const request: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EleContract>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EleContract>;
      }

      /**
        * 获取查看电子业务链接
获取查看电子业务链接，传eleContractId、phoneNumber、idCardNum,empId
        * /wx-ncmp/elecbusiness/getElecBusinessUrl
        */
      export namespace getEleContractUrl {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
        export const request: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EleContract>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EleContract>;
      }

      /**
        * 获取签署/作废电子业务链接
获取签署/作废电子业务链接，传eleContractId、phoneNumber、idCardNum,empId
        * /wx-ncmp/elecbusiness/getElecSignBusinessUrl
        */
      export namespace getElecSignBusinessUrl {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
        export const request: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EleContract>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EleContract>;
      }

      /**
        * 签署电子业务材料
签署电子业务材料，传empId
        * /wx-ncmp/elecbusiness/signElecBusinessMaterial
        */
      export namespace signElecBusinessMaterial {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          Array<defs.ncmp.EleContract>
        >;
        export const request: (
          data?: defs.ncmp.EcBusiness,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EcBusiness,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EcBusiness>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EcBusiness>;
      }
    }

    /**
     * Elec Sign Controller
     */
    export namespace elecSign {
      /**
        * 华住补证件上传
华住补证件上传，传supplyCertId，certName，mediaId，openId
        * /wx-ncmp/elecsign/certFileUpload
        */
      export namespace certFileUpload {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.HzSupplyCert>;
        export const request: (
          data?: defs.ncmp.HzSupplyCert,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.HzSupplyCert,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.HzSupplyCert>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.HzSupplyCert>;
      }

      /**
        * form形式传华住补证件上传
form形式华住补证件上传，传supplyCertId，certName，openId
        * /wx-ncmp/elecsign/certFileUploadByForm
        */
      export namespace certFileUploadByForm {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.HzSupplyCert>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 作废电子离职材料
作废电子离职材料，传empId
        * /wx-ncmp/elecsign/delQuitMaterial
        */
      export namespace delQuitMaterial {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
        export const request: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EcQuit>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EcQuit>;
      }

      /**
        * 华住补证件删除
华住补证件删除，传supplyCertId
        * /wx-ncmp/elecsign/deteleLaborSupplyCertHz
        */
      export namespace deteleLaborSupplyCertHz {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.HzSupplyCert>;
        export const request: (
          data?: defs.ncmp.HzSupplyCert,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.HzSupplyCert,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.HzSupplyCert>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.HzSupplyCert>;
      }

      /**
        * 获取电子签合同列表
查看电子合同接口，传empId
        * /wx-ncmp/elecsign/getEleContractList
        */
      export namespace getEleContractList {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
        export const request: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EleContract>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EleContract>;
      }

      /**
        * 获取查看电子签合同链接
获取查看电子签合同链接，传eleContractId、phoneNumber、idCardNum,empId
        * /wx-ncmp/elecsign/getEleContractUrl
        */
      export namespace getEleContractUrl {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
        export const request: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EleContract>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EleContract>;
      }

      /**
        * 获取华住补证件主记录
获取华住补证件主记录，传empId,如果返回对象为空说明没记录，菜单不显示，如果有值，把值作为参数传给展示接口
        * /wx-ncmp/elecsign/getHzSupplyCountByEmpId
        */
      export namespace getHzSupplyCountByEmpId {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.HzSupplyCert>;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 查看电子离职证明
查看电子离职证明，传empId
        * /wx-ncmp/elecsign/getQuitCert
        */
      export namespace getQuitCert {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EcQuit>;
        export const request: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EcQuit>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EcQuit>;
      }

      /**
        * 根据离职合同获取合同URL
根据离职合同获取合同URL，EcQuit
        * /wx-ncmp/elecsign/getQuitCertlUrl
        */
      export namespace getQuitCertlUrl {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EcQuit>;
        export const request: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EcQuit>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EcQuit>;
      }

      /**
        * 查看电子离职材料
查看电子离职材料，传empId
        * /wx-ncmp/elecsign/getQuitMaterial
        */
      export namespace getQuitMaterial {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EcQuit>;
        export const request: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EcQuit>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EcQuit>;
      }

      /**
        * 根据离职材料获取合同URL
查看电子离职材料，EcQuit
        * /wx-ncmp/elecsign/getQuitMaterialUrl
        */
      export namespace getQuitMaterialUrl {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EcQuit>;
        export const request: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EcQuit>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EcQuit>;
      }

      /**
        * 获取待操作的电子签合同
传eleContractStatus：2待签署，4待作废， 入职额外传cityCode
        * /wx-ncmp/elecsign/getSignEleContract
        */
      export namespace getSignEleContract {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
        export const request: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EleContract,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EleContract>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EleContract>;
      }

      /**
        * 获取需要上传的证件
获取需要上传的证件，传supplyId
        * /wx-ncmp/elecsign/queryHzSupplyCert
        */
      export namespace queryHzSupplyCert {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.HzSupplyCert>;
        export const request: (
          data?: defs.ncmp.HzSupplyCert,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.HzSupplyCert,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.HzSupplyCert>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.HzSupplyCert>;
      }

      /**
        * 签署电子离职材料
签署电子离职材料，传empId
        * /wx-ncmp/elecsign/signQuitMaterial
        */
      export namespace signQuitMaterial {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
        export const request: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.EcQuit,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.EcQuit>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.EcQuit>;
      }

      /**
        * 华住补证件提交
华住补证件提交，传supplyId
        * /wx-ncmp/elecsign/submitCert
        */
      export namespace submitCert {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.HzSupplyCert>;
        export const request: (
          data?: defs.ncmp.HzSupplyCert,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.HzSupplyCert,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.HzSupplyCert>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.HzSupplyCert>;
      }

      /**
       * 查看补证件上传附件
       * /wx-ncmp/elecsign/view
       */
      export namespace toViewMinio {
        export class Params {
          /** filePath */
          filePath: string;
        }

        export type Response = any;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * 考勤接口
     */
    export namespace eosAttendance {
      /**
        * 获取公司列表
获取公司列表
        * /wx-ncmp/eos/attendance/getCompany
        */
      export namespace getSignEleContract {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          Array<ObjectMap<string, ObjectMap>>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取登录的员工信息
获取登录的员工信息
        * /wx-ncmp/eos/attendance/getEmployee
        */
      export namespace getEmployee {
        export class Params {
          /** custId */
          custId: number;
        }

        export type Response = defs.ncmp.HttpResult<
          Array<ObjectMap<string, ObjectMap>>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 简单的调用示例，前端不要用这接口
简单的调用示例，前端不要用这接口
        * /wx-ncmp/eos/attendance/test
        */
      export namespace getTest {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * 考勤打卡接口
     */
    export namespace eosCheckIn {
      /**
        * 获取外勤打卡记录）
获取外勤打卡记录
        * /wx-ncmp/eos/attendance/checkin/getCheckFieldList
        */
      export namespace getCheckFieldList {
        export class Params {
          /** 公司id */
          custId: number;
          /** 日期 */
          date: string;
        }

        export type Response = defs.ncmp.HttpResult<
          Array<defs.ncmp.AcCheckLog>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取打卡记录
获取打卡记录
        * /wx-ncmp/eos/attendance/checkin/getCheckLogList
        */
      export namespace getCheckLogList {
        export class Params {
          /** 公司id */
          custId: number;
          /** 日期 */
          date: string;
        }

        export type Response = defs.ncmp.HttpResult<
          Array<defs.ncmp.AcCheckLog>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 判断是否拥有打卡地点和考勤规则
判断是否拥有打卡地点和考勤规则
        * /wx-ncmp/eos/attendance/checkin/getIsHasCheck
        */
      export namespace getIsHasCheck {
        export class Params {
          /** 公司id */
          custId: number;
        }

        export type Response = defs.ncmp.HttpResult<
          ObjectMap<string, ObjectMap>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 判断是否在打卡范围内
判断是否在打卡范围内
        * /wx-ncmp/eos/attendance/checkin/getIsInScope
        */
      export namespace getIsInScope {
        export class Params {
          /** 公司id */
          custId: number;
          /** 纬度 */
          latitude: string;
          /** 经度 */
          longitude: string;
        }

        export type Response = defs.ncmp.HttpResult<number>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取系统时间
获取系统时间
        * /wx-ncmp/eos/attendance/checkin/getSysTime
        */
      export namespace getSysTime {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 打卡
打卡
        * /wx-ncmp/eos/attendance/checkin/saveCheck
        */
      export namespace saveCheck {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<number>;
        export const request: (
          data?: defs.ncmp.AcCheckLog,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.AcCheckLog,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.AcCheckLog>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.AcCheckLog>;
      }

      /**
        * 外勤打卡）
打卡
        * /wx-ncmp/eos/attendance/checkin/saveCheckField
        */
      export namespace saveCheckField {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<number>;
        export const request: (
          data?: defs.ncmp.AcCheckLog,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.AcCheckLog,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.AcCheckLog>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.AcCheckLog>;
      }

      /**
        * 补签打卡）
补签打卡
        * /wx-ncmp/eos/attendance/checkin/saveCheckRepair
        */
      export namespace saveCheckRepair {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<number>;
        export const request: (
          data?: defs.ncmp.AcCheckLog,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.AcCheckLog,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.AcCheckLog>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.AcCheckLog>;
      }

      /**
        * form形式上传文件
form形式上传文件
        * /wx-ncmp/eos/attendance/checkin/uploadFile
        */
      export namespace uploadFile {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Hiresep Controller
     */
    export namespace hiresep {
      /**
        * 通过accountId查询最新入职信息
通过accountId查询最新入职信息
        * /wx-ncmp/hiresep/lastestHiresep
        */
      export namespace findLastestHiresep {
        export class Params {
          /** accountId */
          accountId: string;
        }

        export type Response = defs.ncmp.WechatHiresepDTO;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 查询当前登陆的员工的所有城市最新的入职表单
查询当前登陆的员工的所有城市最新的入职表单
        * /wx-ncmp/hiresep/listCityHireForm
        */
      export namespace listCityHireForm {
        export class Params {}

        export type Response = defs.ncmp.WechatHiresepDTO;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * rpa接口
     */
    export namespace hrs {
      /**
        * 在线问答校验token
在线问答校验token，传token
        * /wx-ncmp/rpa/hrs/checkToken
        */
      export namespace checkToken {
        export class Params {}

        export type Response = defs.ncmp.HttpRpaResult<defs.ncmp.TokenCheck>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取5年平均工资
根据年份获取最近5年平均工资，传城市名称或省份名称、查询年份
        * /wx-ncmp/rpa/hrs/getAverSalary
        */
      export namespace getAverSalary {
        export class Params {}

        export type Response = defs.ncmp.HttpRpaResult<
          Array<defs.ncmp.AveSalary>
        >;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取当年平均工资
根据年份获取当年平均工资，传城市名称或省份名称、查询年份
        * /wx-ncmp/rpa/hrs/getAverSalaryByYear
        */
      export namespace getAverSalaryByYear {
        export class Params {}

        export type Response = defs.ncmp.AveSalary;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取公积金基数
获取公积金基数，传人员分类id：personCategoryId，由获取人员分类接口返回
        * /wx-ncmp/rpa/hrs/getFundBase
        */
      export namespace getFundBase {
        export class Params {}

        export type Response = defs.ncmp.HttpRpaResult;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取指定月公积金
获取指定月公积金，同社保接口
        * /wx-ncmp/rpa/hrs/getFundByMonth
        */
      export namespace getFundByMonth {
        export class Params {}

        export type Response = defs.ncmp.RpaEmpFee;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取最近缴纳城市
获取最近缴纳城市，传token
        * /wx-ncmp/rpa/hrs/getLatestCity
        */
      export namespace getLatestCity {
        export class Params {}

        export type Response = defs.ncmp.EmpFeeCity;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取最近公积金
获取最近公积金，同社保接口
        * /wx-ncmp/rpa/hrs/getLatestFund
        */
      export namespace getLatestFund {
        export class Params {}

        export type Response = defs.ncmp.RpaEmpFee;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取最近公积金缴纳城市
获取最近公积金缴纳城市，传token
        * /wx-ncmp/rpa/hrs/getLatestFundCity
        */
      export namespace getLatestFundCity {
        export class Params {}

        export type Response = defs.ncmp.EmpFeeCity;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取最近工资
获取最近工资，传token
        * /wx-ncmp/rpa/hrs/getLatestSalary
        */
      export namespace getLatestSalary {
        export class Params {}

        export type Response = defs.ncmp.Salary;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取最近社保
获取最近社保，传token
        * /wx-ncmp/rpa/hrs/getLatestSs
        */
      export namespace getLatestSs {
        export class Params {}

        export type Response = defs.ncmp.RpaEmpFee;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取最近社保缴纳城市
获取最近社保缴纳城市，传token
        * /wx-ncmp/rpa/hrs/getLatestSsCity
        */
      export namespace getLatestSsCity {
        export class Params {}

        export type Response = defs.ncmp.EmpFeeCity;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取人员分类
获取人员分类，传城市名称cityName
        * /wx-ncmp/rpa/hrs/getPersonCategory
        */
      export namespace getPersonCategory {
        export class Params {}

        export type Response = defs.ncmp.HttpRpaResult<
          Array<defs.ncmp.PersonCategory>
        >;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取指定月工资
获取指定月工资，传token,如果查某月工资传sendMonth,格式限制为：2023年8月
        * /wx-ncmp/rpa/hrs/getSalaryByMonth
        */
      export namespace getSalaryByMonth {
        export class Params {}

        export type Response = defs.ncmp.Salary;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取社保基数
获取社保基数，传人员分类id：personCategoryId，由获取人员分类接口返回
        * /wx-ncmp/rpa/hrs/getSsBase
        */
      export namespace getSsBase {
        export class Params {}

        export type Response = defs.ncmp.HttpRpaResult;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取指定月社保
获取指定月社保，传token,如果查某月数据传serviceMonth,格式限制为：2023年8月
        * /wx-ncmp/rpa/hrs/getSsByMonth
        */
      export namespace getSsByMonth {
        export class Params {}

        export type Response = defs.ncmp.RpaEmpFee;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取社保实作进度
获取社保实作进度，传empId和groupId
        * /wx-ncmp/rpa/hrs/getSsProcessList
        */
      export namespace getSsProcessList {
        export class Params {}

        export type Response = defs.ncmp.HttpRpaResult<
          Array<defs.ncmp.SsProcess>
        >;
        export const request: (
          data?: defs.ncmp.SsProcessDto,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.SsProcessDto,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.SsProcessDto>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.SsProcessDto>;
      }

      /**
        * 获取客服信息
获取客服信息，传token
        * /wx-ncmp/rpa/hrs/getStewardInfo
        */
      export namespace getStewardInfo {
        export class Params {}

        export type Response = defs.ncmp.StewardInfo;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Policy Controller
     */
    export namespace policy {
      /**
        * 下载政策附件
下载政策附件,传文件路径和文件名称
        * /wx-ncmp/policy/downloadPolicyFile
        */
      export namespace downloadPolicyFile {
        export class Params {
          /** fileName */
          fileName: string;
          /** filePath */
          filePath: string;
        }

        export type Response = any;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取业务大类列表
获取业务大类列表，传cityId，所属类型categoryId，所属项目busnameClassId
        * /wx-ncmp/policy/getBusType
        */
      export namespace getBusType {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.BusinessType>;
        export const request: (
          data?: defs.ncmp.BusinessType,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.BusinessType,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.BusinessType>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.BusinessType>;
      }

      /**
        * 获取业务项目列表
获取业务项目列表
        * /wx-ncmp/policy/getBusnameClass
        */
      export namespace getBusnameClass {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.BusNameClass>;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取城市列表
获取城市列表，传empId就或获取订单城市，否则获取所有城市
        * /wx-ncmp/policy/getEmpCity
        */
      export namespace getEmpCity {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.City>;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
        * 获取材料
根据业务小类查询材料,传业务小类busSubtypeId
        * /wx-ncmp/policy/getMaterial
        */
      export namespace getMaterial {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.MaterialsPackage>;
        export const request: (
          data?: defs.ncmp.BusinessSubTypeMailQuery,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.BusinessSubTypeMailQuery,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.BusinessSubTypeMailQuery>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.ncmp.BusinessSubTypeMailQuery
        >;
      }

      /**
        * 获取政策
根据业务小类查询政策，传业务小类busnameSubtypeId，所属类型categoryId
        * /wx-ncmp/policy/getSingleFringeBenefits
        */
      export namespace getSingleFringeBenefits {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.FringeBenefitsVO>;
        export const request: (
          data?: defs.ncmp.FringeBenefitsQuery,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.FringeBenefitsQuery,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.FringeBenefitsQuery>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.ncmp.FringeBenefitsQuery
        >;
      }

      /**
        * 获取业务小类
获取业务小类列表，传empId、业务大类busTypeId、是否可预约isBooked
        * /wx-ncmp/policy/getSubType
        */
      export namespace getSubType {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.BusinessSubType>;
        export const request: (
          data?: defs.ncmp.BusinessSubType,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.BusinessSubType,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.BusinessSubType>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.ncmp.BusinessSubType
        >;
      }

      /**
        * 获取业务小类明细
获取业务小类明细，传业务小类id逗号分隔：busSubtypeIds
        * /wx-ncmp/policy/getSubTypeByIds
        */
      export namespace getSubTypeByIds {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.BusinessSubType>;
        export const request: (
          data?: defs.ncmp.BusinessSubTypeMailQuery,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.BusinessSubTypeMailQuery,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.BusinessSubTypeMailQuery>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.ncmp.BusinessSubTypeMailQuery
        >;
      }

      /**
        * 发材料模板邮件
根据业务小类发业务模板邮件,传业务小类busSubtypeId,openId,emailAddress,accountId
        * /wx-ncmp/policy/sendMaterialMail
        */
      export namespace sendMaterialMail {
        export class Params {}

        export type Response = defs.ncmp.HttpResult;
        export const request: (
          data?: defs.ncmp.BusinessSubTypeMailQuery,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.BusinessSubTypeMailQuery,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.BusinessSubTypeMailQuery>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.ncmp.BusinessSubTypeMailQuery
        >;
      }
    }

    /**
     * Policy Encyclopedia Controller
     */
    export namespace policyEncyclopedia {
      /**
        * 政策详情
政策详情
        * /wx-ncmp/policyEncyclopedia/detail
        */
      export namespace detail {
        export class Params {
          /** policyTemplateId */
          policyTemplateId: string;
        }

        export type Response = defs.ncmp.HttpResult<defs.ncmp.PolicyDetail>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 政策目录导航
政策目录导航
        * /wx-ncmp/policyEncyclopedia/getPolicyLevelTree
        */
      export namespace getPolicyLevelTree {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          Array<defs.ncmp.PolicyLevelTree>
        >;
        export const request: (
          data?: defs.ncmp.QueryPolicyEncyclopedia,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.QueryPolicyEncyclopedia,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.QueryPolicyEncyclopedia>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.ncmp.QueryPolicyEncyclopedia
        >;
      }

      /**
        * 省份
省份列表
        * /wx-ncmp/policyEncyclopedia/listProvince
        */
      export namespace listProvince {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          Array<ObjectMap<string, ObjectMap>>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 特区
特区列表
        * /wx-ncmp/policyEncyclopedia/listSpecialArea
        */
      export namespace listSpecialArea {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          Array<ObjectMap<string, ObjectMap>>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 政策大全列表
政策大全列表
        * /wx-ncmp/policyEncyclopedia/select
        */
      export namespace select {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<
          ObjectMap<string, ObjectMap>
        >;
        export const request: (
          data?: defs.ncmp.QueryPolicyEncyclopedia,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.QueryPolicyEncyclopedia,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.QueryPolicyEncyclopedia>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.ncmp.QueryPolicyEncyclopedia
        >;
      }
    }

    /**
     * Privacy Controller
     */
    export namespace privacy {
      /**
        * 通过openId查询是否同意了最新隐私协议
通过openId查询是否同意了最新隐私协议
        * /wx-ncmp/privacy/isRegisted
        */
      export namespace findLastestRegister {
        export class Params {
          /** openId */
          openId: string;
        }

        export type Response = defs.ncmp.PrivacyRegisterDTO;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 签署同意最新版本
签署同意最新版本
        * /wx-ncmp/privacy/register
        */
      export namespace postRegister {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<boolean>;
        export const request: (
          data?: defs.ncmp.PrivacyParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.PrivacyParam,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.PrivacyParam>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.PrivacyParam>;
      }
    }

    /**
     * Rpa Elec Sign Controller
     */
    export namespace rpaElecSign {
      /**
        * 获取待作废的电子签合同, 告警code: getLinkForAbort
只需传token
        * /wx-ncmp/rpa/elecsign/getLinkForAbort
        */
      export namespace getAbortEleContract {
        export class Params {}

        export type Response =
          defs.ncmp.HttpRpaResult<defs.ncmp.ElecSignResult>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取待作废的离职合同, 告警code: getLinkForAbortSep
只需传token
        * /wx-ncmp/rpa/elecsign/getLinkForAbortSep
        */
      export namespace getAbortEleContractSep {
        export class Params {}

        export type Response =
          defs.ncmp.HttpRpaResult<defs.ncmp.ElecSignResult>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取待签署的离职合同, 告警code: getLinkForSep
只需传token
        * /wx-ncmp/rpa/elecsign/getLinkForSep
        */
      export namespace getSignEleContractSep {
        export class Params {}

        export type Response =
          defs.ncmp.HttpRpaResult<defs.ncmp.ElecSignResult>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取待签署的电子签合同 , 告警code: getLinkForSign
只需传token
        * /wx-ncmp/rpa/elecsign/getLinkForSign
        */
      export namespace getSignEleContract {
        export class Params {}

        export type Response =
          defs.ncmp.HttpRpaResult<defs.ncmp.ElecSignResult>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Rpa Question Controller
     */
    export namespace rpaQuestion {
      /**
       * handleFAQ
       * /wx-ncmp/rpa/faq/test
       */
      export namespace handleFAQ {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<ObjectMap>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Ss Controller
     */
    export namespace ss {
      /**
        * 获取社保实作进度
获取社保实作进度，传empId和groupId
        * /wx-ncmp/ss/getSsProcessList
        */
      export namespace getSsProcessList {
        export class Params {}

        export type Response = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
        export const request: (
          data?: defs.ncmp.SsProcessDto,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.ncmp.SsProcessDto,
          options?: Taro.request.CommonUseRequestOption<defs.ncmp.SsProcessDto>,
        ) => Taro.request.CommonUseResultType<Response, defs.ncmp.SsProcessDto>;
      }
    }
  }
}
