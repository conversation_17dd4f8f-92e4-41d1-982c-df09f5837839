/**
 * 税前抵扣申请-赡养人信息
 */
import { useEffect } from 'react'
import { View } from '@tarojs/components'
import { BottomBtn, Form, FormItemProps, withPage, useForm } from '@components'
import { useRouter } from '@tarojs/taro'
import { navigateTo, getGlobalData } from '@utils'
import { getScrollStyle } from '@utils/transforms'
import { users } from '@apis/users'
import isEmpty from 'lodash/isEmpty'
import styles from './index.module.scss'

const cardType = [
  { key: '1', value: '居民身份证' },
  { key: '2', value: '中国护照' },
  { key: '3', value: '港澳居民来往内地通行证' },
  { key: '4', value: '港澳居民居住证' },
  { key: '5', value: '台湾居民来往大陆通行证' },
  { key: '6', value: '台湾居民居住证' },
  { key: '7', value: '外国护照' },
  { key: '8', value: '外国人永久居留身份证' },
  { key: '9', value: '外国人工作许可证（A类）' },
  { key: '10', value: '外国人工作许可证（B类）' },
  { key: '11', value: '外国人工作许可证（C类）' },
  { key: '12', value: '其他个人证件' }
]
const Index = () => {
  const { tSupportId = '' } = useRouter().params
  const employeeId = getGlobalData<'account'>('account').empId
  const { type = 'create' } = useRouter<{ type: 'create' | 'update' }>().params
  const form = useForm()
  const countryId = form.watch('countryId')
  const tSupportCardType = form.watch('tSupportCardType')
  const { result } = users.baseData.getBaseData.useRequest({ pageNum: '1', typeId: '911' })
  const openBankOptions = result?.data || ([] as defs.users.BaseData[])
  const onSubmit = (values: any) => {
    // console.log('values----', values)
    users.user.getSupportInfo
      .request({
        employeeId: employeeId
      })
      .then(res => {
        // console.log(res, 'res')
        // tSupportInfoList
        if (!isEmpty(res.data?.tSupportInfoList)) {
          let temp = res.data?.tSupportInfoList?.find(item => {
            return item.tSupportCardNum === values.tSupportCardNum
          })
          // console.log(temp, 'temp')
          if (temp) {
            // console.log('身份证重复')
            Taro.showToast({
              title: '身份证重复',
              icon: 'none',
              duration: 2000
            })
            return
          }
        }
        // console.log('jinlai')
        users.user.saveTSupportInfo.request(values).then(ress => {
          if (ress?.code == 0) {
            // console.log('保存成功')
            // Taro.navigateTo({
            //   url: '/pages/pre-tax/declare/submitSuccessfully/index'
            // })
            Taro.navigateTo({
              url: '/pages/pre-tax/declare/supportElder/index'
            })
          }
        })
      })
  }
  // useEffect(() => {
  //   const values = form.getValues()
  //   form.reset({ ...values, employeeId: employeeId,tSupportId: ''})
  // }, [employeeId])
  useEffect(() => {
    if (countryId) {
      form.setValue('tSupportCountryId', countryId)
      form.setValue('countryId', countryId)
      users.user.getCountryInfo
        .request({
          countryName: ''
        })
        .then(countryRes => {
          // console.log(countryRes, 'countryRes')
          !isEmpty(countryRes?.data) &&
            countryRes?.data.map((item, index) => {
              if (item.countryId == countryId) {
                form.setValue('countryName', item.countryName)
              }
            })
        })
    }
  }, [countryId])
  useEffect(() => {
    const values = form.getValues()
    if (tSupportId) {
      users.user.getTSupportInfo
        .request({
          tSupportId: tSupportId
        })
        .then(res => {
          // console.log(res)
          form.reset({ ...res.data, employeeId: employeeId, tSupportId: tSupportId })
          form.setValue('countryId', res.data?.tSupportCountryId)
        })
      // form.reset({ ...values, employeeId: employeeId,tSupportId: tSupportId})
    } else {
      form.reset({ employeeId: employeeId, tSupportId: '' })
    }
  }, [employeeId, tSupportId])
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const columns: FormItemProps[] = [
    {
      name: 'tSupportName',
      type: 'text',
      title: '姓名',
      rules: { required: true }
    },
    {
      name: 'countryId',
      type: 'page_choice',
      title: '国籍',
      rules: { required: true },
      pageOptions: {
        keys: ['countryId', 'countryName'],
        labelKey: 'countryName',
        url: '/country'
      }
    },
    {
      name: 'tSupportCardType',
      type: 'select',
      title: '身份证件类型',
      options: cardType,
      rules: { required: true }
    },
    {
      name: 'tSupportCardNum',
      type: 'id_card',
      title: '身份证件号码',
      rules: {
        required: true,
        pattern: tSupportCardType == 1 ? {
          value: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/,
          message: '请输入正确的身份证格式'
        }: {
          value: /([^\.\d]|^)(\d+)([^\.\d]|$)/,
          message: ''
        }
      }
    }
  ]
  return (
    <View className={styles.wrap}>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '保存',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
