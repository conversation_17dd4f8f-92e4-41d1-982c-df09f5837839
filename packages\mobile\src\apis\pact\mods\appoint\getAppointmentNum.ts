/**
 * @description 预约人数接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** bookingDate */
  bookingDate?: string;
  /** cityId */
  cityId?: string;
  /** dateType */
  dateType?: string;
  /** openId */
  openId?: string;
}

export type Result = defs.pact.AppointmentNumBean;
export const path = '/yc-wepact-mobile/appoint/getAppointmentNum';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
