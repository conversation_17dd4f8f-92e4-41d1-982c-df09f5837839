import { ScrollViewProps } from '@tarojs/components/types/ScrollView'
type RefreshStateType = 'Default' | 'Refresh' | 'LoadMore' | 'NoMoreData'
interface ListViewProps<T = any> extends ScrollViewProps {
  height?: number
  width?: number // 默认 100%
  itemSize: number
  // 数据源
  itemData: T[]
  renderItem: (item: T, index: number,id?:string) => any
  refreshState?: RefreshStateType
  onRefresh?: () => Promise<boolean>
  onLoadMore?: () => Promise<boolean> // boolean 是否可以下一页
  /**不下拉刷新 */
  noRefresh?: boolean;
  /**不上拉加载 */
  noPull?: boolean;
  refreshEventName?:string
  unlimitedSize?:boolean

}

export { RefreshStateType, ListViewProps }
