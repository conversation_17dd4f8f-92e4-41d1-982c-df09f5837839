import { withPage } from '@components'
import { View } from '@tarojs/components'
import { getGlobalData } from '@utils/global-data'
import { useEffect } from 'react'
import styles from './index.module.scss'

/** URL configuration type */
type EnvUrlConfig = {
  robotChat: string;
};

/** Environment configuration type */
type EnvUrls = {
  [key in 'prod' | 'test' | 'dev']: EnvUrlConfig;
};

const ENV_URLS: EnvUrls = {
  prod: {
    robotChat: 'https://ptah-next.kxjlcc.com:31443/college/#/index?shareCode=c4db6273-1839-4f55-9f2f-aa19d06377ee&channel=SHARE_LINK',
  },
  test: {
    robotChat: 'https://ptah-uat.kxjlcc.com:29999/college/#/index?shareCode=ca18165f-d393-4b33-a90d-7b5955608d1f&channel=SHARE_LINK',
  },
  dev: {
    robotChat: 'https://ptah-uat.kxjlcc.com:29999/college/#/index?shareCode=ca18165f-d393-4b33-a90d-7b5955608d1f&channel=SHARE_LINK',
  }
};


/** Get URL configuration for current environment */
const getEnvUrls = (): EnvUrlConfig => {
  const env = APP_ENV || 'dev';
  return ENV_URLS[env] || ENV_URLS.dev;
};

const Index = () => {
  useEffect(() => {
    const account: any = getGlobalData<'account'>('account') || {};
    let accountInfo: any = {};
    
    try {
      const platformInfo = sessionStorage.getItem('PlatformInfo');
      if (platformInfo) {
        accountInfo = JSON.parse(platformInfo);
      }
    } catch (error) {
      console.error('Failed to parse PlatformInfo:', error);
    }

    const envUrls = getEnvUrls();
    const latestToken = account.globalToken || accountInfo?.globalToken || '';
    
    const redirectUrl = `${envUrls.robotChat}&yc_token=${latestToken}`;

    requestAnimationFrame(() => {
      window.location.href = redirectUrl;
    });
  }, []);

  return (
    <View className={styles.container}>
      <View className={styles.loadingContainer}>
        <View className={styles.loadingSpinner} />
        <View className={styles.loadingText}>正在跳转...</View>
      </View>
    </View>
  );
};

export default withPage(Index)
