.container {
  width: 100%;
  height: calc(100vh - 320px);
  background-color: #fff;
  box-sizing: border-box;
  overflow: hidden;

  .wrapper {
    width: 100%;
    height: 60%;
  }

  .error_wrapper {
    height: 60%;
    display: flex;
    align-items: center;
  }

  .tip_wrap {
    padding: 20px;
  }

  .tip_wraperror {
    padding: 46px 0 263px 46px;
  }

  .tip {
    height: 40px;
    font: 28px/40px 'PingFangSC-Regular';
    font-weight: 400;
    color: rgba(153, 153, 153, 1);
  }

  .error_tip {
    display: flex;
    align-items: center;
    height: 48px;
    margin: 0 auto;
  }

  .error_tip .img {
    margin-right: 20px;
    width: 28px;
    height: 28px;
  }

  .error_tip .error_info {
    font: 28px/48px 'PingFangSC-Regular';
    font-weight: 400;
    height: 100%;
    color: #999999;
  }

  .content {
    width: calc(100% - 40px);
    height: 90%;
    margin: 0px 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    overflow-y: auto;
    .content_title {
      display: flex;
      padding: 12px;
      .dot {
        width: 20px;
        height: 20px;
        background: #6190E8;
        border-radius: 50%;
        margin-right: 10px;
        margin-top: 10px;
      }
      .item {
        width: 100%;
        margin-left: 12px;
        font: 28px 'PingFangSC-Regular';
        .address {
          color: rgba(153, 153, 153, 1);
        }
      }
    }
  }

  .bottom {
    display: flex;
    .clockIn_btn {
      position: relative;
      margin: 117px auto 115px;

      img {
        width: 228px;
        height: 228px;
      }

      .btn {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        color: #fff;
        font-weight: 500;

        .text {
          font: 44px/62px 'PingFangSC-Medium';
        }

        .time {
          font: 34px/48px 'arial';
        }
      }
    }
  }

  .athor_set {
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -75px;
    margin-top: -75px;

    width: 150px;
    height: 150px;
    line-height: 150px;
    border-radius: 50%;
    border: 0;
    text-align: center;
    vertical-align: middle;
    font-size: 28px;
    color: #fff;
    background: #666;
  }

  .bg {
    position: absolute;
    z-index: 1;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 194px;
  }
}
.uploader {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin: 10px 20px;
  .item {
    margin-top: 200px;
  }
}

.img_wrap {
  display: flex;
  position: relative;

  .img{
    width: 80px;
    height: 80px;
    margin: 0px 20px;
  }

  .del{
    position: absolute;
    top: -10px;
    right: 5px;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    .icon {
      width: 20px;
      height: 20px;
    }
  }
}