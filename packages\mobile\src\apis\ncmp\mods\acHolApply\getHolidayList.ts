/**
 * @description 获取假期列表
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** custId */
  custId: number;
}

export type Result = defs.ncmp.HttpResult<ObjectMap<string, ObjectMap>>;
export const path = '/wx-ncmp/eos/hol/apply/getHolidayList';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
