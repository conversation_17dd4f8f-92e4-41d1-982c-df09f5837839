import { FunctionComponent, useEffect, useState } from 'react'
import { View } from '@tarojs/components'
import Taro, { useDidShow } from '@tarojs/taro'
import { useWx } from '@utils/wx'
import { isEmpty } from 'lodash'
import { setGlobalData, getGlobalData, isWx, isComWx, isDingTalk } from '@utils'
import classnames from 'classnames'
import styles from './index.module.scss'
import H5CSSTranstionHoc from './H5CSSTranstionHoc'
import Error from '../error'

export const withPage = (
  Component: FunctionComponent,
  options?: {
    needLogin?: boolean
    showAfterLogin?: boolean
    needSignature?: boolean
    skeletonOptions?: { enable?: boolean }
  }
) => {
  const { needLogin = true, showAfterLogin = true, needSignature = false } = options || {}

  const Page: FunctionComponent<any> = () => {
    const PlatformInfo = sessionStorage.getItem('PlatformInfo')
    const accountInfo = PlatformInfo && JSON.parse(PlatformInfo)
    const account = getGlobalData<'account'>('account')
    const systemInfo = getGlobalData<'systemInfo'>('systemInfo')
    const [refresh, setRefresh] = useState(0)

    useWx()

    const handleSystemInfo = async () => {
      try {
        const res = await Taro.getSystemInfo()
        setGlobalData('systemInfo', res)
        return true
      } catch (error) {
        console.error('获取系统信息失败:', error)
        return false
      }
    }

    // 初始化系统信息
    useEffect(() => {
      if (isEmpty(systemInfo)) {
        handleSystemInfo()
      }
    }, [systemInfo])

    // 窗口大小变化处理
    useEffect(() => {
      const handleResize = async () => {
        const success = await handleSystemInfo()
        if (success) {
          setRefresh(prev => prev + 1)
        }
      }

      Taro.onWindowResize(handleResize)
      return () => Taro.offWindowResize(handleResize)
    }, [])

    // 签名处理
    useEffect(() => {
      if (needSignature) {
        if (isWx()) {
          // TODO: 实现微信签名逻辑
        } else if (isComWx()) {
          // TODO: 实现企业微信签名逻辑
        }
      }
    }, [])

    // 钉钉标题设置
    useDidShow(() => {
      if (isDingTalk()) {
        const setDingTalkTitle = () => {
          try {
            const currentPage = Taro.getCurrentPages()[Taro.getCurrentPages().length - 1]
            if (currentPage) {
              const { navigationBarTitleText } = currentPage.config
              const { eleBusinessStatus } = currentPage.$taroParams
              const title = navigationBarTitleText || document.title

              if (window.location.hash.includes('home')) {
                dd.biz.navigation.setTitle({ title: '首页' })
              } else if (title) {
                dd.biz.navigation.setTitle({
                  title: eleBusinessStatus === '3' ? '签署电子业务' : eleBusinessStatus === '4' ? '作废电子业务' : title
                })
              }
            } else {
              dd.biz.navigation.setTitle({ title: '首页' })
            }
          } catch (error) {
            console.error('设置钉钉标题失败:', error)
          }
        }

        setTimeout(setDingTalkTitle, 600)
      }
    })
    // 登录状态检查
    if (needLogin && showAfterLogin && !(account?.openId || accountInfo?.openId)) {
      const err = sessionStorage.getItem('AccountErrorInfo')
      if (!isEmpty(err)) {
        return <Error errorInfo={err} />
      }
      return (
        <View className={classnames(styles.wrap, styles.center)}>
          <View className={styles.loadingContainer}>
            <View className={styles.loadingSpinner} />
            <View className={styles.loadingText}>加载中...</View>
          </View>
        </View>
      )
    }

    if (!systemInfo) {
      return <View className={styles.emptyState}>系统初始化中...</View>
    }

    // 渲染主要内容
    const renderContent = () => (
      <View className={styles.wrap}>
        <Component />
      </View>
    )

    return IS_H5 ? <H5CSSTranstionHoc>{renderContent()}</H5CSSTranstionHoc> : renderContent()
  }

  return Page
}
