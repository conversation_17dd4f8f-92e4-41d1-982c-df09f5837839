/**
 * @description 获取城市的动态表单项数据
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = object;
export const path = '/yc-wepact-mobile/dynamics-form/appformItem';
export const method = 'POST';
export const request = (
  data: defs.pact.DynamicFormReqBean,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.DynamicFormReqBean,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
