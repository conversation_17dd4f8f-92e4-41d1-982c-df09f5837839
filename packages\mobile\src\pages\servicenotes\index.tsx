import { View, Text } from '@tarojs/components'
import { withPage } from '@components';
import styles from './index.module.scss'

const list = [
  {
    title: '一、引导语',
    content: [
      {
        content:
          '自2019年1月起，上海启动新版社保卡集中换发工作。易才为服务企业的用户提供工商银行、招商银行（排名不分先后）社保卡申请便捷专属通道。企业用户可在2家银行中挑选一家进行申请。'
      }
    ]
  },
  {
    title: '二、新版社保卡介绍',
    content: [
      {
        content:
          '1.新版社保卡具有信息记录、自助查询、就医结算、缴费和待遇领取等基本功能，可以用于办理养老保险、医疗保险、工伤保险、失业保险、生育保险、就业服务、劳动关系、人事人才、居民健康管理等事务。'
      },
      {
        content:
          '2.新版社保卡兼具金融服务功能，可实现现金存取、转账、消费等功能。申请人可以自愿选择相关服务银行，开通新版社保卡的金融服务功能。'
      }
    ]
  },
  {
    title: '三、服务对象',
    content: [
      {
        content: '据现行政策规定，仅供本市户籍以及境内参加本市社会保险的易才服务的企业雇员（不包括外籍、港澳台）使用。'
      }
    ]
  },
  {
    title: '四、用户须知',
    content: [{ content: '根据市信息服务中心规定，用户仅挑选一家银行申请，如选择多家银行，则可能会导致申请不予通过。' }]
  },
  {
    title: '五、使用方式',
    content: [
      { content: ' 1）选择银行 专属社保卡信息采集页面' },
      { content: ' 2）在线填写 个人信息，完成信息上传' },
      { content: ' 3）进度查询 工商银行可至信息采集页面查询；招商银行请下载并安装银行APP查询' },
      {
        content:
          ' 4）投递签收 待市信息服务中心制卡完成后，委托指定邮政投递至申领人指定投递地址，申领人出示本人有效居民身份证，邮政投递人员审核，领卡人签收'
      },
      { content: ' 5）开通激活 用户收到新版社保卡后，请本人携身份证及社保卡至对应银行网点柜台开通激活' }
    ]
  },
  {
    title: '六、服务费用',
    content: [
      {
        content:
          '新版社保卡免费申请，同时免收银行管理费、年费及开卡费等。如有其他金融功能相关收费，服务银行应按国家有关规定标准实施。'
      }
    ]
  },
  {
    title: '七、过渡衔接',
    content: [
      {
        content:
          '新旧社保卡换领过渡期间，实行新旧卡并行使用。待申请人领取新版社保卡并到银行柜面办理开通激活手续后，旧社保卡则自动废止。尚未换发新版社保卡或未办理开通激活手续的，旧社保卡仍可继续正常使用。'
      }
    ]
  },
  {
    title: '八、免责条款',
    content: [
      {
        content:
          '易才仅提供银行社保卡申请便捷通道，具体换领事宜及发放时间由市信息服务中心受理，易才不承诺用户社保卡申请一定成功。'
      }
    ]
  }
]
const Index = () => {
  return (
    <View className={styles.wrap}>
      <Text>
        {list?.map((item,index) => {
          return (
            <View className={styles.wrap} key={index}>
              <View>{item.title}</View>
              <View className={styles.text_indent}>
                {item.content?.map((item1,index1) => {
                  return <View key={index1}>{item1.content}</View>
                })}
              </View>
            </View>
          )
        })}
      </Text>
    </View>
  )
}
export default withPage(Index)
