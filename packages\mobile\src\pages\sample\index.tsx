import { Fragment } from 'react'
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { BottomBtn, withPage } from '@components'
import Taro, { useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils/transforms'
import idcard1 from '@assets/sample/idcard2.png'
import idcard2 from '@assets/sample/idcard1.png'
import wrong1 from '@assets/sample/wrong1.png'
import wrong2 from '@assets/sample/wrong2.png'
import wrong3 from '@assets/sample/wrong3.png'
import wrong4 from '@assets/sample/wrong4.png'
import styles from './index.module.scss'

const Index = () => {
  const { cityCode, uuid, type, key, flag } = useRouter().params
  const isDrawer = type === '1'
  const samples = [
    {
      title: '查看正确样例',
      items: [
        {
          title: '身份证头像面',
          img: idcard1,
          message: ''
        },
        {
          title: '身份证国徽面',
          img: idcard2,
          message:
            '(1)身份证正反面的朝向为横向，图像端正，不歪斜，不竖立；身份证正反面要纵向排列，（正面上，反面下），不允许横向排列；图片内容清晰，在适当放大后仍能清晰辨识。\n(2)身份证有效区域在整个图片中占比要合适，不允许出现无效部分占比过大的情况。'
        }
      ]
    },
    {
      title: '查看错误样例1',
      items: [
        {
          title: '身份证头像面',
          img: wrong1,
          message: ''
        },
        {
          title: '身份证国徽面',
          img: wrong2,
          message: '⑴身份证正反面倾斜较大、图片变形、内容模糊。\n⑵边界不清晰，底色灰暗。'
        }
      ]
    },
    {
      title: '查看错误样例2',
      items: [
        {
          title: '身份证头像面',
          img: wrong3,
          message: ''
        },
        {
          title: '身份证国徽面',
          img: wrong4,
          message: '原因：身份证内容模糊不清、难以辩证'
        }
      ]
    }
  ]
  const scrollStyle = getScrollStyle({ bottom: 120 })
  return (
    <Fragment>
      <ScrollView style={scrollStyle} scrollY>
        {samples.map(sample => (
          <View key={sample.title} className={styles.sample}>
            <View className={styles.title_wrap}>
              <Text className={styles.title}>{sample.title}</Text>
            </View>
            <View>
              {sample.items.map(item => (
                <View key={item.title} className={styles.item}>
                  <Image className={styles.item_img} src={item.img} />
                  <Text className={styles.item_title}>{item.title}</Text>
                  <Text className={styles.item_message}>{item.message}</Text>
                </View>
              ))}
            </View>
          </View>
        ))}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: isDrawer ? '返回' : '确定',
            onClick: () => {
              if (isDrawer) {
                Taro.navigateBack()
              } else {
                if (key === '1') {
                  Taro.navigateTo({
                    url: `/pages/materialdata/index?cityCode=${cityCode}&uuid=${uuid}&flag=${flag}`
                  })
                } else {
                  Taro.navigateTo({
                    url: `/pages/induction/handle/lowcode-form/index?cityCode=${cityCode}&uuid=${uuid}`
                  })
                }
              }
            }
          }
        ]}
      />
    </Fragment>
  )
}

export default withPage(Index)
