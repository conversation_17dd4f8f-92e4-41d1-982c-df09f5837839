import { FunctionComponent } from 'react'
import KeyboardAwareScrollView from '@components/keyboard-aware-scroll-view'
import { FormItem } from './form-item'
import { FormProps } from './type'

const Form: FunctionComponent<FormProps & { disabled?: boolean }> = props => {
  const { form, columns, disabled, ...rest } = props
  return (
    <KeyboardAwareScrollView {...rest} scrollY>
      {columns?.map((item, index) => {
        return <FormItem form={form} {...item} key={`${item.name}${index}`} disabled={disabled || item.disabled} />
      })}
    </KeyboardAwareScrollView>
  )
}

export default Form
