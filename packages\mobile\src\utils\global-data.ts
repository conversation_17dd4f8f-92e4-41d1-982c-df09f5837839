const globalData = {}

if (IS_RN) {
  global.globalData = {}
}

export interface Account {
  id: number;
  mobile: string;
  idCard: string;
  openId: string;
  empId: string;
  bindStatus: boolean;
  bindTime: string;
  updateTime: string;
  accountId: string;
  token: string;
  latestToken: string;
  credentialsType: string;
  globalToken: string;
  isOuterEmp: string;
}

interface GlobalData {
  // 账户信息
  account: Account
  // 系统信息
  systemInfo: Taro.getSystemInfo.Result;
}

const setGlobalData = (key: keyof GlobalData, val: any) => {
  (IS_RN ? global.globalData : globalData)[key] = val
}

const getGlobalData = <T extends keyof GlobalData>(key: keyof GlobalData) => {
  return ((IS_RN ? global.globalData : globalData) as GlobalData)[key as T] || {}
}

export { setGlobalData, getGlobalData }
