/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-15 14:11:45
 * @LastAuthor: 王正荣
 * @LastTime: 2021-09-18 09:59:15
 * @message: 
 */
import { FunctionComponent, useMemo } from 'react'
import { View, Text, Button, Image } from '@tarojs/components'
import Detail from '@assets/icon/icon-detail.png'
import Cancel from '@assets/icon/icon-cancel.png'
import Order from '@assets/icon/order.png'
import styles from './index.module.scss'
import {
  ButtonIconType,
  ButtonSize,
  ButtonType
} from './types'
import type { ButtonsProps } from './types'

// 重新导出类型，方便外部使用
export { ButtonIconType, ButtonSize, ButtonType } from './types'
export type { ButtonsProps } from './types'

// 图标配置映射
const ICON_CONFIG: Record<ButtonIconType, { src: string; className: string }> = {
  [ButtonIconType.DETAIL]: {
    src: Detail,
    className: styles.icon
  },
  [ButtonIconType.CANCEL]: {
    src: Cancel,
    className: styles.icon_wrap
  },
  [ButtonIconType.ORDER]: {
    src: Order,
    className: styles.order_icon
  }
} as const

// 按钮样式映射
const BUTTON_STYLE_MAP = {
  [ButtonIconType.CANCEL]: styles.cancel_wrap,
  [ButtonIconType.ORDER]: styles.wrap,
  [ButtonIconType.DETAIL]: styles.wrap
} as const

const Buttons: FunctionComponent<ButtonsProps> = ({
  title = '',
  icon = ButtonIconType.DETAIL,
  type,
  size,
  disabled = false,
  loading = false,
  onClick,
  className = '',
  testId,
  ...restProps
}) => {
  // 获取图标配置
  const iconConfig = useMemo(() => {
    const iconKey = icon as ButtonIconType
    return ICON_CONFIG[iconKey] || ICON_CONFIG[ButtonIconType.DETAIL]
  }, [icon])

  // 获取按钮样式
  const buttonClassName = useMemo(() => {
    const iconKey = icon as ButtonIconType
    const baseStyle = BUTTON_STYLE_MAP[iconKey] || styles.wrap

    // 添加尺寸样式
    const sizeStyle = size === ButtonSize.SMALL ? styles.small :
                     size === ButtonSize.LARGE ? styles.large : ''

    // 添加加载状态样式
    const loadingStyle = loading ? styles.loading : ''

    // 组合所有样式
    const allStyles = [baseStyle, sizeStyle, loadingStyle, className]
      .filter(Boolean)
      .join(' ')

    return allStyles
  }, [icon, size, loading, className])

  // 处理点击事件
  const handleClick = () => {
    if (disabled || loading) return
    onClick?.()
  }

  return (
    <Button
      className={buttonClassName}
      onClick={handleClick}
      disabled={disabled || loading}
      data-testid={testId}
      {...restProps}
    >
      {iconConfig && (
        <Image
          className={iconConfig.className}
          src={iconConfig.src}
        />
      )}
      <Text className={styles.text}>
        {loading ? '加载中...' : title}
      </Text>
    </Button>
  )
}

export default Buttons
