/**
 * @description 入职办理-身份证识别接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.OcrResponseBean;
export const path = '/yc-wepact-mobile/entry/getOCRImageInfo';
export const method = 'POST';
export const request = (
  data: defs.pact.OcrParamBean,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.OcrParamBean,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
