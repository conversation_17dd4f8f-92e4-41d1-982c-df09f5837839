.wrap {
  width: 750px;
  flex: 1;
  /*  #ifndef rn */
  height: 100vh;
  /*  #endif  */
}

.buttonWrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  height: 60px;
}

.button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background: #1890ff;
  width: 150px;
  height: 55px;
  font-size: 28px;
  margin: 0 20px 0 0;
}

.button_disabled {
  @extend .button;
  background: #d4d9e1;
}

.input {
  width: 55px;
  height: 50px;
  border: 2px #000;
  border-radius: 4px;
}

.inputWrap {
  border: 2px black;
}

.text {
  margin: 0 30px 0 0;
}
