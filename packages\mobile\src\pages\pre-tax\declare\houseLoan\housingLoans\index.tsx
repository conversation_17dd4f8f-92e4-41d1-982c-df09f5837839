/*
 * @Author: your name
 * @Date: 2021-09-06 09:22:25
 * @LastEditTime: 2021-11-24 17:33:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\declare\houseLoan\housingLoans\index.tsx
 */
/**
 * 税前抵扣申请-住房贷款利息支出
 */
import { useEffect, useState } from 'react'
import { getGlobalData } from '@utils'
import { View } from '@tarojs/components'
import { users } from '@apis/users'
import Taro, { useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils/transforms'
import { BottomBtn, withPage, useForm, Form, FormItemProps } from '@components'
import isEmpty from 'lodash/isEmpty'
import styles from './index.module.scss'

const isloan = [
  { key: 0, value: '否' },
  { key: 1, value: '是' }
]
const houseFirstLoan = [
  { key: 0, value: '否' },
  { key: 1, value: '是' }
]
const houseType = [
  { key: 0, value: '房屋所有权证' },
  { key: 1, value: '不动产权证' },
  { key: 2, value: '房屋买卖合同' },
  { key: 3, value: '房屋预售合同' }
]
const provideType = [
  { key: 1, value: '公积金贷款' },
  { key: 0, value: '商业贷款' }
]

const Index = () => {
  const { id = '' } = useRouter().params
  const employeeId = getGlobalData<'account'>('account').empId
  const { type = 'create' } = useRouter<{ type: 'create' | 'update' }>().params
  const [require, setRequire] = useState<boolean>(false)
  const [disHousNum, setdisHousNum] = useState<boolean>(false)
  const form = useForm()
  const { result } = users.baseData.getBaseData.useRequest({ pageNum: '1', typeId: '911' })
  const openBankOptions = result?.data || ([] as defs.users.BaseData[])
  const onSubmit = (values: any) => {
    // console.log('values----', values)
    values.employeeId = employeeId
    users.user.saveHouseLoanInfo.request(values).then(res => {
      // console.log(res)
      if (res?.code == 0) {
        // console.log('保存成功')
        Taro.navigateTo({
          url: '/pages/pre-tax/declare/submitSuccessfully/index'
        })
      }
    })
  }

  const deductionAmount = form.watch('deductionAmount')
  const houseLoanType = form.watch('houseLoanType')

  useEffect(() => {
    if (!deductionAmount) {
      form.setValue('deductionAmount', 1000)
    }
  }, [deductionAmount])

  useEffect(() => {
    users.user.getHouseLoanInfo
      .request({
        employeeId: employeeId
      })
      .then(res => {
        // console.log(res)
        !isEmpty(res?.data[0]) && form.reset({ ...res?.data[0], employeeId: employeeId, houseId: '' })
        // !isEmpty(res?.data[0]) && res?.data[0].isOneself == false ? form.setValue('isOneself', 0) : form.setValue('isOneself', 1)
        if (!isEmpty(res?.data[0]) && res?.data[0].isOneself == false) {
          form.setValue('isOneself', 0)
        } else if (!isEmpty(res?.data[0]) && res?.data[0].isOneself == true) {
          form.setValue('isOneself', 1)
        } else {
        }
        !isEmpty(res?.data) && setdisHousNum(true)
      })
  }, [employeeId])

  useEffect(() => {
    form.setValue('deductionMonth', '')
  }, [])

  useEffect(() => {
    if (id !== '') {
      users.user.getLoanInfo
        .request({
          houseId: id
        })
        .then(res => {
          // console.log(res)
          !isEmpty(res?.data) && form.reset({ ...res?.data, employeeId: employeeId, houseId: res.data?.houseId, deductionMonth: '', deductionType: 1, endRepaymentDate: '2050-12-12'})
          res.data?.isOneself == false ? form.setValue('isOneself', 0) : form.setValue('isOneself', 1)
        })
    }else {
      form.setValue('houseId', '')
      form.setValue('deductionMonth', '')
      form.setValue('deductionType', 1)
      form.setValue('endRepaymentDate', '2050-12-12')
    }
  }, [id])

  useEffect(() => {
    const values = form.getValues()
    form.reset({ ...values, employeeId: employeeId, houseId: '' })
  }, [employeeId])

  useEffect(() => {
    if (houseLoanType == 0) {
      setRequire(true)
    } else {
      setRequire(false)
    }
  }, [houseLoanType])

  const scrollStyle = getScrollStyle({ bottom: 120 })
  const columns: FormItemProps[] = [
    {
      title: '抵扣额度（元）',
      name: 'deductionAmount',
      type: 'text',
      disabled: true
      // rules: { required: true, maxLength: 25 }
    },
    {
      name: 'houseAddress',
      type: 'text',
      title: '房屋坐落地址',
      rules: { required: true, maxLength: 25 }
    },
    {
      name: 'isOneself',
      type: 'single',
      title: '本人是否借款',
      options: isloan,
      rules: { required: true }
    },
    {
      name: 'houseCredentialsType',
      type: 'single',
      title: '房屋证书类型',
      options: houseType,
      rules: { required: true }
    },
    {
      name: 'houseCredentialsNum',
      type: 'text',
      title: '房屋证书号码',
      disabled: disHousNum,
      rules: { required: true }
    },
    {
      name: 'houseFirstLoan',
      type: 'single',
      title: '是否婚前各自首套贷款，且婚后分别扣除50%',
      options: houseFirstLoan,
      rules: { required: true }
    },
    {
      name: 'houseLoanType',
      type: 'single',
      title: '贷款类型',
      options: provideType,
      rules: { required: true }
    },
    {
      name: 'houseLoanBank',
      type: 'text',
      title: '贷款银行',
      rules: { required: require }
    },
    {
      name: 'houseLoanNum',
      type: 'text',
      title: '贷款合同编号',
      rules: { required: true, maxLength: 25 }
    },
    {
      name: 'startRepaymentDate',
      type: 'date',
      title: '首次还款日期',
      rules: { required: false }
    },
    {
      name: 'loanPeriod',
      type: 'text',
      title: '贷款期限（月数）',
      rules: { required: true, maxLength: 25 }
    }
  ]
  return (
    <View className={styles.wrap}>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '保存',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
