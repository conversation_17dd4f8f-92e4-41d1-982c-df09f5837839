class AcCheckLog {
  /** 外勤签到地址（实际打卡） */
  checkAdd = '';

  /** 打卡记录id */
  checkLogId = undefined;

  /** 外勤签到备注,补签备注 */
  checkRemark = '';

  /** 打卡时间 */
  checkTime = '';

  /** 打卡类型（1上班 2下班 3外勤） */
  checkType = undefined;

  /** 创建者 */
  createBy = undefined;

  /** 创建时间 */
  createDt = '';

  /** 客户id */
  custId = undefined;

  /** 数据来源(1正常打卡2补签3导入) */
  dataSource = undefined;

  /** 部门id */
  deptId = undefined;

  /** 设备型号 */
  deviceDes = '';

  /** 设备id字符 */
  deviceId = '';

  /** 员工id */
  eosEmpId = undefined;

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = undefined;

  /** 删除标志（0代表存在 2代表删除） */
  isDeleted = undefined;

  /** 纬（实际打卡） */
  latitude = '';

  /** 办公地址id */
  locSetId = undefined;

  /** 打卡地点描述 */
  locSetName = '';

  /** 经度（实际打卡） */
  longitude = '';

  /** 外勤签到图片url */
  picUrl = '';

  /** 备注 */
  remark = '';

  /** 更新者 */
  updateBy = undefined;

  /** 更新时间 */
  updateDt = '';
}

class AcHolApply {
  /** 申请id */
  applyId = undefined;

  /** 申请时间 */
  applyTime = '';

  /** 孩子出生日期 */
  childBirthday = '';

  /** 婴儿数量 */
  childNumber = undefined;

  /** 孩子预产期 */
  childPreborn = '';

  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 客户id */
  custId = undefined;

  /** 部门id */
  deptId = undefined;

  /** 结束日期 */
  endTime = '';

  /** 员工id */
  eosEmpId = undefined;

  /** 审批结束时间 */
  examOverTime = '';

  /** 审批是否结束（0未结束/1已结束） */
  examStepIsOver = undefined;

  /** 请假折合的天数 */
  holDays = undefined;

  /** 请假时数 */
  holHours = undefined;

  /** 请假类型id */
  holId = undefined;

  /** 请假名称 */
  holName = '';

  /** 是否删除（0否 1是） */
  isDeleted = undefined;

  /** length */
  length = undefined;

  /** marriageRegistrationDay */
  marriageRegistrationDay = '';

  /** 备注 */
  remark = '';

  /** 流水号 */
  serialNum = '';

  /** 副本版本号 */
  sonVer = undefined;

  /** start */
  start = undefined;

  /** 开始日期 */
  startTime = '';

  /** 审批状态（1审批通过 2待审批 3驳回 4撤销） */
  state = undefined;

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';

  /** 显示时长 */
  viewHours = '';
}

class AveSalary {
  /** 月度平均工资 */
  avgPayMon = '';

  /** 年度平均工资 */
  avgPayYear = '';

  /** 执行时间 */
  execStartTime = '';

  /** 最低工资（小时）分6档填入 1档 */
  minHour1 = '';

  /** minHour2 */
  minHour2 = '';

  /** minHour3 */
  minHour3 = '';

  /** minHour4 */
  minHour4 = '';

  /** minHour5 */
  minHour5 = '';

  /** minHour6 */
  minHour6 = '';

  /** 最低工资（小时）分6档填入 1档适用区县 */
  minHourCounty1 = '';

  /** minHourCounty2 */
  minHourCounty2 = '';

  /** minHourCounty3 */
  minHourCounty3 = '';

  /** minHourCounty4 */
  minHourCounty4 = '';

  /** minHourCounty5 */
  minHourCounty5 = '';

  /** minHourCounty6 */
  minHourCounty6 = '';

  /** 最低工资（月度）分6档填入，1档 */
  minMon1 = '';

  /** minMon2 */
  minMon2 = '';

  /** minMon3 */
  minMon3 = '';

  /** minMon4 */
  minMon4 = '';

  /** minMon5 */
  minMon5 = '';

  /** minMon6 */
  minMon6 = '';

  /** 最低工资（月度）分6档填入，1档 适用区县 */
  minMonCounty1 = '';

  /** minMonCounty2 */
  minMonCounty2 = '';

  /** minMonCounty3 */
  minMonCounty3 = '';

  /** minMonCounty4 */
  minMonCounty4 = '';

  /** minMonCounty5 */
  minMonCounty5 = '';

  /** minMonCounty6 */
  minMonCounty6 = '';

  /** 省份/城市 */
  pcName = '';

  /** 所属年份 */
  yearDate = '';
}

class AveSalaryStr {
  /** str1 */
  str1 = '';

  /** str2 */
  str2 = '';

  /** str3 */
  str3 = '';
}

class BusNameClass {
  /** 业务项目id */
  bussNameClassId = '';

  /** 业务项目名称 */
  bussNameClassName = '';
}

class BusinessSubType {
  /** 小类id */
  busSubtypeId = '';

  /** 小类名称 */
  busSubtypeName = '';

  /** 大类id */
  busTypeId = '';

  /** 大类名称 */
  busTypeName = '';

  /** 业务项目id */
  bussNameClassId = '';

  /** 业务项目名称 */
  bussNameClassName = '';

  /** 所属类型 */
  categoryId = '';

  /** 雇员id */
  empId = '';

  /** 是否可预约 */
  isBooked = '';

  /** 个人空表模板 */
  pBlankTemplatePath = '';

  /** 个人空表模板名 */
  pBlankTemplatePathName = '';

  /** 个人样本模板 */
  pSampleTemplatePath = '';

  /** 个人样本模板名 */
  pSampleTemplatePathName = '';

  /** 业务说明 */
  remark = '';
}

class BusinessSubTypeMailQuery {
  /** accountId */
  accountId = '';

  /** busSubtypeIds */
  busSubtypeIds = '';

  /** emailAddress */
  emailAddress = '';

  /** openId */
  openId = '';
}

class BusinessType {
  /** 大类id */
  busTypeId = '';

  /** 大类名称 */
  busTypeName = '';

  /** 业务项目名称id */
  busnameClassId = '';

  /** 所属类型 */
  categoryId = '';

  /** 城市id */
  cityId = '';

  /** 城市名称 */
  cityName = '';
}

class City {
  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';
}

class EbmApplicationNode {
  /** 实际办理天数 */
  actualProcessDays = undefined;

  /** 业务办理申请ID */
  applicationId = undefined;

  /** 主键 */
  applicationNodeId = undefined;

  /** 业务办理内容ID */
  busCityConfigId = undefined;

  /** 节点ID */
  busCityNodeConfigId = undefined;

  /** 节点名称 */
  busCityNodeConfigName = '';

  /** 业务节点顺序 */
  busCityNodeNum = undefined;

  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** 失败原因 */
  failureReason = '';

  /** 政府性收费金额 */
  governFee = '';

  /** 是否支付津贴待遇(1是、0否) */
  isAllowance = '';

  /** 是否支付津贴待遇(1是、0否) */
  isAllowanceName = '';

  /** 是否收取材料(1是、0否) */
  isCollectMaterial = '';

  /** 是否收取材料(1是、0否) */
  isCollectMaterialName = '';

  /** isDeleted */
  isDeleted = '';

  /** 是否有政府性收费（1是、0否） */
  isGovernFee = '';

  /** 是否有政府性收费（1是、0否） */
  isGovernFeeName = '';

  /** 是否已发送提醒 0-未提醒 1-已提醒 */
  isReminded = undefined;

  /** mimicBy */
  mimicBy = '';

  /** 办理结果 1 成功 2失败 */
  nodeResult = undefined;

  /** 节点业务进度SELECT * FROM BD_BASE_DATA WHERE TYPE = 12336 */
  nodeStatus = undefined;

  /** 办理过程 */
  processDescription = '';

  /** proxyBy */
  proxyBy = '';

  /** 提交后道办理时间 */
  submitToBackendTime = '';

  /** 办理周期(正整数 天) */
  transactPeriod = '';

  /** updateBy */
  updateBy = '';

  /** updateDt */
  updateDt = '';
}

class EbmApplicationNodeMaterial {
  /** 主键 */
  applicationMaterialId = undefined;

  /** 员工业务办理节点ID */
  applicationNodeId = undefined;

  /** 材料收集进度 */
  collectionProgress = '';

  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** isDeleted */
  isDeleted = '';

  /** 是否通知上传 0 否 1 是 */
  isNotifyUpload = undefined;

  /** 是否原件(1是、0否) */
  isOriginal = undefined;

  /** 原件是否已寄出 0 否 1 是 */
  isOriginalSent = undefined;

  /** 是否返还材料(1是、0否) */
  isReturnMaterial = '';

  /** 材料确认情况 1 已确认、2 材料修改、3补充材料 */
  materialConfirmStatus = undefined;

  /** 材料份数 */
  materialCount = undefined;

  /** 材料模版存文件id */
  materialFileId = '';

  /** 材料名称 */
  materialName = '';

  /** 材料上传ID */
  materialUploadId = undefined;

  /** 材料ID */
  materialsId = undefined;

  /** mimicBy */
  mimicBy = '';

  /** proxyBy */
  proxyBy = '';

  /** 用印签字方(1易才章、2客户章、3人员签名 */
  signatoryParty = '';

  /** 模版名称 */
  templateName = '';

  /** updateBy */
  updateBy = '';

  /** updateDt */
  updateDt = '';
}

class EbmApplicationNodeShipment {
  /** 员工业务办理节点材料上传ID逗号分隔多个 */
  applicationMaterialIds = '';

  /** 员工业务办理节点ID */
  applicationNodeId = undefined;

  /** 快递公司 select * from BD_BASE_DATA where type =200 */
  courierCompany = undefined;

  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** isDeleted */
  isDeleted = '';

  /** mimicBy */
  mimicBy = '';

  /** proxyBy */
  proxyBy = '';

  /** 收件人 */
  recipient = '';

  /** 收件人电话 */
  recipientPhone = '';

  /** 主键 */
  shipmentId = undefined;

  /** 寄件时间 */
  shipmentTime = '';

  /** 寄件类型 1 员工来件 ， 2客户HR来件，3客服至客服件，4客服退回员工 */
  shipmentType = undefined;

  /** 本次寄件勾选的材料id */
  tickMaterialsId = '';

  /** 快递单号 */
  trackingNumber = '';

  /** updateBy */
  updateBy = '';

  /** updateDt */
  updateDt = '';
}

class EbmApplicationNodeVoucher {
  /** 员工业务办理节点ID */
  applicationNodeId = undefined;

  /** createBy */
  createBy = '';

  /** createDt */
  createDt = '';

  /** isDeleted */
  isDeleted = '';

  /** mimicBy */
  mimicBy = '';

  /** proxyBy */
  proxyBy = '';

  /** updateBy */
  updateBy = '';

  /** updateDt */
  updateDt = '';

  /** 凭证文件ID */
  voucherFileId = undefined;

  /** 主键 */
  voucherId = undefined;

  /** 凭证数据来源 1 HRO 2 EOS */
  voucherType = undefined;
}

class EbmBusinessApplicationDTO {
  /** 主键 申请id */
  applicationId = undefined;

  /** 接单客服ID */
  assigneeCsId = undefined;

  /** 接单客服名称 */
  assigneeCsName = '';

  /** 派单部门名称 */
  assignerDepartmentName = '';

  /** 后道客服 */
  bankendCsId = undefined;

  /** 后道客服名称 */
  bankendCsName = '';

  /** 各地业务内容ID */
  busCityConfigId = undefined;

  /** 业务内容 */
  busContent = '';

  /** 业务项目 */
  busnameClassId = '';

  /** 业务项目 */
  busnameClassName = '';

  /** 业务类型 */
  categoryId = '';

  /** 业务类型 */
  categoryName = '';

  /** 收取金额 */
  chargeAmount = '';

  /** 城市名称 */
  cityName = '';

  /** 是否客户端显示(客户端可见1 ，不可见0) */
  clientShowState = '';

  /** 是否客户端显示(客户端可见1 ，不可见0) */
  clientShowStateName = '';

  /** 创建人名称 */
  createByName = '';

  /** 创建时间 */
  createDt = '';

  /** 当前办理节点 */
  currentNode = undefined;

  /** 客户编号 */
  custId = undefined;

  /** 客户名称 */
  custName = '';

  /** 缴费实体ID */
  custPayEntityId = '';

  /** 缴费实体名称 */
  custPayEntityName = '';

  /** 唯一号 */
  empCode = '';

  /** 上下岗ID */
  empHireSepId = undefined;

  /** 员工ID */
  empId = undefined;

  /** 姓名 */
  empName = '';

  /** 到期天数 */
  expireDays = undefined;

  /** 办结人 */
  finishByName = '';

  /** 办结时间 */
  finishDate = '';

  /** 身份证号 */
  idCardNum = '';

  /** 是否收取业务费用 */
  isChargeBusiness = undefined;

  /** 是否收取业务费用名称 */
  isChargeBusinessName = '';

  /** 是否收取客户费用 0 否 1是 */
  isChargeCust = undefined;

  /** 是否收取客户费用名称 */
  isChargeCustName = '';

  /** 是否确认办理 0 否 1是 */
  isConfirmHandle = undefined;

  /** 是否单立户  0否 1是 */
  isIndependent = undefined;

  /** 是否单立户名称 */
  isIndependentName = '';

  /** 是否自助办理（1是、0否） */
  isSelfTransact = undefined;

  /** 材料列表 */
  materialList = [];

  /** 是否需要联系员工提交材料 0 否 1 是 */
  needEmpMaterial = undefined;

  /** 是否需要联系员工提交材料名称 */
  needEmpMaterialName = '';

  /** 节点信息 */
  node = new EbmApplicationNode();

  /** nodeList */
  nodeList = [];

  /** 进度 */
  progress = '';

  /** 进度名称 */
  progressName = '';

  /** 项目客服 */
  projectCsId = undefined;

  /** 项目客服名称 */
  projectCsName = '';

  /** 备注 */
  remark = '';

  /** 办理结果 1 全部成功 2  全部失败 */
  result = undefined;

  /** 办理结果名称 */
  resultName = '';

  /** 新增时入离职状态 */
  sepStatus = undefined;

  /** shipmentList */
  shipmentList = [];

  /** 业务来源 1 HRO 2 EOS 3 微信端 */
  sourceType = undefined;

  /** 业务来源名称 */
  sourceTypeName = '';

  /** 新增时实做状态 */
  ssStatus = undefined;

  /** 业务状态 1办理中 ，2办理完成 ，3取消  4终止 */
  status = undefined;

  /** 业务状态名称 */
  statusName = '';

  /** 手机号 */
  telephone = '';

  /** 办理对象 */
  transObject = undefined;

  /** 办理对象名称 */
  transObjectName = '';

  /** 办理属性 */
  transProperty = undefined;

  /** 办理属性名称 */
  transPropertyName = '';

  /** 办理对象(1客户、2员工) */
  transactObject = undefined;

  /** 办理对象(1客户、2员工) */
  transactObjectName = '';

  /** 办理属性(1流程业务、2单次业务) */
  transactProperty = undefined;

  /** 办理属性(1流程业务、2单次业务) */
  transactPropertyName = '';

  /** 办理方式(1客户办理、2员工办理 ,逗号分隔字符串 ) */
  transactType = '';

  /** 办理方式(1客户办理、2员工办理 ,逗号分隔字符串 ) */
  transactTypeStr = '';

  /** 办理方式(1客户办理、2员工办理 ,逗号分隔字符串 ) */
  transactTypeStrName = '';

  /** voucherList */
  voucherList = [];

  /** 微信端业务进度查询 0 不开通 1 开通 */
  wechatProgressQuery = undefined;

  /** 是否微信显示(微信端可见1 ，不可见0) */
  wxShowState = '';

  /** 是否微信显示(微信端可见1 ，不可见0) */
  wxShowStateName = '';
}

class EbmBusinessCityConfigDTO {
  /** 各地业务办理id */
  busCityConfigId = '';

  /** busConfigId */
  busConfigId = '';

  /** 业务内容 */
  busContent = '';

  /** 业务办理说明 */
  busContentDesc = '';

  /** 业务项目 */
  busnameClassId = '';

  /** 业务项目 */
  busnameClassName = '';

  /** 业务类型 */
  categoryId = '';

  /** 业务类型 */
  categoryName = '';

  /** 城市id */
  cityId = '';

  /** 城市Name */
  cityName = '';

  /** 是否客户端显示(客户端可见1 ，不可见0) */
  clientShowState = '';

  /** 是否客户端显示(客户端可见1 ，不可见0) */
  clientShowStateName = '';

  /** 实做要求  ，3 在缴4停缴5过期 */
  doRequire = '';

  /** 实做要求  ，3 在缴4停缴5过期 */
  doRequireName = '';

  /** 员工自助办理途径 */
  empSelfContent = '';

  /** 是否有效：  0 无效 1 有效 */
  isCityValid = '';

  /** 是否有效：  0 无效 1 有效 */
  isCityValidName = '';

  /** 订单要求，状态1入职未生效2在职3离职 ) */
  orderRequire = '';

  /** 订单要求，状态1入职未生效2在职3离职 ) */
  orderRequireName = '';

  /** 缴纳产品要求(200:养老保险，201:医疗保险，202: 失业保险，203:工伤保险，204:生育保险，240: 住房公积金)    存逗号分隔的字符串 */
  productRequire = '';

  /** 缴纳产品要求(200:养老保险，201:医疗保险，202: 失业保险，203:工伤保险，204:生育保险，240: 住房公积金)    存逗号分隔的字符串 */
  productRequireName = '';

  /** 办理对象(1客户、2员工) */
  transactObject = '';

  /** 办理对象(1客户、2员工) */
  transactObjectName = '';

  /** 办理属性(1流程业务、2单次业务) */
  transactProperty = '';

  /** 办理属性(1流程业务、2单次业务) */
  transactPropertyName = '';

  /** 办理方式(1易才办理、2员工办理 ,逗号分隔字符串 ) */
  transactType = '';

  /** 办理方式(1易才办理、2员工办理 ,逗号分隔字符串 ) */
  transactTypeName = '';

  /** 是否微信显示(微信端可见1 ，不可见0) */
  wxShowState = '';

  /** 是否微信显示(微信端可见1 ，不可见0) */
  wxShowStateName = '';
}

class EbmBusinessCnMaterialDTO {
  /** 各地业务内容id */
  busCityConfigId = '';

  /** 节点id */
  busCityNodeConfigId = '';

  /** 主键ID */
  busCnMaterialId = '';

  /** 是否原件 */
  isOriginal = '';

  /** 是否原件名称 */
  isOriginalName = '';

  /** 是否返还材料(1是、0否) */
  isReturnMaterial = '';

  /** 是否返还材料(1是、0否) */
  isReturnMaterialName = '';

  /** 材料模版(存文件id) */
  materialFileId = '';

  /** 材料模版(存文件名称) */
  materialFileName = '';

  /** 材料数量 */
  materialNum = '';

  /** 材料编号 */
  materialsId = '';

  /** 材料名称 */
  materialsName = '';

  /** 用印签字方(1易才章、2客户章、3人员签名) */
  signatoryParty = '';

  /** 用印签字方名称 */
  signatoryPartyName = '';
}

class EbmBusinessMatShipResp {
  /** 材料信息 */
  ebmApplicationNodeMaterialList = [];

  /** 寄件信息 */
  ebmApplicationNodeShipmentList = [];
}

class EbmBusinessQuery {
  /** 结束时间 */
  endDt = '';

  /** 页数 */
  pageNum = undefined;

  /** 每页记录数,默认65536条 */
  pageSize = undefined;

  /** 开始时间 */
  startDt = '';
}

class EcBusiness {
  /** 业务Id */
  businessId = '';

  /** 城市码 */
  cityCode = '';

  /** 电子业务Id */
  eleBusinessId = '';

  /** 电子业务名称 */
  eleBusinessName = '';

  /** 电子签署标志(1未发起、2拟定中、3员工已签署、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废、11客户已签署、12员工已作废) */
  eleBusinessStatus = undefined;

  /** 电子业务状态名称 */
  eleBusinessStatusName = '';

  /** 上下岗id */
  empHireSepId = '';

  /** 员工id */
  empId = '';

  /** 姓名 */
  empName = '';

  /** 身份证号码 */
  idCardNum = '';

  /** 手机号码 */
  phoneNumber = '';
}

class EcQuit {
  /** 离职证明电子合同id */
  certificateEleId = '';

  /** 离职证明电子合同名称 */
  certificateElename = '';

  /** 电子离职证明状态:1未发起、5已完成、6已过期、9已作废 */
  certificateStatus = undefined;

  /** 电子离职证明状态名称:1未发起、5已完成、6已过期、9已作废 */
  certificateStatusName = '';

  /** 签署URL */
  eleSinUrl = '';

  /** 上下岗id */
  empHireSepId = '';

  /** empId */
  empId = '';

  /** 姓名 */
  empName = '';

  /** 身份证号码 */
  idCardNum = '';

  /** 离职材料电子合同id */
  materialEleId = '';

  /** 离职材料电子合同名称 */
  materialEleName = '';

  /** 电子离职材料状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废  */
  materialStatus = undefined;

  /** 电子离职材料状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废  */
  materialStatusName = '';

  /** 手机号码 */
  phoneNumber = '';

  /** 离职任务编号 */
  quitTaskId = undefined;
}

class EleContract {
  /** 城市码 */
  cityCode = '';

  /** 电子合同Id */
  eleContractId = '';

  /** 电子合同名称 */
  eleContractName = '';

  /** 电子合同状态 */
  eleContractStatus = '';

  /** 签署URL */
  eleSinUrl = '';

  /** 雇员Id */
  empId = '';

  /** 姓名 */
  empName = '';

  /** 身份证号码 */
  idCardNum = '';

  /** 劳动合同id */
  laborContractId = '';

  /** 手机号码 */
  phoneNumber = '';
}

class ElecSignResult {
  /** 电子签链接 */
  eleSinUrl = '';

  /** 管家地址 */
  stewardAddr = '';

  /** 管家姓名 */
  stewardName = '';

  /** 管家联系方式 */
  stewardTel = '';

  /** 管家油箱 */
  stewardmail = '';
}

class EmpFeeCity {
  /** 城市名称 */
  cityName = '';

  /** 客户名称 */
  custName = '';
}

class EmpFeeProcess {
  /** 产品名称列表 */
  products = '';

  /** 参保状态 */
  statusWx = '';
}

class FringeBenefitsQuery {
  /** busnameSubtypeId */
  busnameSubtypeId = '';

  /** categoryId */
  categoryId = '';
}

class FringeBenefitsVO {
  /** accordingFileNumber1 */
  accordingFileNumber1 = '';

  /** accordingFileNumber2 */
  accordingFileNumber2 = '';

  /** accordingFileNumber3 */
  accordingFileNumber3 = '';

  /** benefitsId */
  benefitsId = undefined;

  /** busnameClassId */
  busnameClassId = undefined;

  /** busnameClassName */
  busnameClassName = '';

  /** busnameSubtypeId */
  busnameSubtypeId = undefined;

  /** busnameSubtypeName */
  busnameSubtypeName = '';

  /** busnameTypeId */
  busnameTypeId = undefined;

  /** busnameTypeName */
  busnameTypeName = '';

  /** categoryId */
  categoryId = undefined;

  /** categoryName */
  categoryName = '';

  /** cityId */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** createBy */
  createBy = undefined;

  /** createDt */
  createDt = '';

  /** crossCityHandle */
  crossCityHandle = undefined;

  /** crossCityHandleArea */
  crossCityHandleArea = '';

  /** crossCityHandleText */
  crossCityHandleText = '';

  /** crossProvinceHandle */
  crossProvinceHandle = undefined;

  /** crossProvinceHandleArea */
  crossProvinceHandleArea = '';

  /** crossProvinceHandleText */
  crossProvinceHandleText = '';

  /** crossRegionHandle */
  crossRegionHandle = undefined;

  /** crossRegionHandleArea */
  crossRegionHandleArea = '';

  /** crossRegionHandleText */
  crossRegionHandleText = '';

  /** eHandleCondition */
  eHandleCondition = '';

  /** eHandleOfflineProcess */
  eHandleOfflineProcess = '';

  /** eHandleProcess1 */
  eHandleProcess1 = '';

  /** eHandleProcess2 */
  eHandleProcess2 = '';

  /** eHandleProcess3 */
  eHandleProcess3 = '';

  /** effectiveDate1 */
  effectiveDate1 = '';

  /** effectiveDate2 */
  effectiveDate2 = '';

  /** effectiveDate3 */
  effectiveDate3 = '';

  /** fileName1 */
  fileName1 = '';

  /** fileName2 */
  fileName2 = '';

  /** fileName3 */
  fileName3 = '';

  /** handleForm */
  handleForm = '';

  /** handleType */
  handleType = undefined;

  /** handleTypeText */
  handleTypeText = '';

  /** handleWindow1 */
  handleWindow1 = '';

  /** handleWindow2 */
  handleWindow2 = '';

  /** handleWindow3 */
  handleWindow3 = '';

  /** isBooked */
  isBooked = '';

  /** isDeleted */
  isDeleted = undefined;

  /** isValid */
  isValid = '';

  /** makReservations */
  makReservations = undefined;

  /** makReservationsText */
  makReservationsText = '';

  /** orderStatus */
  orderStatus = undefined;

  /** orderStatusText */
  orderStatusText = '';

  /** otherHandleInfo1 */
  otherHandleInfo1 = '';

  /** otherHandleInfo2 */
  otherHandleInfo2 = '';

  /** otherHandleInfo3 */
  otherHandleInfo3 = '';

  /** otherHandleInfo4 */
  otherHandleInfo4 = '';

  /** otherHandleInfo5 */
  otherHandleInfo5 = '';

  /** otherHandleInfo6 */
  otherHandleInfo6 = '';

  /** otherPolicyInfo1 */
  otherPolicyInfo1 = '';

  /** otherPolicyInfo2 */
  otherPolicyInfo2 = '';

  /** otherPolicyInfo3 */
  otherPolicyInfo3 = '';

  /** otherPolicyInfo4 */
  otherPolicyInfo4 = '';

  /** otherPolicyInfo5 */
  otherPolicyInfo5 = '';

  /** otherPolicyInfo6 */
  otherPolicyInfo6 = '';

  /** otherPolicyInfo7 */
  otherPolicyInfo7 = '';

  /** otherPolicyInfo8 */
  otherPolicyInfo8 = '';

  /** otherPolicyInfo9 */
  otherPolicyInfo9 = '';

  /** pHandleCondition */
  pHandleCondition = '';

  /** pHandleOfflineProcess */
  pHandleOfflineProcess = '';

  /** pHandleProcess1 */
  pHandleProcess1 = '';

  /** pHandleProcess2 */
  pHandleProcess2 = '';

  /** pHandleProcess3 */
  pHandleProcess3 = '';

  /** payee */
  payee = '';

  /** personCategoryId */
  personCategoryId = undefined;

  /** personCategoryName */
  personCategoryName = '';

  /** policyFileId1 */
  policyFileId1 = '';

  /** policyFileId2 */
  policyFileId2 = '';

  /** policyFileId3 */
  policyFileId3 = '';

  /** policyFileName1 */
  policyFileName1 = '';

  /** policyFileName2 */
  policyFileName2 = '';

  /** policyFileName3 */
  policyFileName3 = '';

  /** policySource1 */
  policySource1 = '';

  /** policySource2 */
  policySource2 = '';

  /** policySource3 */
  policySource3 = '';

  /** policyUrl1 */
  policyUrl1 = '';

  /** policyUrl2 */
  policyUrl2 = '';

  /** policyUrl3 */
  policyUrl3 = '';

  /** processDifference */
  processDifference = '';

  /** ssStatus */
  ssStatus = undefined;

  /** ssStatusText */
  ssStatusText = '';

  /** statutoryDeadline */
  statutoryDeadline = '';

  /** supplementaryInfo1 */
  supplementaryInfo1 = '';

  /** supplementaryInfo2 */
  supplementaryInfo2 = '';

  /** supplementaryInfo3 */
  supplementaryInfo3 = '';

  /** supplementaryInfo4 */
  supplementaryInfo4 = '';

  /** supplementaryInfo5 */
  supplementaryInfo5 = '';

  /** supplementaryInfo6 */
  supplementaryInfo6 = '';

  /** termsContent1 */
  termsContent1 = '';

  /** termsContent2 */
  termsContent2 = '';

  /** termsContent3 */
  termsContent3 = '';

  /** tollHandle */
  tollHandle = undefined;

  /** tollHandleText */
  tollHandleText = '';

  /** tollStandard */
  tollStandard = '';

  /** updateBy */
  updateBy = undefined;

  /** updateDt */
  updateDt = '';

  /** windowAddress */
  windowAddress = '';
}

class HttpResult {
  /** 返回状态code */
  code = '';

  /** 错误信息 */
  errorMsg = '';

  /** 具体的返回结果 */
  resultObj = new BusNameClass();

  /** suc */
  suc = false;
}

class HttpRpaResult {
  /** 具体返回内容 */
  msg_response = new InnerResponse();

  /** Web API调用成功或失败 ， success->成功 ， 其他->失败 */
  parse_result = '';
}

class HzSupplyCert {
  /** 审批状态 */
  auditStatus = '';

  /** 证件id */
  certId = '';

  /** 证件名称 */
  certName = '';

  /** 文件名称 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** 是否必填 1是 0否 */
  isMust = '';

  /** 上传文件id */
  mediaId = '';

  /** openId */
  openId = '';

  /** 驳回原因 */
  rejectReason = '';

  /** 主键 */
  supplyCertId = '';

  /** 批次id */
  supplyId = '';

  /** 上传方 1客服 2个人 */
  uploadType = '';
}

class InnerResponse {
  /** 移出场景内的变量 */
  remove = new AveSalaryStr();

  /** 存进场景内的变量 */
  update = new AveSalaryStr();
}

class Map {}

class MaterialsPackage {
  /** 小类id */
  busSubtypeId = '';

  /** 大类id */
  busTypeId = '';

  /** 是否是单位办理材料1 是 0否 */
  isEMaterial = '';

  /** 是否原件 */
  isOriginal = '';

  /** 是否是个人办理材料1 是 0否 */
  isPMaterial = '';

  /** 是否返还给申请人 */
  isReturn = '';

  /** 材料数量 */
  materialsAccount = '';

  /** 材料编号 */
  materialsId = '';

  /** 材料名称 */
  materialsName = '';

  /** 材料包id */
  packageId = '';
}

class PersonCategory {
  /** 人员分类id */
  personCategoryId = '';

  /** 人员分类名称 */
  personCategoryName = '';
}

class PolicyDetail {
  /** 生效日期 */
  effectiveDate = '';

  /** 失效日期 */
  expirationDate = '';

  /** 所属年份 */
  policyTemplateInfoYear = undefined;

  /** 服务类型名称 */
  serviceTypeName = '';

  /** templateGroupRespList */
  templateGroupRespList = [];

  /** 适用范围:1国家 2省份3城市4特区 */
  templateScope = undefined;
}

class PolicyField {
  /** 客户端是否显示 0：不显示,1：显示 */
  clientShowState = undefined;

  /** 字段编码 */
  fieldCode = '';

  /** 字段名称 */
  fieldName = '';

  /** 字段值 */
  fieldValue = '';

  /** 附件名称 */
  fileName = '';

  /** HRO端是否显示 0：不显示,1：显示 */
  hroShowState = undefined;

  /** 是否删除 0否 1是 */
  isDeleted = '';

  /** 字段是否必填 0否 1是 */
  isMust = undefined;

  /** 字段选项 */
  items = '';

  /** 政策模板字段ID */
  policyTemplateFieldId = '';

  /** 政策模板分组ID */
  policyTemplateGroupId = '';

  /** 显示顺序 */
  seqNum = undefined;

  /** 模板状态  状态:0初始 1有效 2无效 */
  templateState = undefined;

  /** 字段类型:1.文本 2.多行文本 3.日期 4.下拉菜单 5.链接 6.附件 7.多选项 8.数字(整数) 9.数字(小数) */
  type = undefined;

  /** 超链接名称 */
  urlName = '';

  /** wx端是否显示 0：不显示,1：显示 */
  wxShowState = undefined;
}

class PolicyGroup {
  /** 客户端是否显示 0：不显示,1：显示 */
  clientShowState = undefined;

  /** 字段列表 */
  fields = [];

  /** 分组编号 */
  groupCode = '';

  /** HRO端是否显示 0：不显示,1：显示 */
  hroShowState = undefined;

  /** 是否删除 0否 1是 */
  isDeleted = '';

  /** 政策模板分组ID */
  policyTemplateGroupId = '';

  /** 政策模板ID */
  policyTemplateId = '';

  /** 显示顺序 */
  seqNum = undefined;

  /** 分组名称 */
  templateGroupName = '';

  /** 模板状态  状态:0初始 1有效 2无效 */
  templateState = undefined;

  /** wx端是否显示 0：不显示,1：显示 */
  wxShowState = undefined;
}

class PolicyLevelTree {
  /** children */
  children = [];

  /** id */
  id = '';

  /** name */
  name = '';

  /** pId */
  pId = '';
}

class PrivacyParam {
  /** openId */
  openId = '';

  /** 隐私协议当前最新版本id */
  privacyId = undefined;

  /** 隐私协议当前最新版本号 */
  version = undefined;
}

class PrivacyRegisterDTO {
  /** 隐私协议当前最新版本的内容,只有当用户没签最新协议时才返回 */
  content = '';

  /** 隐私协议当前最新版本id,如果返回-1，说明压根没有维护的记录，这时候不能让他同意 */
  privacyId = undefined;

  /** 隐私协议登记记录主表id,只有当用户已经签过了最新协议后才返回 */
  privacyRegisterId = undefined;

  /** registed */
  registed = false;

  /** 隐私协议当前最新版本号,如果返回-1，说明压根没有维护的记录，这时候不能让他同意 */
  version = undefined;
}

class QueryPolicyEncyclopedia {
  /** 城市id集合 */
  cityIds = [];

  /** endIndex */
  endIndex = undefined;

  /** 政策生效状态 0初始 1待生效 2 生效 3失效 */
  infoState = undefined;

  /** 政策生效状态 0初始 1待生效 2 生效 3失效 */
  infoStates = [];

  /** 是否全部城市:0否，1是 */
  isAllCity = undefined;

  /** 是否全部省份:0否，1是 */
  isAllProvince = undefined;

  /** 是否全部特区i:0否，1是 */
  isAllSpecialArea = undefined;

  /** 一级名称Id */
  level1Id = '';

  /** 一级名称 */
  level1Name = '';

  /** 二级名称Id */
  level2Id = '';

  /** 二级名称 */
  level2Name = '';

  /** 是否分页查询（true:分页；false:不分页） */
  pageQuery = false;

  /** 政策模板详情ID */
  policyTemplateInfoId = undefined;

  /** 所属年份 */
  policyTemplateInfoYear = '';

  /** 所属年份 */
  policyTemplateInfoYears = [];

  /** 政策标题Id */
  policyTitleId = '';

  /** 省份城市 */
  provinceCity = '';

  /** 省份id集合 */
  provinceIds = [];

  /** 适用范围:1国家 2省份3城市4特区 */
  queryScopes = [];

  /** 服务类型 */
  serviceType = undefined;

  /** 服务类型名称 */
  serviceTypeName = '';

  /** 服务类型集合 */
  serviceTypes = [];

  /** 特区id集合 */
  specialAreaIds = [];

  /** startIndex */
  startIndex = undefined;

  /** 政策详情名称 */
  templateInfoName = '';

  /** 适用范围:1国家 2省份3城市 */
  templateScope = '';
}

class RpaEmpFee {
  /** 合计金额 */
  amt = '';

  /** 没权限提示文本信息 */
  authorityHintMsg = '';

  /** 社保组类型 1社保 2公积金 */
  category = '';

  /** 客户id */
  custId = '';

  /** 客户名称 */
  custName = '';

  /** 企业金额 */
  eAmt = '';

  /** 企业基数 */
  eBase = '';

  /** empFeeProcessList */
  empFeeProcessList = [];

  /** 雇员id */
  empId = '';

  /** 是否授权 1是 0否 */
  ifAuthority = false;

  /** 是否有数据 1是 0否 */
  ifHasData = false;

  /** 个人金额 */
  pAmt = '';

  /** 个人基数 */
  pBase = '';

  /** 参保项目与状态 */
  productStatusName = '';

  /** 服务月 */
  serviceMonth = '';
}

class Salary {
  /** 没权限提示文本信息 */
  authorityHintMsg = '';

  /** 客户名称 */
  custName = '';

  /** empId */
  empId = '';

  /** 收入合计 */
  f1 = '';

  /** 本次扣税 */
  f10 = '';

  /** 扣款合计 */
  f2 = '';

  /** 实发合计 */
  f3 = '';

  /** 是否授权 true是 false否 */
  ifAuthority = false;

  /** 是否有数据 true是 false否 */
  ifHasData = false;

  /** sendId */
  sendId = '';

  /** sendMonth */
  sendMonth = '';

  /** 发放状态 */
  wageBatchStatusName = '';

  /** 报税状态 */
  wageTaxStatus = '';
}

class SsProcess {
  /** 办理时间 */
  createdt = '';

  /** 社保组 */
  groupName = '';

  /** 社保组产品 */
  products = '';

  /** 办理状态 */
  statusWx = '';
}

class SsProcessDto {
  /** empId */
  empId = '';

  /** 1 社保，2 公积金 */
  groupType = '';
}

class StewardInfo {
  /** 地址 */
  address = '';

  /** 联系方式 */
  contact = '';

  /** 雇员id（不用） */
  empId = '';

  /** 邮箱 */
  mail = '';

  /** 供应商类型（不用） */
  providerType = '';

  /** 管家姓名 */
  stewardName = '';
}

class TokenCheck {
  /** 当前用户类型 1:认证用户 2:匿名用户 */
  userType = undefined;
}

class WechatHiresepDTO {
  /** 注册账号对应的ID */
  accountId = '';

  /** 办理城市id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 微信入职办理主表id */
  empHiresepMainId = '';

  /** 低代码表单的json数据 */
  jsonStr = '';

  /** 关联照片信息的uuid */
  uuid = '';
}

export const ncmp = {
  AcCheckLog,
  AcHolApply,
  AveSalary,
  AveSalaryStr,
  BusNameClass,
  BusinessSubType,
  BusinessSubTypeMailQuery,
  BusinessType,
  City,
  EbmApplicationNode,
  EbmApplicationNodeMaterial,
  EbmApplicationNodeShipment,
  EbmApplicationNodeVoucher,
  EbmBusinessApplicationDTO,
  EbmBusinessCityConfigDTO,
  EbmBusinessCnMaterialDTO,
  EbmBusinessMatShipResp,
  EbmBusinessQuery,
  EcBusiness,
  EcQuit,
  EleContract,
  ElecSignResult,
  EmpFeeCity,
  EmpFeeProcess,
  FringeBenefitsQuery,
  FringeBenefitsVO,
  HttpResult,
  HttpRpaResult,
  HzSupplyCert,
  InnerResponse,
  Map,
  MaterialsPackage,
  PersonCategory,
  PolicyDetail,
  PolicyField,
  PolicyGroup,
  PolicyLevelTree,
  PrivacyParam,
  PrivacyRegisterDTO,
  QueryPolicyEncyclopedia,
  RpaEmpFee,
  Salary,
  SsProcess,
  SsProcessDto,
  StewardInfo,
  TokenCheck,
  WechatHiresepDTO,
};
