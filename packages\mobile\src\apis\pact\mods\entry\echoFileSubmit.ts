/**
 * @description 回显文件再次提交
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.EchoFileSubmitResp;
export const path = '/yc-wepact-mobile/entry/echoFileSubmit';
export const method = 'POST';
export const request = (
  data: defs.pact.EchoFileSubmitReqBean,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.EchoFileSubmitReqBean,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
