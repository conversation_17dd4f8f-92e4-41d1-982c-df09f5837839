/**
 * @description 身份证正反面合成
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.IdCardComposeRespBean;
export const path = '/yc-wepact-mobile/entry/idCardCompose';
export const method = 'POST';
export const request = (
  data: defs.pact.IdCardComposePram,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.IdCardComposePram,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
