// 按钮图标类型枚举
export enum ButtonIconType {
  DETAIL = 'detail',
  CANCEL = 'cancel',
  ORDER = 'order'
}

// 按钮尺寸枚举
export enum ButtonSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large'
}

// 按钮类型枚举
export enum ButtonType {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  DANGER = 'danger'
}

// 按钮组件属性接口
export interface ButtonsProps {
  /** 按钮文本 */
  title?: string
  /** 按钮图标类型 */
  icon?: ButtonIconType | string
  /** 按钮类型 */
  type?: ButtonType | string
  /** 按钮尺寸 */
  size?: ButtonSize | number
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示加载状态 */
  loading?: boolean
  /** 点击事件处理函数 */
  onClick?: () => void
  /** 自定义样式类名 */
  className?: string
  /** 测试ID */
  testId?: string
}

// 图标配置接口
export interface IconConfig {
  src: string
  className: string
}

// 图标配置映射类型
export type IconConfigMap = Record<ButtonIconType, IconConfig>

// 按钮样式映射类型
export type ButtonStyleMap = Record<ButtonIconType, string>
