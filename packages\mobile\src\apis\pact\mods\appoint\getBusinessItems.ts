/**
 * @description 预约业务项目
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openId */
  openId?: string;
}

export type Result = defs.pact.BusinessItemBean;
export const path = '/yc-wepact-mobile/appoint/getBusinessItems';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
