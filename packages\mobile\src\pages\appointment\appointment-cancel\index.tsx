/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-17 16:07:15
 * @LastAuthor: 王正荣
 * @LastTime: 2021-09-17 17:51:39
 * @message: 
 */
import { View, Text, ScrollView } from '@tarojs/components'
import { useState } from 'react'
import { dynamicStyle, getGlobalData, getScrollStyle } from '@utils'
import { pact } from '@apis/pact'
import { BottomBtn, withPage, FormLabel, Modal } from '@components'
import Taro, { useRouter } from '@tarojs/taro'
import styles from './index.module.scss'

const Index = () => {
  const { openId, empId } = getGlobalData<'account'>('account')
  const { bookingId, source } = useRouter<{ bookingId: string; source: string }>().params
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const [active, setActive] = useState('')
  const [cancelReason, setSetReasoh] = useState('')
  const [selected, setSelected] = useState(false)
  const [visible, setVisible] = useState(false)
  
  const setReasoh = (key: string, reason: string) => {
    setSelected(true)
    setActive(key)
    setSetReasoh(reason)
  }

  const cancelAppointment = async () => {
    await pact.appoint.cancelAppointment.request({
      bookingId,
      cancelReason,
      empId,
      openId
    }).then((res) => {
      if (res.code === '200') {
        Taro.navigateTo({ url: `/pages/appointment/cancel-success/index?source=${Number(source)+1}` });
      }
    })
  }

  return (
    <View>
      <ScrollView style={scrollStyle} scrollY>
        <FormLabel level={2} required title='取消原因' />
        <View className={styles.buttons_wrap}>
          {['时间冲突', '重复预约', '预约信息填写错误'].map((item, index) => (
            <View
              key={index}
              {...dynamicStyle(active === String(index + 1) ? styles.tag_btn_active : styles.tag_btn)}
              onClick={() => setReasoh(String(index + 1), item)}
            >
              <Text>{item}</Text>
            </View>
          ))}
        </View>
        <Modal
          visible={visible}
          onConfirm={() => {
            setVisible(false)
            cancelAppointment()
          }}
          content='是否取消预约'
        />
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '取消预约',
            onClick: () => selected ? setVisible(true) : {},
            disabled: !selected
          },
          {
            title: '放弃',
            type: 'ghost',
            onClick: () => {
              Taro.navigateBack({ delta: Number(source) })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
