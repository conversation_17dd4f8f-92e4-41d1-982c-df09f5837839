import { View, Image, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { BottomBtn, withPage } from '@components'
import styles from './index.module.scss'

const Index = () => {
  return (
    <View className={styles.wrap}>
      
        <Image className={styles.img} src={require('@assets/steward/subImg.png')} />
        <View className={styles.title}>提交成功</View>
        <View>您的申请已提交，预计从2021-09起之后的计税月开始抵扣</View>
      
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () => {
              Taro.navigateTo({
                url: `/pages/home/<USER>
              })
            }
          }
        ]}
      />
    </View>
  )
}

export default Index
