/**
 * @description 获取易才HRO系统中易才管家信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** empId */
  empId?: string;
  /** openId */
  openId?: string;
}

export type Result = defs.pact.ButlerResponseBean;
export const path = '/yc-wepact-mobile/per/getSteward';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
