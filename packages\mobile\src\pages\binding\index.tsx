import { View, Text, Image, Input } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { isPhoneNumber } from '@utils/is-type'
import { useEffect, useState } from 'react'
import { pact } from '@apis/pact'
import { auth } from '@apis/auth'
import { getGlobalData, setGlobalData, getScrollStyle, isWx, getPlatformCode, showSuccess } from '@utils'
import { Form, BottomBtn, FormItemProps, withPage, useForm } from '@components'
import styles from './index.module.scss'

type FormValues = {
  idCardNum: string
  mobilePhoneNum: string
  vcode: string
  verificationCode: string
}
const smsMsg = [
  '不是内部员工，无法绑定!',
  '',
  '您是我们的内部员工,但是没开通,请联系企业人事!',
  '系统中没有找到手机号码,请联系企业人事!',
  '您的操作过于频繁,请1分钟后请求!',
  '验证码无效!'
]
const bindMsg = [
  '不是内部员工，无法绑定!',
  '绑定成功!',
  '您是我们的内部员工,但是没开通,请联系企业人事!',
  '',
  '您的操作过于频繁,请1分钟后请求!',
  '验证码无效!'
]
const COUNT_SEC = 60

const SendVerifyBtn = ({ checkValue }) => {
  const { openId, accountId } = getGlobalData<'account'>('account') || {}
  const [sec, setSec] = useState(COUNT_SEC)
  const [loading, setLoading] = useState(false) // 开始调接口
  const [timing, setTiming] = useState(false) // 开始倒计时
  useEffect(() => {
    let interval
    // 开始倒计时
    if (timing) {
      interval = setInterval(() => {
        setSec(preSecond => {
          if (preSecond <= 1) {
            setTiming(false)
            clearInterval(interval)
            // 重置秒数
            return COUNT_SEC
          } else {
            return preSecond - 1
          }
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [timing])

  const sendPhoneCode = async () => {
    const formData = await checkValue()
    if (isWx()) {
      if (formData && !loading && !timing) {
        setLoading(true)
        await pact.auth.sendSMS
          .request({ accountId, openId, ...formData })
          .then(res => {
            const result = res.data?.result || 'unexpected wrong'
            if (result == '1') {
              showSuccess('验证码发送成功')
              setTiming(true)
            } else {
              Taro.showToast({ title: smsMsg[result] || result, icon: 'none' })
            }
          })
          .finally(() => setLoading(false))
      }
    } else {
      if (timing) return
      const PlatformInfo = sessionStorage.getItem('PlatformInfo')
      if (PlatformInfo) {
        const platformOpenid = JSON.parse(PlatformInfo).platformOpenid;
        auth.authWeb.getMobileCode.request({ platformOpenid, ...formData, mobilePhoneNum: formData.cellphone, cellphone: undefined, platform: getPlatformCode() }).then(res => {
          if (res.success) {
            showSuccess('验证码发送成功')
            setTiming(true)
          }
        })
      }
    }
  }
  return (
    <View className={styles.btn_sm_wrap} onClick={sendPhoneCode}>
      <Text className={styles.btn_sm}>{timing ? `${sec}s` : '获取验证码'}</Text>
    </View>
  )
}

const Index = () => {
  const account = getGlobalData<'account'>('account')
  const { openId, accountId } = account || {}
  const [verifyImg, setVerifyImg] = useState<string>('')
  const form = useForm<FormValues>()
  const scrollStyle = getScrollStyle({ bottom: 120, top: 400, hasNav: false })
  const columns: FormItemProps[] = [
    {
      title: '证件号',
      name: 'idCardNum',
      type: 'text',
      rules: { required: '请填写正确的证件号码' },
      inputProps: { maxlength: 18 },
      showLine: false
    },
    {
      title: '手机号码',
      name: 'mobilePhoneNum',
      type: 'mobile',
      rules: { validate: val => isPhoneNumber(val), required: true },
      inputProps: { maxlength: 11 },
      customMsg: '请填写正确的手机号码',
      showLine: false
    },
    {
      title: '验证码',
      name: 'vcode',
      rules: { required: '请填写验证码' },
      showLine: false,
      render: field => (
        <View className={styles.ipt_wrap}>
          <Input onInput={field.onChange} {...field} className={styles.input_code} maxlength={4} />
          {
            verifyImg && (<Image src={`data:image/png;base64,${verifyImg}`} className={styles.ver_code} onClick={updateVerifyImg} />)
          }
        </View>
      )
    },
    {
      title: '手机验证码',
      name: 'verificationCode',
      rules: { required: '请填写手机验证码', validate: val => val?.length == 6 },
      customMsg: '手机验证码有误',
      showLine: false,
      render: field => (
        <View className={styles.ipt_wrap}>
          <Input onInput={field.onChange} {...field} className={styles.input_code} maxlength={6} />
          <SendVerifyBtn checkValue={checkValue} />
        </View>
      )
    }
  ]
  useEffect(() => {
    updateVerifyImg()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const updateVerifyImg = async () => {
    if (isWx()) {
      await pact.auth.getVcode.request({ openId: openId, accountId: accountId }).then(res => {
        setVerifyImg(res?.data?.base64 || '')
      })
    } else {
      const PlatformInfo = sessionStorage.getItem('PlatformInfo')
      if (PlatformInfo) {
        const platformOpenid = JSON.parse(PlatformInfo).platformOpenid;
        platformOpenid && await auth.authWeb.getImgCode.request({ platformOpenid, platform: getPlatformCode() }).then(res => {
          res.data?.base64Img && setVerifyImg(res.data?.base64Img)
        })
      }
    }
  }

  const checkValue = async () => {
    const { idCardNum, mobilePhoneNum, vcode } = form.getValues()
    const isValidate = await form.trigger(['idCardNum', 'mobilePhoneNum', 'vcode'])
    return isValidate ? { cellphone: mobilePhoneNum, idCardNum, vcode } : null
  }
  const goHomePage = async () => {
    await pact.wxConfig.getEpmInfoByOpenId.request({ openId }).then((res) => {
      const newAccount = { ...account, ...res }
      setGlobalData('account', newAccount)
      sessionStorage.setItem('PlatformInfo', JSON.stringify(newAccount))
      if (Taro.getCurrentPages()[0].path === '/policy_query') {
        Taro.navigateTo({ url: '/pages/policy_query/index' })
        return
      }
      Taro.navigateTo({ url: '/pages/home/<USER>' })
    })
  }
  const onSubmit = async data => {
    const { verificationCode, idCardNum, mobilePhoneNum } = data
    if (isWx()) {
      const res = await pact.auth.authenticationBinding.request({
        openId,
        accountInfo: { verificationCode, accountId, idCardNum, mobilePhoneNum }
      })
      const result = res.data?.result || 'unexpected wrong'
      if (result !== '1') {
        Taro.showToast({ title: bindMsg[result] || result, icon: 'none' })
        return
      }
      Taro.showToast({
        title: '绑定成功',
        icon: 'none',
        success: () => {
          goHomePage()
        }
      })
    } else {
      const PlatformInfo = sessionStorage.getItem('PlatformInfo')
      if (PlatformInfo) {
        const PlatformInfoData = JSON.parse(PlatformInfo)
        const platformOpenid = PlatformInfoData.platformOpenid;
        const cropId = JSON.parse(PlatformInfo).cropId;
        platformOpenid && auth.authWeb.updateBind.request({ platformOpenid, platform: getPlatformCode(), mobileVerifyCode: verificationCode, idCardNum, mobilePhoneNum, company: cropId }).then(res => {
          const resdata = res.data
          if (res.success && resdata) {
            sessionStorage.setItem('PlatformInfo', JSON.stringify({ ...PlatformInfoData, ...resdata }))
            Taro.showToast({
              title: '绑定成功', icon: 'none', success: () => {
                Taro.navigateTo({ url: '/pages/home/<USER>' })
              }
            })
          }
        })
      }
    }
  }
  return (
    <View className={styles.wrap}>
      <Image src={require('../../assets/img-banner.png')} className={styles.banner} />
      <Form form={form} columns={columns} style={scrollStyle} />
      <BottomBtn
        btns={[
          {
            title: '绑定',
            onClick: form.handleSubmit(onSubmit)
          },
          {
            title: '返回主菜单',
            onClick: () => {
              Taro.navigateTo({ url: '/pages/home/<USER>' })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
