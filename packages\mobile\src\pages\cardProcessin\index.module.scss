.wrap {
  width: 100%;
  background-color: #ffffff;
}
.header {
  width: 100%;
}
.list {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 40px;
  font-weight: 400;
  padding: 30px;
  justify-content: space-between;
  border: solid 0px #e9ecef;
  border-bottom-width: 1px;
}
.list_item {
  display: flex;
  align-items: center;
}
.icon {
  width: 60px;
  height: 60px;
  margin-right: 15px;
}
.btn {
  border-radius: 40px;
  height: 45px;
  line-height: 45px;
  font-size: 20px;
}
.group_btn {
  background: #adb5bd;
  border-color: transparent;
  color: #fff;
}
.active_btn {
  background: #b51e25;
  border-color: transparent;
  color: #fff;
}
.content {
  padding: 20px;
  .content_text {
    font-size: 15px;
    color: gray;
  }
}
.checkbox {
  align-items: center;
  width: 40px;
  height: 40px;
  background: #fff;
}
.text_color {
  color: red;
}
.ml_20 {
  margin-left: 20px;
}
.mt_20 {
  margin-top: 20px;
}
