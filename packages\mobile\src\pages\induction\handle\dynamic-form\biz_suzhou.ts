import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleSuZhouColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const hasSsCard = form.watch('hasSsCard')
  const culture_suzhou = form.watch('culture_suzhou')
  if (column.name === 'graduationSchool' || column.name === 'graduationDate' || column.name === 'professional'){
    return { ...column, isHidden: !['大学本科', '大学专科'].includes(culture_suzhou)}
  }
  if (column.name === 'ssNumber') {
    return { ...column, isHidden: '0'.includes(hasSsCard)}
  }
  return column;
}

export { handleSuZhouColumn }