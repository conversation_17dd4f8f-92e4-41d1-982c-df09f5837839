// const path = require('path')
// const OUTPUT_FILE_NAME = 'router' || 'page'
// const OUTPUT_FILE_TYPE = '.js'
module.exports = {
  route: {
    //!路由输出文件(自己定义,默认src/page.js)弃用了
    //! output: path.resolve(__dirname, '..', `src/${OUTPUT_FILE_NAME}${OUTPUT_FILE_TYPE}`),
    //默认pages文件夹，即编译路径
    // inputPath
    //文件后缀(默认有)
    suffix: ['.ts', '.js', '.tsx', '.jsx'],
    //index主页名(默认index)，生成的d.ts也有影响
    prefix: 'index',
    //编译路由根路径(默认pages文件夹)，生成的d.ts也有影响，路由起始名  ex: /#/${rootPageName}/*
    rootPageName: 'pages',
    //主页(可能匹配中多条，不影响结果，返回第一条，最好精确写)
    indexPage: 'home',
    // 生成配置文件（默认app.config.ts）
    // configFile:...,
    // 生成的ts文件
    // tsHintPath:...
  },
  //以下是taro配置,上面config可以自定义
  taroConfig: {
    window: {
      backgroundTextStyle: 'light',
      navigationBarBackgroundColor: '#fff',
      navigationBarTitleText: 'WeChat',
      navigationBarTextStyle: 'black',
    },
    rn: {
      screenOptions: {
        // 设置页面的options，参考https://reactnavigation.org/docs/stack-navigator/#options
        shadowOffset: { width: 0, height: 0 },
        borderWidth: 0,
        elevation: 0,
        shadowOpacity: 1,
        borderBottomWidth: 0,
      },
    },
  },
}
