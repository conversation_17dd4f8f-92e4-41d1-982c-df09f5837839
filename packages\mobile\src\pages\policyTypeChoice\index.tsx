import { ScrollView, View, Text, Image} from '@tarojs/components'
import { Fragment, useEffect, useState } from 'react'
import { BottomBtn } from '@components'
import { getGlobalData, getScrollStyle } from '@utils'
import { ncmp } from '@apis/ncmp'
import Taro, { useRouter } from '@tarojs/taro'
import no_data from '@assets/no-data.png'
import styles from './index.module.scss'

const Index = () => {
  const [list, setList] = useState<any[]>([])
  const { eventName, cityId, categoryId, bussNameClassId, busTypeId} = useRouter().params || {}
  const { empId } = getGlobalData<'account'>('account')
  useEffect(() => {
    if (eventName === 'categoryId') {
        setList([
            {key: '1', name: '社保业务'},
            {key: '2', name: '公积金业务'},
            {key: '3', name: '人力资源服务相关业务（原在职业务）'},
            // {key: '4', name: '定点医疗机构变更'},
            // {key: '5', name: '简易业务办理'},
        ])
    }
  }, [eventName])
  useEffect(() => {
    if (eventName === 'bussNameClassId') {
        ncmp.policy.getBusnameClass
        .request({})
        .then(res => {
          if (res.code == '200') {
            let data = res?.resultObj?.map(item => {
              return {
                key: item.bussNameClassId,
                name: item.bussNameClassName
              }
            })
            setList(data)
          } else {
            Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
          }
        })
        .catch(() => {
          Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
        })
    }
  }, [eventName])
  useEffect(() => {
    if (eventName === 'busTypeId') {
        ncmp.policy.getBusType
        .request({busnameClassId:bussNameClassId, categoryId, cityId})
        .then(res => {
          if (res.code == '200') {
            let data = res?.resultObj?.map(item => {
              return {
                key: item.busTypeId,
                name: item.busTypeName
              }
            })
            setList(data)
          } else {
            Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
          }
        })
        .catch(() => {
          Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
        })
    }
  }, [eventName, bussNameClassId, categoryId, cityId])
  useEffect(() => {
    if (eventName === 'busSubtypeId') {
        ncmp.policy.getSubType
        .request({busTypeId, empId })
        .then(res => {
          if (res.code == '200') {
            let data = res?.resultObj?.map(item => {
              return {
                key: item.busSubtypeId,
                name: item.busSubtypeName
              }
            })
            setList(data)
          } else {
            Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
          }
        })
        .catch(() => {
          Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
        })
    }
  }, [eventName, empId, busTypeId])
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const onSubmit = (item:any) => {
    if (item.name) {
        let obj:any
        switch (eventName) {
          case 'categoryId':
            obj = {
              categoryId: item.key,
              categoryName: item.name
            }
            break;
          case 'bussNameClassId':
            obj = {
              bussNameClassId: item.key,
              bussNameClassName: item.name
            }
            break;
          case 'busTypeId':
            obj = {
              busTypeId: item.key,
              busTypeName: item.name
            }
            break;
          case 'busSubtypeId':
            obj = {
              busSubtypeId: item.key,
              busSubtypeName: item.name
            }
            break;
          default:
            break;
        }
        Taro.eventCenter.trigger(eventName, obj)
        Taro.navigateBack()
  }}
  return (
    <Fragment>
      <ScrollView style={scrollStyle} scrollY>
        {
          list.length === 0 ? (
            <View className={styles.empty}>
              <Image className={styles.no_data} src={no_data} />
              <Text className={styles.no_text}>暂无可以查看的数据</Text>
            </View>
          ) : (
            list?.map(item => {
              return (
                <View
                  key={item.key}
                  className={styles.item}
                  onClick={() => { onSubmit(item)}}
                >
                  <Text className={styles.text}>{item.name}</Text>
                </View>
              )
            })
          )
        }
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () => Taro.navigateBack()
          }
        ]}
      />
    </Fragment>
  )
}

export default Index