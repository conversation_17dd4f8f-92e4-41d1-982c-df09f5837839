
import { useMemo } from 'react'
import isEmpty from 'lodash/isEmpty'
import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'
import { handleBeiJingColumn } from './../dynamic-form/biz_beijing'
import { handleChangChunColumn } from './../dynamic-form/biz_changchun'
import { handleSuQianColumn } from './../dynamic-form/biz_suqian'
import { handleDongYingColumn } from './../dynamic-form/biz_dongying'
import { handleTaiAnColumn } from './../dynamic-form/biz_taian'
import { handleShenZhenColumn } from '../dynamic-form/biz_shenzhen'
import { handleXIANColumn } from '../dynamic-form/biz_xian'
import { handleJiLinColumn } from '../dynamic-form/biz_jilin'
import { handleLongYanColumn } from '../dynamic-form/biz_longyan'
import { handleZhangZhouColumn } from '../dynamic-form/biz_zhangzhou'
import { handleTaiYuanColumn } from '../dynamic-form/biz_taiyuan'

const hospitalArr = ['hospital1Id', 'hospital2Id', 'hospital3Id', 'hospital4Id']
export interface FormItemType {
  name: string
  code: string
  titleLevel: 1 | 2 | 3
  requireFlag: boolean
  operation: string | 'textarea' | 'text'
  optionList: []
  restrictValue: string // 最多长度
  defaultValue: string
  remind: string // 提示
  orderValue: number
  subList: FormItemType[]
  itemUUID: string
  parentItemUUID: string
  businessId: string
  itemValue: string
}

let columns: FormItemProps[] | any = []
const handleColumns = (
  _items: FormItemType[],
  cityCode: string,
  form: UseFormReturn,
  page: string,
  isChild: boolean = false
) => {
  if (!isChild) {
    columns = []
  }

  const removeCode = ['post_code', 'hukouZipCode']
  _items.filter(item => !removeCode.includes(item.operation) && (item.code !== 'enterinsurance_beijing' && item.code !== 'unisocialcode_beijing')).map(section => {
    const optionList = section.optionList.map(item => ({
      key: Object.keys(item)[0],
      value: Object.values(item)[0]
    }))
    const options = [{key: '是', value: '是'},{key: '否', value: '否'}]
    let column:any = {
      options: optionList,
      type: section.titleLevel === 1 ? '' : (section.operation as any),
      name: section.code,
      level: section.titleLevel,
      title: section.name,
      remind: section.remind,
      defaultValue: section.itemValue
    }
    if(cityCode === "10740" && hospitalArr.includes(column.name || "")){
      columns.push(handleBeiJingColumn(column,form,page , "10740"))
    } else if (cityCode === "10802") {
      columns.push(handleChangChunColumn(column,form,page))
    } else if (cityCode === "10857") {
      if(column.name === 'isFullTime_dongying'){
        column.options = options
      }
      columns.push(handleDongYingColumn(column,form,page))
    } else if (cityCode === "10845") {
      if(column.name === 'payedIn_suqian'){
        column.options = options
      }
      columns.push(handleSuQianColumn(column,form,page))
    } else if (cityCode === "10854") {
      if(column.name === 'payedIn_taian'){
        column.options = options
      }
      columns.push(handleTaiAnColumn(column,form,page))
    } else if (cityCode === "10937"){
      if (column.name === 'newHukouType_shenzhen'){
        column.title = '户籍性质'
      }
      if (column.name === 'hukouType'){
        column = {}
      }
      if (['hasSSinShenzhen_shenzhen', 'hasPFinShenzhen_shenzhen'].includes(column.name!)){
        column.options = options
      }
      columns.push(handleShenZhenColumn(column,form,page))
    } else if (cityCode === "10749"){
      if (['shaanxiPersonMediIns_xian', 'otherProvMediIns_xian', 'anyCityStaffMediIns_xian', 'shanxiProvinceHasPaidPensionInsurance_xian'].includes(column.name!)){
        column.options = options
      }
      if(column.title === '紧急联系方式及婚姻状况'){
        column.title ='紧急联系方式'
      }
      columns.push(handleXIANColumn(column,form,page))
    } else if (cityCode === "10890"){
      if (column.name === "isEnterFund_longyan"){
        column.options = options
      }
      columns.push(handleLongYanColumn(column,form,page))
    } else if (cityCode === "10889"){
      if (['workExp_zhangzhou', 'isEnterFund_zhangzhou'].includes(column.name!)){
        column.options = options
      }
      columns.push(handleZhangZhouColumn(column,form,page))
    } else if (cityCode === "10763"){
      if(column.name === "graduatingStd_taiyuan"){
        column.options = options
      }
      columns.push(handleTaiYuanColumn(column,form,page))
    } else if (cityCode === "10803"){
      if(column.name === 'isEnterFund_jilin'){
        column.options = options
      }
      columns.push(handleJiLinColumn(column,form,page))
    } else if (['10764', '10768', '10771', '10769', '10767', '10761', '900000460', '10762', '10765', '900000740'].includes(cityCode)){
      if (column.name === 'graduationlatest_shanxi') {
        column.options = options
      }
      columns.push(column)
    }else{
      columns.push(column)
    }
    !isEmpty(section.subList) && handleColumns(section.subList, cityCode!, form, page, true)
  })
  return columns
}
const getColumns = (items, otherItems, cityCode, form, renderFileItem = () => null) => {
  const columns1 = handleColumns(items, cityCode, form, '1')
  const columns2 = handleColumns(otherItems, cityCode, form, '2')
  columns.push({render: () => renderFileItem()})
  // eslint-disable-next-line react-hooks/rules-of-hooks
  return useMemo(() => {
    if (cityCode === '10743') {
      const item = columns1.find((i: any) => i.name === 'tianjinUnemploymentCardNo')
      const index = columns1.indexOf(item as any)
      columns1.splice(index, 0, ...columns2)
      return columns1
    }
    const i = columns1.findIndex((ii: { name: string }) =>  ii.name === 'idCardNum')
    const i1 = columns1.findIndex((ii: { name: string }) =>  ii.name === 'validStart')
    const i2 = columns1.findIndex((ii: { name: string }) =>  ii.name === 'validEnd')
    const item1 = columns1.find((ii: { name: string }) =>  ii.name === 'validStart')
    const item2 = columns1.find((ii: { name: string }) =>  ii.name === 'validEnd')
    columns1.splice(i+1, 0, {...item1})
    columns1.splice(i+2, 0, {...item2})
    columns1.splice(i1+2, 1)
    columns1.splice(i2+1, 1)
    return [...columns1, ...columns2]
  }, [cityCode, columns1, columns2])
}

export { handleColumns, getColumns }
