import { useRef, useState } from 'react'
import { View, Image, Button, Input, Text } from '@tarojs/components'
import * as Sentry from '@sentry/react'
import Modal from '@components/modal'
import err from '@assets/error.png'
import styles from './index.module.scss'

const Error = ({ errorInfo }: any) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [visible, setVisible] = useState(false)
  // eslint-disable-next-line react-hooks/rules-of-hooks
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const contentRef = useRef<string>('')
  let errorData: any = {};
  try {
    errorData = JSON.parse(errorInfo ?? '{}')
  // eslint-disable-next-line @typescript-eslint/no-shadow
  } catch (error) {

  }
  // console.log('errorInfo---', errorData)
  return (
    <View className={styles.error}>
      <View>
        <Image src={err} className={styles.img} />
      </View>
      <View className={styles.fontsize}>{`错误编码${errorData?.errorMessage ?? ''}`}</View>
      <View className={styles.fontsize}>{errorData?.data || '数据加载失败/错误'}</View>
      <View className={styles.btnbox}>
        <Button className={styles.btn} type='warn' onClick={() => { setVisible(true) }}>
          反馈信息
        </Button>
      </View>
      <Modal
        visible={visible}
        title='提示'
        onConfirm={() => {
          const content = contentRef.current || ''
          Sentry.captureMessage("用户反馈ERROR提交的信息", {
            contexts: {
              text: {
                // name,
                content,
              },
            },
            level: Sentry.Severity.Info,
          });
          setVisible(false)
        }}
        content={<View>
          <View className={styles.view}>
            <Text>反馈内容 : </Text>
            <Input focus type='text' onInput={(e) => { contentRef.current = e.detail?.value }} className={styles.input} />
          </View>
        </View>}
      />
    </View>
  )
}

export default  Error
