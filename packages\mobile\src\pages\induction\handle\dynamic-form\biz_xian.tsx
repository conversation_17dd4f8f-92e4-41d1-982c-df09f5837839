import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleXIANColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
    const shaanxiPersonMediIns_xian = form.watch('shaanxiPersonMediIns_xian');
    const otherProvMediIns_xian = form.watch('otherProvMediIns_xian');
    const anyCityStaffMediIns_xian = form.watch('anyCityStaffMediIns_xian');
    const shanxiProvinceHasPaidPensionInsurance_xian = form.watch('shanxiProvinceHasPaidPensionInsurance_xian');
    if (column.name === 'shaanxiPersonMediIns_xian'){

        form.register('shaanxiPersonMediIns_xian', {value: column.defaultValue})
    }
    if (column.name === 'otherProvMediIns_xian'){
        form.register('otherProvMediIns_xian', {value: column.defaultValue})
    }
    if (column.name === 'anyCityStaffMediIns_xian'){
        form.register('anyCityStaffMediIns_xian', {value: column.defaultValue})
    }

    if (column.name === 'shanxiProvinceHasPaidPensionInsurance_xian'){
        form.register('shanxiProvinceHasPaidPensionInsurance_xian', {value: column.defaultValue})
    }
    if (column.name === 'shaanxiPersonMediIns_xian'){
      return {...column, remind: ['1', '是'].includes(shaanxiPersonMediIns_xian) ? column.remind : '' }
    }
    if (column.name === 'otherProvMediIns_xian'){
        return {...column, remind: ['1', '是'].includes(otherProvMediIns_xian) ? column.remind : ''}
    }
    if (column.name === 'anyCityStaffMediIns_xian'){
        return {...column, remind: ['1', '是'].includes(anyCityStaffMediIns_xian) ? column.remind : ''}
    }
    if (column.name === 'shanxiProvinceHasPaidPensionInsurance_xian'){
        return {...column, remind: ['1', '是'].includes(shanxiProvinceHasPaidPensionInsurance_xian) ? column.remind : ''}
    }
    return column;
}

export { handleXIANColumn }