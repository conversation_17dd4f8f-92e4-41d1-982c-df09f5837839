.wrap {
  position: relative;
  width: 750px;
}

.content {
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  position: absolute;
  width: 710px;
  left: 20px;
  top: 20px;
}

.bg_img {
  width: 750px;
}

.time_picker {
  background-color: #fff;
  align-items: center;
  height: 70px;
  text-align: left;
  border-radius: 8px;
  width: 280px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  padding: 0 20px;
}


.icon {
  width: 34px;
  height: 35px;
}

.medical_search {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
}
.search_line {
  width: 10px;
  height: 2px;
  margin: 0 10px;
  background-color: rgba(0, 0, 0, 0.5);
}

// 搜索按钮
.search_btn {
  background-color: #b51e25;
  height: 70px;
  width: 70px;
  line-height: 70px;
  padding: 0 20px;
  margin-left: 10px;
  border-radius: 8px;
  color: #fff;
  font-size: 26px;
}
.search_icon {
  width: 32px;
  height: 32px;
  margin-top: 20px;
}
.input {
  background-color: #ffffff;
  border-radius: 4px;
  height: 72px;
  width: 620px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
  font-size: 28px;
  color: #333;
}
.group_query {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 20px;
  padding-top: 14px;
  border: 0 dashed #fff;
  border-top-width: 2px;
}
.form_select_arrow {
  height: 12px;
  width: 20px;
  margin-left: 17px;
}
.group_btn {
  flex: 1;
  height: 58px;
  line-height: 59px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  text-align: center;
  font-size: 24px;
  padding: 0;
  margin: 0 0 0 10px;
}

.tag_wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  width: 670px;
  margin-top: 40px;
  padding-top: 14px;
  border: 0 dashed #fff;
  border-top-width: 2px;
}
.tag_wrap_list {
  width: 40%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.tag_inline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  font-size: 26px;
  color: #666666;
}
.p_t{
  padding-top: 5px;
}
.tag_btn {
  width: 150px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.5);
}

.tag_btn_active {
  @extend .tag_btn;
  background-color: #b51e25;
}

.btn_text {
  color: #333;
  font-size: 26px;
}
.btn_text_active {
  color: #fff;
  font-size: 26px;
}
.item {
  padding: 5px 10px;
}
.item_detail {
  border-width: 1px;
  border-radius: 14px;
}
.group_list {
  display: flex;
  width: 750px;
  padding: 10px;
}
.list_top_item {
  flex: 1;
  min-height: 50px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  font-size: 26px;
  color: #666666;
}
.list_top_item_title {
  font-size: 20px;
  color: #666666;
}

.list_top_item_text {
  margin-left: 10px;
  font-size: 24px;
  color: #000;
}
.list_title {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  align-items: center;
  border: 0px solid #efeff4;
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.list_title_arrow {
  height: 15px;
  width: 8px;
}
.text_title {
  font-size: 26px;
  padding-right: 10px;
}
.text{
  font-size: 26px;
  color: #666666;
}
