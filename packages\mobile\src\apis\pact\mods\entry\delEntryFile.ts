/**
 * @description 删除入职信息文件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** fileId */
  fileId: number;
}

export type Result = defs.pact.UploadEntryFileRespBean;
export const path = '/yc-wepact-mobile/entry/delEntryFile/{fileId}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
