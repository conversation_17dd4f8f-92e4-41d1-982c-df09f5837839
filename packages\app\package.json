{"name": "app", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "upgradePeerdeps": "install-peerdeps @tarojs/taro-rn -o -Y && install-peerdeps @tarojs/components-rn -o -Y && install-peerdeps @tarojs/router-rn -o -Y"}, "dependencies": {"@ant-design/react-native": "^4.2.0", "@react-native-async-storage/async-storage": "^1.13.2", "@react-native-community/cameraroll": "^4.0.4", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/geolocation": "^2.0.2", "@react-native-community/masked-view": "^0.1.10", "@react-native-community/netinfo": "^5.9.7", "@react-native-community/segmented-control": "^2.2.2", "@react-native-community/slider": "4.0.0-rc.3", "@react-native-picker/picker": "^1.16.4", "expo-av": "~8.6.0", "expo-barcode-scanner": "~9.0.0", "expo-brightness": "^8.3.0", "expo-camera": "~9.0.0", "expo-file-system": "~9.2.0", "expo-image-picker": "~9.1.0", "expo-keep-awake": "^8.3.0", "expo-location": "^9.0.1", "expo-permissions": "~9.3.0", "expo-sensors": "^9.1.0", "react": "^17.0.1", "react-native": "^0.64.0", "react-native-fast-image": "^8.3.7", "react-native-flipper": "^0.103.0", "react-native-gesture-handler": "^1.10.3", "react-native-image-resizer": "^1.4.0", "react-native-keyboard-aware-scroll-view": "^0.9.4", "react-native-linear-gradient": "^2.5.6", "react-native-pager-view": "^5.4.0", "react-native-reanimated": "^1.13.1", "react-native-safe-area-context": "^3.1.8", "react-native-screens": "^2.11.0", "react-native-svg": "^12.1.1", "react-native-syan-image-picker": "0.4.10", "react-native-unimodules": "^0.11.0", "react-native-webview": "^11.6.4", "yarn": "^1.22.11"}, "jest": {"preset": "react-native"}}