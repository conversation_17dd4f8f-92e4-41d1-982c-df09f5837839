import { useEffect, useRef, useState } from 'react'
import { View, Image } from '@tarojs/components'
import clockInBgImg from '@assets/attendance/clockIn_bg.png'
import Location from '@assets/attendance/location.png'
import WarnImg from '@assets/attendance/warn.png'
import Taro from '@tarojs/taro'
import styles from './index.module.scss'
import { MultiMarker, TMap as QQMap } from 'tlbs-map-react'
import { AtIcon, AtTextarea } from 'taro-ui'
import * as imageConversion from 'image-conversion'
import { formatDate } from '@utils/common'
import { uploadFile } from '../inside'
import { getGlobalData } from '@utils/global-data'
import { ncmp } from '@apis/ncmp'

//统一使用eos map key
const mapKey = 'UQRBZ-KSBLN-6WDFH-SSHXW-42C76-63BEG'//'DGOBZ-ON43V-OVQPM-52LF3-A5PP3-SLBEC'

const qqMap = (): Promise<any> => {
  return new Promise(function (resolve, reject) {
    var script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = `https://map.qq.com/api/gljs?v=1.exp&libraries=service&key=${mapKey}`
    script.onerror = (err) => reject(err)
    script.onload = (e) => {
      resolve(e)
    }
    document.head.appendChild(script)
  })
}

interface OutsideProps {
  custId: string | undefined
  position: Record<'lat' | 'lng', string | number>
}

const Index = ({ custId, position }: OutsideProps) => {
  const mapRef = useRef(null)
  const markerRef = useRef(null)

  // const markStyles = {
  //   multiMarkerStyle: {
  //     width: 20,
  //     height: 30,
  //     anchor: { x: 10, y: 30 },
  //     color: '#D54440',
  //   },
  // }

  const [geometries, setGeometries] = useState<any>([
    {
      // styleId: 'multiMarkerStyle',
      position: { lat: 40.0404, lng: 116.2735 },
    },
  ])

  const [center, setCenter] = useState<any>(position)

  const [imgList, setImgList] = useState<any[]>([])

  const [picUrl, setPicUrl] = useState<string[]>([])

  const [isShowMap, setIsShowMap] = useState<boolean>(false)

  const currentDate = new Date()

  const [currentTime, setCurrentTime] = useState<Date>(currentDate)

  const [currentAddress, setCurrentAddress] = useState<string>('')

  const [deviceInfo, setDeviceInfo] = useState<string>('')

  const [checkRemark, setCheckRemark] = useState<string>('')

  const [hasCheckFieldList, setHasCheckFieldList] = useState<boolean>(false)

  var timer: any = null

  const getSystemDate = async () => {
    //获取服务器时间
    const res: any = await ncmp.eosCheckIn.getSysTime.request({})
    setCurrentTime(new Date(res?.resultObj?.replace(/-/g, '/')))
  }

  const getSystemInfo = async () => {
    const systemInfo = getGlobalData<'systemInfo'>('systemInfo')
    setDeviceInfo(systemInfo?.model)
  }

  const reverseGeocoder = async () => {
    setGeometries((pre) => [{ ...pre, position: { ...position } }])

    setCenter(position)

    setIsShowMap(true)

    qqMap().then(() => {
      const geocoder = new TMap.service.Geocoder()
      console.log('reverseGeocoder', position)
      geocoder.getAddress({ location: new TMap.LatLng(position.lat, position.lng) }).then((res: any) => {
        const { formatted_addresses, address } = res?.result
        setCurrentAddress(address + formatted_addresses?.recommend)
      })
    })
  }

  const setTimer = () => {
    //清空计时器
    timer && clearInterval(timer)

    timer = setInterval(() => {
      setCurrentTime((pre) => {
        return new Date(pre.getTime() + 1000)
      })
    }, 1000)
  }

  const removeImg = (index: number) => {
    setImgList((pre) => pre.filter((_, i) => i !== index))
    setPicUrl((pre) => pre.filter((_, i) => i !== index))
  }

  const uploadImage = () => {
    if (imgList?.length >= 3) {
      Taro.showToast({
        title: '最多上传3张图片',
        icon: 'none',
      })
      return
    }

    Taro.chooseImage({
      count: 3,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        console.log('upload', res)
        setImgList((pre) => [...pre, ...res?.tempFilePaths])
        res?.tempFiles?.forEach((item, i) => {
          const file: any = item.originalFileObj
          // 比例 scale
          imageConversion.compressAccurately(file, { size: 300, width: 750 }).then((data) => {
            uploadFile(
              { url: '/wx-ncmp/eos/attendance/checkin/uploadFile', count: res?.tempFiles?.length, currentIndex: i },
              data,
              (filePath) => {
                setPicUrl((pre) => [...pre, filePath])
              }
            )
          })
        })
      },
    })
  }

  const onClockIn = async () => {
    Taro.showLoading()

    const { lat, lng } = position

    const res: any = await ncmp.eosCheckIn.saveCheckField.request({
      custId: Number(custId),
      latitude: lat,
      longitude: lng,
      checkTime: formatDate(currentTime, 'yyyy-MM-dd HH:mm:ss'),
      deviceDes: deviceInfo,
      checkAdd: currentAddress,
      deviceId: deviceInfo,
      checkRemark: checkRemark,
      picUrl: picUrl?.join(','),
    })

    Taro.hideLoading()

    let redrictUrl = '/pages/attendance/clockin/fail/index?endtime='
    console.log(res,"saveCheck")
    if (res?.resultObj === 1) {
      redrictUrl = '/pages/attendance/clockin/success/index?endtime='
    }
    console.log(redrictUrl,"redrictUrl");

    Taro.navigateTo({
      url: redrictUrl + formatDate(currentTime, 'HH:mm:ss'),
    })
  }

  const getCheckFieldList = async () => {
    const res = await ncmp.eosCheckIn.getCheckFieldList.request({
      custId: Number(custId),
      date: formatDate(currentTime, 'yyyy-MM-dd'),
    })
    setHasCheckFieldList(res?.resultObj?.length > 0)
  }

  useEffect(() => {
    if (position.lat === '') return

    setPicUrl([])
    setImgList([])

    getSystemDate()
    setTimer()
    getSystemInfo()
    getCheckFieldList()

    reverseGeocoder()
  }, [position])

  return (
    <div className={styles.container}>
      {position.lat === '' ? (
        <div className={styles.error_wrapper}>
          <div className={styles.error_tip}>
            <img src={WarnImg} className={styles.img} />
            <div className={styles.error_info}>无法定位，请先开启定位</div>
          </div>
        </div>
      ) : (
        isShowMap && (
          <>
            <div className={styles.wrapper}>
              <div className={styles.tip_wrap}>
                <div className={styles.tip}>
                  <img src={Location} />
                  {currentAddress}
                </div>
              </div>
              <div className={styles.content}>
                <QQMap ref={mapRef} apiKey={mapKey} options={{ zoom: 17, center: center }}>
                  <MultiMarker ref={markerRef} geometries={geometries} />
                  {/* <InfoWindow position={currentLocation} content={currentAddress} /> */}
                </QQMap>
              </div>
            </div>
            <div className={styles.textarea}>
              <AtTextarea
                placeholder='请输入备注'
                maxLength={200}
                className={styles.input}
                value=''
                onChange={(e) => setCheckRemark(e)}
              />
              <div className={styles.uploader}>
                {imgList?.map((item, index) => {
                  return (
                    <View className={styles.img_wrap} key={item?.path}>
                      <Image
                        className={styles.img}
                        src={item}
                        onClick={() => {
                          Taro.previewImage({
                            current: item,
                            urls: imgList,
                          })
                        }}
                      />
                      <View className={styles.del}>
                        <AtIcon
                          className={styles.icon}
                          value='close-circle'
                          size={16}
                          onClick={() => removeImg(index)}
                        ></AtIcon>
                      </View>
                    </View>
                  )
                })}
                {imgList?.length < 3 && (
                  <View style={{ marginLeft: '20px' }}>
                    <AtIcon value='camera' size='36' onClick={() => uploadImage()}></AtIcon>
                  </View>
                )}
              </div>
            </div>
            <div className={styles.bottom}>
              <div className={styles.clockIn_btn} onClick={onClockIn}>
                <img src={clockInBgImg} />
                <div className={styles.btn}>
                  <div className={styles.text}>{hasCheckFieldList ? '再次打卡' : '外勤打卡'}</div>
                  <div className={styles.time}>{formatDate(currentTime, 'HH:mm:ss')}</div>
                </div>
              </div>
            </div>
          </>
        )
      )}
    </div>
  )
}

export default Index
