/**
 * @description sendTemplateMsg1
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.users.ResponseEntity;
export const path = '/user-server/api/template-msg/send/salaryPaymentFailed';
export const method = 'POST';
export const request = (
  data: defs.users.TemplateMsg,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.users.TemplateMsg,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
