.item_wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-left: 6px;
}
.file_item {
  width: 210px;
  height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #efeff4;
  margin: 15px;
}
.title {
  color: #333;
  font-size: 24px;
}
.img_wrap {
  width: 160px;
  height: 160px;
  border: 1px dashed #efeff4;
  // display: flex;
  // justify-content: center;
  // align-items: center;
  // position: relative;
  //z-index: 99;
}
.img {
  width: 150px;
  height: 150px;
}
:global {
  .taro-textarea{
    background-color: #fff;
    color: #333;
  }
}

