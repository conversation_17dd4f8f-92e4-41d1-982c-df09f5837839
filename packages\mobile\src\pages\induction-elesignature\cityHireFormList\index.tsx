import { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import { View, Text, Button, Image } from '@tarojs/components'
import { withPage } from '@components'
import { ncmp } from '@apis/ncmp'
import no_data from '@assets/no-data.png'
import styles from './index.module.scss'

const Index = () => {
  const [hireFormList, setHireFormList] = useState<defs.ncmp.WechatHiresepDTO[] | unknown[]>([])
  useEffect(() => {
    ncmp.hiresep.listCityHireForm
      .request({}, { isToken: true })
      .then((res: any) => {
        if (res.code === '200') {
          Array.isArray(res?.resultObj) && setHireFormList(res?.resultObj || [])
        } else {
          Taro.showModal({
            title: '提示',
            content: res.errorMsg || '系统异常!',
            showCancel: false
          })
        }
      })
      .catch(() => {
        const title = '系统异常！'
        Taro.showToast({ title, icon: 'none' })
      })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const tolowcodeFormPage = (item: any) => {
    Taro.navigateTo({
      url: `pages/induction/handle/lowcode-form/index?cityCode=${item?.cityId}&mainId=${item?.empHiresepMainId}&uuid=${item?.uuid}&title=入职办理查看`
    })
  }
  return (
    <View className={styles.wrap}>
      {hireFormList.length > 0 ? (
        hireFormList.map((item: any) => (
          <View
            className={styles.item}
            key={item.eleContractName}
            onClick={() => {
              tolowcodeFormPage(item)
            }}
          >
            <Text className={styles.eleContractName}>{item.cityName}</Text>
            <Button className={styles.eleContractStatus}>查看</Button>
          </View>
        ))
      ) : (
        <View className={styles.empty}>
          <Image className={styles.no_data} src={no_data} />
          <Text className={styles.no_text}>暂无可以查看的入职记录</Text>
        </View>
      )}
    </View>
  )
}

export default withPage(Index)
