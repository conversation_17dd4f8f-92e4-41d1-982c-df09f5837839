import { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import { getGlobalData } from '../global-data'

/**
 * 获取系统信息
 * @returns
 */
export const useSystemInfo = () => {
  const [systemInfo, setSystemInfo] = useState<Taro.getSystemInfo.Result>()
  useEffect(() => {
    Taro.getSystemInfo().then(res => setSystemInfo(res))
  }, [])
  return systemInfo
}

/**
 * 获取当前用户信息
 * @returns
 */
export const useAccountInfo = () => {
  const account = getGlobalData<'account'>('account')
  return account
}
