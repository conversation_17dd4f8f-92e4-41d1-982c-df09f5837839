/**
 * @description 签署电子业务材料
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<Array<defs.ncmp.EleContract>>;
export const path = '/wx-ncmp/elecbusiness/signElecBusinessMaterial';
export const method = 'POST';
export const request = (
  data: defs.ncmp.EcBusiness,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.EcBusiness,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
