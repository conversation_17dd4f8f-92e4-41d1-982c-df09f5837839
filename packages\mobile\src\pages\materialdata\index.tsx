import { useEffect, useState } from 'react'
import Taro, { useRouter } from '@tarojs/taro'
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { isEmpty } from 'lodash'
import { getGlobalData, getScrollStyle, chooseImage, useRequest } from '@utils'
import { BottomBtn, withPage, FormTip } from '@components'
import { pact } from '@apis/pact'
import RequiredImg from '@assets/icon/icon-star.png'
import IconDetail from '@assets/icon/icon-detail.png'
import AddImg from '@assets/getadd.png'
import delImg from '@assets/icon/icon-delete.png'
import styles from './index.module.scss'

const cityTips = {
  // '10841': '就业失业登记证:如无就失业证，请联系淮安易才',
  '10862': '需要上传1寸照片(电子档)、身份证复印件、就业失业登记证复印件',
  // '10813': '必须上传户口首页、个人页复印件及二代身份证复印件(正背面在同一A4纸)',
  '10744': '本人证件照:如无社保卡则必须上传(蓝底或红底)一寸洗印照片',
  '10959': '本人证件照:如无社保卡则必须上传(蓝底或红底)一寸洗印照片',
  '10921': '本人证件照:如无社保卡则必须上传(蓝底或红底)一寸洗印照片',
  '10740': '证件照使用电子版白底（像素358*441；大小9kb-20kb）必须上传',
  '10748': '必须上传身份证正反面复印件扫描件或正反面图片',
  '10919': '社保局要求本人证件照： 无底色要求，彩色，大小不超过1M，质量清晰',
  // '10996':
  //   '社保局要求本人证件照： 白底免冠证件照，照片大小（14-60kb）,照片尺寸（441像素（高）×358像素（宽），照片色彩（24位真色彩），头部占照片尺寸的2/3',
  '10950': '必须上传户口首页、个人页复印件及二代身份证复印件（正反面在同一A4纸上）'
}

const Index = () => {
  const [flag, setflag] = useState(false)
  const { cityCode = '', uuid } = useRouter().params
  const { openId } = getGlobalData<'account'>('account')
  const { result = [], run } = useRequest(`/yc-wepact-mobile/dynamics-form/file/${cityCode}/${openId}`, { uuid })
  let imgsData: any = result
  useEffect(() => {
    const index = imgsData.findIndex(i => i.itemCode === 'identificationCard')
    if (index !== -1) {
      imgsData.splice(index, 1)
    }
    if (['10740', '10937'].includes(cityCode)) {
      imgsData.map((i:any) => {
        if ('identificationPhoto'.includes(i.itemCode)) {
          i.requireValue = true
        }
        if ('photoOfBankCard'.includes(i.itemCode)) {
          i.requireValue = false
        }
        return i
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  })
  const delEntryFile = async (item: defs.dynamicsForms.FormFileItemBean) => {
    const fileId = Number(item.data?.[0]?.fileId)
    await pact.entry.delEntryFile.request({ fileId })
    await run()
    Taro.showToast({ title: '删除成功' })
  }
  const previewImage = (url: string) => {
    Taro.previewImage({
      current: url,
      urls: [url]
    })
  }
  const uploadImage = (item: defs.dynamicsForms.FormFileItemBean) => {
    const data = {
      url: '/yc-wepact-mobile/entry/uploadEntryFileByForm',
      type: item.itemCode,
      businessId: uuid,
      cityCode
    }
    chooseImage(data, run)
  }
  const handleSumit = () => {
    const requireFlag = imgsData
      .filter(it => it.itemCode !== 'identificationCard')
      .find(item => item.requireValue && !item.data?.[0]?.fileUrl)
    setflag(requireFlag)
    if (requireFlag) {
      Taro.showToast({ title: '请上传必传资料照片', icon: 'none' })
      return
    }
    Taro.navigateTo({
      url: `/pages/induction/handle/dynamic-form/index?cityCode=${cityCode}&uuid=${uuid}&businessId=${imgsData?.[0]?.businessId}&title=员工入职办理`
    })
  }
  const scrollStyle = getScrollStyle({ bottom: 140 })
  return (
    <View className={styles.wrap}>
      <ScrollView style={scrollStyle} scrollY>
        <View className={styles.sample} onClick={() => Taro.navigateTo({ url: '/pages/sample/index?type=1' })}>
          <Image className={styles.sample_img} src={IconDetail} />
          <Text className={styles.sample_text}>查看样例</Text>
        </View>
        <View className={styles.item_wrap}>
          {imgsData.map(item => (
            <View key={item.itemCode} className={styles.item}>
              <View className={styles.header}>
                {item.requireValue && <Image className={styles.required} src={RequiredImg} />}
                <Text className={styles.title}>{item.itemName}</Text>
              </View>
              <View className={styles.img_wrap}>
                {!isEmpty(item.data) && (
                  <Image
                    className={styles.img}
                    src={item.data?.[0]?.fileUrl!}
                    onClick={() => previewImage(item.data?.[0]?.fileUrl!)}
                  />
                )}
                <View className={styles.require_cls}>
                  {item.requireValue && isEmpty(item.data) && flag && (
                    <Text className={styles.require_tips}>请填写必填项</Text>
                  )}
                </View>
                <View className={styles.del_cls}>
                  {!isEmpty(item.data) ? (
                    <Image className={styles.delImg} src={delImg} onClick={() => delEntryFile(item)} />
                  ) : (
                    <Image className={styles.add} src={AddImg} onClick={() => uploadImage(item)} />
                  )}
                </View>
              </View>
            </View>
          ))}
        </View>
        <FormTip tip='请每次上传前，选择在明亮无阴影处，垂直拍照后上传，保留证件半圆边角' />
        {cityTips[cityCode] ? <FormTip tip={cityTips[cityCode]} /> : null}
      </ScrollView>
      <BottomBtn btns={[{ title: '下一步', onClick: handleSumit }]} />
    </View>
  )
}

export default withPage(Index, { needSignature: true })
