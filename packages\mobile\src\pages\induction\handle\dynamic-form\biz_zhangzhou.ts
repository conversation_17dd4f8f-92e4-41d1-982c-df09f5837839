import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleZhangZhouColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const isFirstSecurity = form.watch('isEnterFund_zhangzhou')
  const workExp_zhangzhou = form.watch('workExp_zhangzhou')
  if (column.name === 'workExp_zhangzhou'){
    form.register('workExp_zhangzhou', {value: ['1', '是'].includes(column.defaultValue) ? '是' : '否'})
}
if (column.name === 'isEnterFund_zhangzhou'){
    form.register('isEnterFund_zhangzhou', {value: ['1', '是'].includes(column.defaultValue) ? '是' : '否'})
}
  if(column.name === 'isEnterFund_zhangzhou') {
    return { ...column, remind: ['1', '是'].includes(isFirstSecurity) && column.remind}
  }
  if (column.name === 'pfundAccount_zhangzhou') {
    return { ...column, rules:{required: true}, isHidden: !['1', '是'].includes(isFirstSecurity)}
  }

  if (column.name === 'workCompany_zhangzhou'){
    return { ...column, isHidden: !['1', '是'].includes(workExp_zhangzhou)}
  }

  if (column.name === 'workBeginTime_zhangzhou'){
    return { ...column, isHidden: !['1', '是'].includes(workExp_zhangzhou)}
  }

  if (column.name === 'workEndTime_zhangzhou'){
    return { ...column, isHidden: !['1', '是'].includes(workExp_zhangzhou)}
  } 

  return column;
}

export { handleZhangZhouColumn }