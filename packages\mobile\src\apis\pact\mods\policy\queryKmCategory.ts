/**
 * @description 知识类别查询接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** parentId */
  parentId?: string;
}

export type Result = defs.pact.QueryKmCategoryBean;
export const path = '/yc-wepact-mobile/policy/queryKmCategory';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
