/*
 * @Author: your name
 * @Date: 2021-08-11 17:30:19
 * @LastEditTime: 2021-09-03 16:02:58
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \wehr_web2\packages\mobile\plugins\router.ts
 */
/* eslint-disable import/no-commonjs */
import type { IPluginContext } from '@tarojs/service'
import { fs } from '@tarojs/helper'

var  oldTemplate = ''
const path = require('path');

const CONFIG_PATH = path.resolve(__dirname, '..', 'config.js')
// const pageList = [];
const originPath = path.resolve(__dirname, '..', '..', 'src/pages')
const INDEX_PAGE_PREFIX = 'index'
const INDEX_FILE_SUFFIX = ['.ts', '.js', '.tsx', '.jsx']
const INDEX_PAGE = options => {
  const suffixOption = options?.route?.suffix || []
  const suffix = INDEX_FILE_SUFFIX.concat(suffixOption)
  const prefix = options?.route?.prefix || INDEX_PAGE_PREFIX
  return suffix.map(fileSuffix => `${prefix}${fileSuffix}`)
}
const createRoute = (innerPath: string, options) => {
  const rootPageName = options?.route?.rootPageName || 'pages'
  const filePath = innerPath.replace(/\\/g, '/')
  // const suffixOption = options.suffix || [];
  // const suffix = INDEX_FILE_SUFFIX.concat(suffixOption);
  const startIndexOf = filePath.indexOf(rootPageName)
  const endIndexOf = filePath.lastIndexOf('.')
  return filePath.slice(startIndexOf, endIndexOf)
  // const returnPath = filePath.slice(indexOf);
  // pageList.push(returnPath);
}
const isFile = inputPath => fs.statSync(inputPath).isFile()
const isIndexPage = (inputPath, fileName, options) =>
  isFile(path.resolve(inputPath)) && INDEX_PAGE(options).includes(fileName)

const getDir = (pathArg, options, pageList: string[] = []) => {
  const inputPath = options?.route?.inputPath || pathArg
  try {
    const fileDirList = fs.readdirSync(inputPath)
    fileDirList.forEach(fileDir => {
      //内部路由
      const innerPath = path.resolve(inputPath, `${fileDir}`)
      if (isIndexPage(innerPath, fileDir, options)) {
        //
        const returnPath = createRoute(innerPath, options)
        pageList.push(returnPath)
      } else {
        getDir(innerPath, options, pageList)
      }
    })
  } catch (error) { }
  return pageList
}

const createConfigFile = (options, pageList) => {
  const tsHintPath = options?.route?.tsHintPath || path.resolve(__dirname, '..', '..', `${`route.d.ts`}`)
  const outputPath = path.resolve(__dirname, '..', '..', `src/${options?.route?.configFile || `app.config.ts`}`)
  const indexPage = options?.route?.indexPage
  if (pageList && pageList.length) {
    //可能匹配中多个
    let indexPath: string[] = []
    const list = pageList?.filter(page => {
      if (page.includes(indexPage)) {
        indexPath.push(page)
      }
      return !page.includes(indexPage)
    })
    if (indexPage) {
      indexPath?.forEach(pathName => {
        list.unshift(pathName)
      })
    }
    const pages = `
    export const pages = ${JSON.stringify(list)};
    `
    try {
      const { taroConfig } = require(CONFIG_PATH)
      const template = `${pages}
      export default {
        pages,
        ${configStr(taroConfig)}
      };`
      if(oldTemplate === template) return;
      oldTemplate = template;
      const tsHintTemplate = `
      declare type navigateToMiniCommonOption = Omit<Taro.navigateToMiniProgram.Option, "path">;
      declare type navigateToCommonOption = Omit<Taro.navigateTo.Option, "url">;
      declare type navigateToCommonOptionUnit = navigateToMiniCommonOption & navigateToCommonOption
      declare interface navigateToOption extends navigateToCommonOptionUnit {
        query?: {
          [key: string]: string|number
        }
      }
      declare type routeKey = ${createKey(list, options)}
      declare function navigateTo(key: routeKey,options: navigateToOption): Promise<Taro.General.CallbackResult>`
      fs.writeFileSync(tsHintPath, tsHintTemplate)
      //判断是否生成app.config&&pages有没有变化&&config有无变化
      // if (fs.existsSync(outputPath)) {
      //   const { default: { pages: pageData, ...resetConfig } } = require(outputPath)
      //   if (JSON.stringify(pageData) === JSON.stringify(list) && configStr(taroConfig) === configStr(resetConfig)) return;
      // }
      fs.writeFileSync(outputPath, template)

    } catch (error) {

    }
  }
}
const createKey = (list: string[], options?: any) => {
  let key = '';
  list.map(str => str.replace(new RegExp('^' + (options?.route?.rootPageName || 'pages')), '').replace(new RegExp("." + options?.route?.prefix || INDEX_PAGE_PREFIX + '$'), '')).forEach(str => {
    key += `"${str}"|`
  })
  return key.slice(0, -1)
}
const configStr = (config = {} || []) => {
  let str = ''
  if (Array.isArray(config)) {
    config.forEach(obj => {
      for (let key in obj) {
        str += `${key}:${JSON.stringify(obj[key])},`
      }
    })
  } else {
    for (let key in config) {
      str += `${key}:${JSON.stringify(config[key])},`
    }
  }
  return str
}
const routeConfig = options => {
  const pageList = getDir(originPath, options, [])
  createConfigFile(options, pageList)
}

// const createPageMap = page =>
//   page.map((v, i) => {
//     if (i === 0) return "[" + v;
//     if (i === page.length - 1) return v + "]";
//     return v;
//   });
// const initRouteConfig = options => {
//   const outputPath = options.output || path.resolve(__dirname, "config.js");
//   if (!fs.existsSync(outputPath)) {
//     fs.mkdirSync(outputPath);
//   }
//   const initPage =
//     options.initPage || ["home"].map(key => `pages/${key}/index`);
//   const template = `
//   module.exports = {
//     pages:${JSON.stringify(initPage)}
//   };
//   `;
//   console.log("🚀 ~ file: router.js ~ line 56 ~ template", template);
//   fs.writeFile(outputPath, template, err =>
//     console.log(`文件写入失败,path:${outputPath},template:${template}`)
//   );
// };
module.exports = (ctx: IPluginContext, options) => {
  // plugin 主体  options自己配置的
  ctx.onBuildStart(() => {
    console.log('编译开始！')
    routeConfig(options)
    // getDir(originPath, options);
  })
  ctx.modifyWebpackChain(args => {
    // console.log("钩子", args);
  })
  ctx.modifyBuildAssets(args => {
    // console.log('钩子2===>修改编译后的结果,', '当前项目输出代码路径:', ctx.paths.outputPath)
    // createConfigFile(options);
    routeConfig(options)
  })
  ctx.onBuildFinish(() => { })
}
module.exports.default = module.exports;
