import CustomTabbar from './tabbar/index'
import clockInImg from '@assets/attendance/location.png'
import clockInImgActive from '@assets/attendance/location_active.png'
import editImg from '@assets/attendance/edit.png'
import leaveImg from '@assets/attendance/leave.png'
import leaveImgActive from '@assets/attendance/leave-active.png'
import Inside from './inside'
import Outside from './outside'
import { withPage } from '@components/index'
import { AtTabs, AtTabsPane } from 'taro-ui'
import { useEffect, useState } from 'react'
import { useRouter } from '@tarojs/taro'
import { WxConfig } from '@utils/jssdk'

function Index() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [inSideVisible, setInSideVisible] = useState(true)
  const [outSideVisible, setOutSideVisible] = useState(false)

  const { custId } = useRouter().params

  const [position, setPosition] = useState({
    lat: '',
    lng: '',
  })

  const onChange = (value) => {
    setCurrentIndex(value)
    if (value === 0) {
      setInSideVisible(true)
      setOutSideVisible(false)
    } else {
      setInSideVisible(false)
      setOutSideVisible(true)
    }
  }

  const getLocation = async () => {
    WxConfig().then(() => {
      //这里必须加延迟，解决config未执行完，getLocation报错问题
      // setTimeout(() => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          console.log('index', res)
          setPosition({
            lat: res?.latitude,
            lng: res?.longitude,
          })
        },
        fail: (err) => {
          console.log('location error-------', err)
        },
      })
    })
    // }, 8)
  }

  useEffect(() => {
    getLocation()
  }, [])

  return (
    <>
      <AtTabs current={currentIndex} tabList={[{ title: '考勤' }, { title: '外勤' }]} onClick={onChange}>
        <AtTabsPane current={currentIndex} index={0}>
          <Inside custId={custId} position={position} />
        </AtTabsPane>
        <AtTabsPane current={currentIndex} index={1}>
          <Outside custId={custId} position={position} />
        </AtTabsPane>
      </AtTabs>
      <CustomTabbar
        list={[
          {
            id: 1,
            title: '打卡',
            imgSrc: clockInImg,
            imgSrcActive: clockInImgActive,
            path: `/attendance/clockin?custId=${custId}`,
          },
          {
            id: 2,
            title: '补签',
            imgSrc: editImg,
            path: `/attendance/clockin/supplementary?custId=${custId}`,
          },
          {
            id: 3,
            title: '请假',
            imgSrc: leaveImg,
            imgSrcActive: leaveImgActive,
            path: `/pages/attendance/leave/index?custId=${custId}`,
          },
        ]}
      />
    </>
  )
}

export default withPage(Index)
