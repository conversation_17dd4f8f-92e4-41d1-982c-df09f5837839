/**
 * @description 根据cityCode获取最新的模板
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** accountId */
  accountId: string;
  /** templateJsonId */
  templateJsonId: string;
}

export type Result = defs.pact.RespWrapper<defs.pact.ClcTemplateInfo>;
export const path = '/yc-wepact-mobile/clc/fetchTemplateByJsonId';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
