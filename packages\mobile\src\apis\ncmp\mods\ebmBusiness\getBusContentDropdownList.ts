/**
 * @description 业务内容下拉列表
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** busnameClassId */
  busnameClassId?: string;
  /** categoryId */
  categoryId?: string;
  /** cityId */
  cityId?: string;
}

export type Result = defs.ncmp.HttpResult<
  Array<defs.ncmp.EbmBusinessCityConfigDTO>
>;
export const path = '/wx-ncmp/ebmbusiness/getBusContentDropdownList';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
