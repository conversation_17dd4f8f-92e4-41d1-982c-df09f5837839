

.wrap {
  position: relative;
  width: 750px;
  // left: 0;
  // top: 0;
  // right: 0;
}

.content {
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  position: absolute;
  width: 710px;
  left: 20px;
  top: 20px;
}

.bg_img {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  width: 750px;
}


.time_picker {
  background-color: #fff;
  align-items: center;
  height: 70px;
  text-align: left;
  border-radius: 8px;
  width: 280px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  padding: 0 20px;
}

.text {
  color: #999;
  font-size: 26px;
}

.icon {
  width: 34px;
  height: 34px;
}

.date_range_picker {
  text-align: center;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
}
.search_line {
  width: 10px;
  height: 2px;
  margin: 0 10px;
  background-color: rgba(0, 0, 0, 0.5);
}

// 搜索按钮
.search_btn {
  background-color: #b51e25;
  height: 70px;
  width: 70px;
  line-height: 70px;
  padding: 0 20px;
  margin-left: 10px;
  border-radius: 8px;
  color: #fff;
  font-size: 26px;
}
.search_icon {
  width: 32px;
  height: 32px;
  margin-top: 20px;
}

.group_query {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 20px;
  padding-top: 14px;
  border: 0 dashed #fff;
  border-top-width: 2px;
}

.group_btn {
  flex: 1;
  height: 58px;
  line-height: 59px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  text-align: center;
  font-size: 24px;
  padding: 0;
  margin: 0 0 0 10px;
}

.tag_wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  width: 670px;
  margin-top: 20px;
  padding-top: 14px;
  border: 0 dashed #fff;
  border-top-width: 2px;
}
.tag_btn{
   width: 150px;
   height: 60px;
   display: flex;
   justify-content: center;
   align-items: center;
   border-radius: 8px;
   background-color: rgba(255, 255, 255, 0.5);
}

.tag_btn_active {
  @extend .tag_btn;
  background-color: #b51e25;
}

.btn_text {
  color: #333;
  font-size: 26px;
}
.btn_text_active {
  color: #fff;
  font-size: 26px;
}

.group_list {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 750px;
}
.list_top_item {
  flex: 1;
  min-height: 50px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
}
.list_top_item_title {
  font-size: 26px;
  color: #666666;
}

.list_top_item_text {
  margin-left: 10px;
  font-size: 28px;
  color: #000;
}
