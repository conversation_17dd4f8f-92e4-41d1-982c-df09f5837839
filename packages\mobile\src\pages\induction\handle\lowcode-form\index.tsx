import { withPage } from '@components/page'
import { View } from '@tarojs/components'
import { useRouter } from '@tarojs/taro'
import { getGlobalData } from '@utils/global-data'
import { useEffect, useRef, useState } from 'react'
import { useDidShow } from '@tarojs/runtime'
import { BaseUrl, useRequest } from '@utils/index'
import pickBy from 'lodash/pickBy'
import { isEmpty } from 'lodash'
import { pact } from '@apis/pact'
import { ncmp } from '@apis/ncmp'
import styles from './index.module.scss'




const Index = () => {
  const [templateData, setTemplateData] = useState<any>({})
  const { title, cityCode = '', uuid, mainId = '' } = useRouter().params
  const { openId, accountId, empId } = getGlobalData<'account'>('account') || {}
  const PlatformInfo = sessionStorage.getItem('PlatformInfo')
  const globalToken = PlatformInfo && JSON.parse(PlatformInfo).globalToken
  const openid = openId || PlatformInfo && JSON.parse(PlatformInfo).openId
  const iframeRef = useRef<any>(null)
  const { result = [], run } = useRequest(`/yc-wepact-mobile/dynamics-form/file/${cityCode}/${openId}`, { uuid })
  useEffect(() => {
    title && Taro.setNavigationBarTitle({ title })
  }, [title])
  useDidShow(() => {
    try {
      pact.lowcode.fetchDataAndTemplate.request({cityCode, mainId, openId: openid}).then(res => {
        if (res.code === 200) {
          setTemplateData(res.data)
        }  
      })
    } catch (error) {
      Taro.showToast({ title: '获取模板数据异常!', icon: 'none' })
    }
  })
  useEffect(() => {
    try {
      const contentWindow = (iframeRef.current?.contentWindow as any) || {}
      const _values = {...templateData, templateJson: templateData?.clcTemplateInfo?.templateJson, openId, accountId, businessId: uuid, wxglobaltoken: globalToken};
      contentWindow.MobileLoad = () => {
        if ((contentWindow as any).setWXLowcodeInitData && templateData) {
          console.log('templateData', _values);
          (contentWindow as any).setWXLowcodeInitData({
            action: 'MobileForm',
            baseUrl: BaseUrl,
            cityCode,
            imgsData: result,
            isPreview: !mainId ? false : true,
            values: _values,
            onSave: (values: any) => {
              handleSubmitFormData(values)
            },
            onCheckSample: () => Taro.navigateTo({ url: '/pages/sample/index?type=1' }),
            onCancel: () => {}
          })
        }
      }
    } catch (error) {
      Taro.showToast({ title: '系统异常!', icon: 'none' })
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [iframeRef.current, templateData])
  const handleSubmitFormData = async (values: any) => {
    const formContent = JSON.stringify({
      ...pickBy({
        ...values,
        accountId,
        cityId: cityCode,
        uuid
      })
    })
    await pact.entry.insertEntryInfo
      .request({
        cityCode,
        openid: openId,
        businessId: uuid,
        formContent: formContent
      })
      .then(res => {
        if (res.code === '1') {
          getSignEleContract('2')
        } else {
          Taro.showToast({ title: res.message || '系统异常, 请稍后重试', icon: 'none' })
        }
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  }
  const getSignEleContract = async (eleContractStatus: string) => {
    await ncmp.elecSign.getSignEleContract
      .request(
        {
          empId,
          eleContractStatus
        },
        { isToken: true }
      )
      .then((res: any) => {
        if (res.code === '200') {
          if (res?.resultObj && res?.resultObj?.eleSinUrl) {
            window.location.href = res?.resultObj?.eleSinUrl || ''
            return
          }
          Taro.navigateTo({ url: '/pages/induction/handle/success/index' })
        }
      })
  }
  return (
    <View className={styles.wrap}>
      {!isEmpty(templateData) && (
        <iframe
          ref={iframeRef}
          src='/lowcode_admin/rpa/ss-template/mobile'
          width='100%'
          title='HireTemplate'
          style={{
            border: 'none',
            width: '100%',
            height: '100vh'
          }}
          sandbox='allow-same-origin allow-scripts'
        />
      )}
    </View>
  )
}

export default withPage(Index)
