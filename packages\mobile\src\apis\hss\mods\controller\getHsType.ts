/**
 * @description 获取人员汇算类型及客户
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.hss.GeneralRespBean<defs.hss.HsAuth>;
export const path = '/yc-hs/api/getHsType';
export const method = 'POST';
export const request = (data: object, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: object,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
