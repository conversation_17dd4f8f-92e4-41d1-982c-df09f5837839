/**
 * @description 身份绑定验证
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.AuthResponseBean;
export const path = '/yc-wepact-mobile/auth/authenticationBinding';
export const method = 'POST';
export const request = (
  data: defs.pact.AuthParamBean,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.AuthParamBean,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
