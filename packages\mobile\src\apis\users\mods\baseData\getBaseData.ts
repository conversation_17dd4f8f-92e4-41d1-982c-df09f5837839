/**
 * @description getBaseData
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.users.ExecuteResult<Array<defs.users.BaseData>>;
export const path = '/user-server/api/getBaseData';
export const method = 'POST';
export const request = (
  data: defs.users.ReceiveBaseData,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.users.ReceiveBaseData,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
