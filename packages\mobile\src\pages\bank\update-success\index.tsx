/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-06 16:03:41
 * @LastAuthor: 王正荣
 * @LastTime: 2021-11-26 17:20:45
 * @message:
 */
import { View, Image, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import success from '@assets/bankCard/bank-success.png'
import { withPage } from '@components/page'
import { users } from '@apis/users'
import { getGlobalData } from '@utils/global-data'
import styles from './index.module.scss'

const close = () => {
  const { empId, openId: cmpToken } = getGlobalData<'account'>('account')
    users.getBankcardInfo.getBankcardInfo
      .request({ empId, cmpToken })
      .then(res => {
        if (res.code === 50020 && res.data?.[0]?.bankAcct) {
          Taro.navigateTo({ url: '/pages/bank/list/index' })
        } else {
          Taro.navigateTo({ url: '/pages/bank/empty/index' })
        }
      })
}

const Index = () => {
  return (
    <View className={styles.wrap}>
      <View className={styles.box_ticket_item}>
        <View className={styles.update}>
          <View className={styles.sun_btn_inner}>
            <Image className={styles.imgcss} src={success} />
          </View>
          <View className={styles.centercss}>银行卡更新成功！</View>
        </View>
      </View>
      <View className={styles.butcss}>
        <Button className={styles.btncsss} onClick={close}>
          <View className={styles.sun_btn}>关闭</View>
        </Button>
      </View>
    </View>
  )
}

export default withPage(Index)
