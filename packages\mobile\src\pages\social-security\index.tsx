/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-08-17 10:11:12
 * @LastAuthor: 王正荣
 * @LastTime: 2021-10-14 14:23:22
 * @message:
 */
import { View, Text,Image } from "@tarojs/components";
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import icon1 from "@assets/icon1.png";
import icon2 from "@assets/icon2.png";
import icon3 from "@assets/icon3.png";
import icon4 from "@assets/icon4.png";
import { useEffect } from "react";
import { withPage } from "@components";
import styles from "./index.module.scss";

const list1 = [
  {
    title: '社保缴纳明细查询',
    img: icon1,
    onClick: () => Taro.navigateTo({ url: '/pages/social-security/list/index?type=social-security' }),
  },
  {
    title: '社保缴纳进度查询',
    img: icon2,
    onClick: () => Taro.navigateTo({ url: '/pages/social-security/process/index?type=social-security' }),
  }
]

const list2 = [
  {
    title: '公积金缴纳明细查询',
    img: icon3,
    onClick: () => Taro.navigateTo({ url: '/pages/social-security/list/index?type=accumulation' }),
  },
  {
    title: '公积金缴纳进度查询',
    img: icon4,
    onClick: () => Taro.navigateTo({ url: '/pages/social-security/process/index?type=accumulation' }),
  }
]

const Index = () => {
  const { type } = useRouter<{ type: 'social-security' | 'accumulation' }>().params
  const list = type === 'social-security' ? list1 : list2

  useDidShow(() => {
    // console.log('设置标配提-1');
    Taro.setNavigationBarTitle({ title: type === 'social-security' ? '社保缴纳明细及进度' : '公积金缴纳明细及进度' })
  })

  return (
    <View className={styles.wrap}>
      {list.map((item) => (
        <View className={styles.item} key={item.title} onClick={item.onClick}>
          <Image className={styles.img} src={item.img} />
          <Text className={styles.text}>{item.title}</Text>
        </View>
      ))}
    </View>
  )
}

export default withPage(Index);
