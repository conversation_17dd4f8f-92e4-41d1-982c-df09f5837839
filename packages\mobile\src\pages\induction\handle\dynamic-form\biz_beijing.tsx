import { FormItemProps } from '@components'
import Taro from '@tarojs/taro'
import { UseFormReturn } from 'react-hook-form'
import { navigateTo } from '@utils'
import { View, Text, Image } from '@tarojs/components'
import icon_arrow from '@assets/icon/icon-arrow.png'
import styles from './index.module.scss'

const arr = ['hospital1Id', 'hospital2Id', 'hospital3Id', 'hospital4Id']
export const scrollToAnchor = anchorName => {
  if (anchorName) {
    let anchorElement = document.querySelector(anchorName)
    if (anchorElement) {
      anchorElement.scrollIntoView({ block: 'start', behavior: 'smooth' })
    }
  }
}
const handleBeiJingColumn = (column: FormItemProps, form: UseFormReturn, page: string, cityCode: string) => {
  const isBeijingPaid = form.watch('isBeijingPaid')
  let flag = !isBeijingPaid || isBeijingPaid === '1'
  if (!flag) {
    if (cityCode === '10740' && column.name === 'ethnic') {
      return { ...column, id: 'scrollTop' }
    }
  }
  const { title, level, name } = column;
  if (page === '2' && arr.includes(column.name || '')) {
    if (column?.defaultValue) {
      const value = column.defaultValue.replace(/\\/g, '')
      column.defaultValue = JSON.parse(value)
    }
    return {
      ...column,
      title: column.title,
      name: column.name,
      type: 'page_choice',
      isHidden: flag,
      remind:
        column.name === 'hospital1Id'
          ? '曾在北京缴纳定点医疗机构为首次参保选择定点医疗机构；最少选2家医院，最多选4家医院（含1家社区医院）'
          : '',
      render: () => {
        const formValue = form.control?._formValues
        const itemName = form.control?._formValues[column.name || '']
        return (
          <View
            onClick={() => {
              const pages = Taro.getCurrentPages()
              const currPage = pages[pages.length - 1]
              if (currPage.path.indexOf('/handle/form?') !== -1) return //查看界面不允许修改
              navigateTo('/fixedmedical' as any, {
                title: title,
                cityId: cityCode,
                eventName: name,
                scen: level,
                keys1: formValue['hospital1Id']?.key,
                keys2: formValue['hospital2Id']?.key,
                keys3: formValue['hospital3Id']?.key,
                keys4: formValue['hospital4Id']?.key
              })
            }}
            id='scrollTop'
          >
            <View className={styles.medical_content}>
              <View className={styles.medical_label}>
                <Text className={styles.medical_title}>{title}</Text>
                <Image className={styles.item_arrow} src={icon_arrow} />
              </View>
              <View className={styles.medical_item}>
                <View className={styles.medical_fourth}>
                  <Text className={styles.medical_title}>{itemName?.name}</Text>
                </View>
                <View className={styles.item_ul}>
                  <View className={styles.item_list}>
                    {' '}
                    医院编号：<Text className={styles.medical_title}>{itemName?.code}</Text>
                  </View>
                  <View className={styles.item_list}>
                    {' '}
                    医院类别：<Text className={styles.medical_title}>{itemName?.category}</Text>
                  </View>
                  <View className={styles.item_list}>
                    {' '}
                    所属县区：<Text className={styles.medical_title}>{itemName?.distric}</Text>
                  </View>
                  <View className={styles.item_list}>
                    {' '}
                    医院等级：<Text className={styles.medical_title}>{itemName?.level}</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        )
      }
    }
  }
  if (page === '2') {
    return { ...column, isHidden: flag }
  }
  return column
}

export { handleBeiJingColumn }
