import { useState, useEffect } from 'react'
import Taro from '@tarojs/taro'
import { withPage } from '@components/page'
import { View, Text, Image, Button, CheckboxGroup, Checkbox } from '@tarojs/components'
import { users } from '@apis/users'
import png2 from '@assets/cardProcessin/gsyh.png'
import png1 from '@assets/cardProcessin/zsyh.png'
import styles from './index.module.scss'


const cityList = [
  {
    name: '招商银行',
    icon: png1,
    type: 1,
  },
  {
    name: '工商银行',
    icon: png2,
    type: 2,
  },
]

const Index = () => {
  const [isChek, setIsChek] = useState(false)
  const urlItem: any[] = useState([])
  useEffect(() => {
    users.martyrs.selectBankcard.request({}).then((res) => {
      console.log(res)
      const linkList = res.map((item) => {
        return item.bankurl
      })
      urlItem[1](linkList)
    })
  }, [])
  // useEffect(() => {
  //   var pages = Taro.getCurrentPages();
  //   var currPage = pages[pages.length - 1]; //当前页面
  //   console.log(pages)
  //   console.log(currPage)

  //     let data = currPage.data;
  //     console.log(data)
    
  // }, [])
  const handleChange = e => {
    setIsChek(e.target.value[0])
  }
  const handleGo = (type) => {
    if (!isChek) return
    if (type == 1) {
      // Taro.navigateTo({
      //   url: `/pages/web/index?url=${urlItem[0][0]}`,
      // })
      window.location.href = urlItem[0][0]
    } else {
      // Taro.navigateTo({
      //   url: `/pages/web/index?url=${urlItem[0][1]}`,
      // })
      window.location.href = urlItem[0][1]
    }
  }
  const handleService = () => {
    Taro.navigateTo({
      url: '/pages/servicenotes/index'
    })
    
    // Taro.navigateTo({
    //   url: '/pages/fixedmedical/index'
    // })
  }

  return (
    <View className={styles.wrap}>
      <View className={styles.header}>
        {cityList.map((item, index) => {
          return (
            <View className={styles.list} key={index}>
              <View className={styles.list_item}>
                <Image src={item.icon} className={styles.icon} />
                <Text>{item.name}</Text>
              </View>
              <View>
                {/* <Button className={`${styles.group_btn}${styles.btn}`}>进入</Button> */}
                <Button
                  className={[isChek ? styles['active_btn'] : styles['group_btn'], styles.btn].join(' ')}
                  onClick={() => handleGo(item.type)}
                >
                  进入
                </Button>
              </View>
            </View>
          )
        })}
      </View>
      <View className={styles.content}>
        <CheckboxGroup onChange={handleChange}>
          <Checkbox value='1' checked={isChek}></Checkbox>
        </CheckboxGroup>
        <Text className={styles.ml_20}>
          我已阅读并同意
          <Text className={styles.text_color} onClick={handleService}>
            《服务须知》
          </Text>
        </Text>
        <View className={styles.mt_20}>
          <Text className={styles.content_text}>
            <View>上海新版社保卡</View>
            <View>
              1、服务对象：据现行政策规定，仅供本市户籍以及境内参加本市社会保险的易才服务的企业雇员（不包括外籍、港澳台）使用
            </View>
            <View>
              2、用户须知：根据市信息服务中心规定，用户仅挑选一家银行申请，如选择多家银行，则可能导致申请不予通过{' '}
            </View>
            <View>3、易才本次服务不收取任何形式费用</View>
          </Text>
        </View>
        <View className={styles.mt_20}>
          <View className={styles.content_text}>
            <View>社保卡银行咨询电话</View>
            <View>工商银行：周经理 021-62678255</View>
            <View>招商银行：张经理 021-31015500-165</View>
          </View>
        </View>
      </View>
    </View>
  )
}
export default withPage(Index)
