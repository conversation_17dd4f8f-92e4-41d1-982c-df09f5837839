import { WebView, View } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import { useEffect } from 'react'
import styles from './index.module.scss'

const Index = () => {
  const { url, title } = useRouter().params
  console.log('url----', url)
  useEffect(() => {
    title && Taro.setNavigationBarTitle({ title })
  }, [title])
  return (
    <View className={styles.wrap}>
      {url && (
        <WebView
          src={url}
          className={styles.wrap}
          onMessage={e => {
            console.log('e-----', e)
          }}
        />
      )}
    </View>
  )
}

export default Index
