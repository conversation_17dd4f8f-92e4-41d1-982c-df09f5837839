/**
 * @description 个人菜单权限接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** empId */
  empId?: string;
}

export type Result = defs.pact.MenuResponseBean;
export const path = '/yc-wepact-mobile/per/getMenuPermissions';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
