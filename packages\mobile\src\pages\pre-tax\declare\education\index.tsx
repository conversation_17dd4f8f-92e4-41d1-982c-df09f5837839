/*
 * @Author: your name
 * @Date: 2021-09-01 15:48:34
 * @LastEditTime: 2021-11-23 16:24:18
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\declare\education\index.tsx
 */
/**
 * 税前抵扣申请-继续教育
 */
import { useEffect, useState } from 'react'
import { View } from '@tarojs/components'
import { Form, FormItemProps, BottomBtn, withPage ,useForm} from '@components'
import { navigateTo, getGlobalData } from '@utils'
import { useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils/transforms'
import { users } from '@apis/users'
import isEmpty from 'lodash/isEmpty'
import styles from './index.module.scss'

const educationType = [
  { key: 1, value: '学历教育' },
  { key: 0, value: '职业教育' }
]
const educationTypes = [
  { key: 0, value: '技能人员职业资格' },
  { key: 1, value: '专业技术人员职业资格' }
]
const educationStatu = [
  { key: '0', value: '大学专科' },
  { key: '1', value: '大学本科' },
  { key: '2', value: '硕士研究生' },
  { key: '3', value: '博士研究生' },
  { key: '4', value: '其他' }
]

const Index = () => {
  const { eduId = '' } = useRouter().params
  const [flag, setFlag] = useState<boolean>(true)
  const empId = getGlobalData<'account'>('account').empId
  const { type = 'create' } = useRouter<{ type: 'create' | 'update' }>().params
  const form = useForm()
  const eduType = form.watch('eduType')
  const { result } = users.baseData.getBaseData.useRequest({ pageNum: '1', typeId: '911' })
  const openBankOptions = result?.data || ([] as defs.users.BaseData[])
  const onSubmit = (values: any) => {
    // console.log('values----', values)
    const value = form.getValues()
    // console.log(eduType)
    if (eduType == 1) {
      form.reset({
        ...value,
        continuingEduType: undefined,
        certificationDate: undefined,
        certificationAuthority: undefined,
        certificationName: undefined,
        certificationNumber: undefined
      })
    } else {
      form.reset({ ...value, eduStartDate: undefined, eduEndDate: undefined, eduPhase: undefined })
    }
    // console.log(value)
    users.user.saveContinuingEdu.request(values).then(res => {
      // console.log(res)
      if (res?.code == 0) {
        // console.log('保存成功')
        if (res?.code == 0) {
          // console.log('保存成功')
          Taro.navigateTo({
            url: '/pages/pre-tax/declare/submitSuccessfully/index'
          })
        }
      }
    })
  }
  useEffect(() => {
    // console.log(eduType)
    if (eduType == undefined) {
      form.setValue('eduType', 1)
    }
    if (eduType == 0) {
      setFlag(false)
      form.setValue('deductionAmount', 3600)
    } else {
      setFlag(true)
      form.setValue('deductionAmount', 400)
    }
  }, [eduType])
  useEffect(() => {
    form.setValue('employeeId', empId)
    form.setValue('deductionMonth', '')
  }, [empId])
  useEffect(() => {
    if (eduId !== '') {
      users.user.getContinuingEduInfo
        .request({
          eduId: eduId
        })
        .then(res => {
          // console.log(res)
          !isEmpty(res?.data) && form.reset({ ...res?.data, employeeId: empId, deductionMonth: ''})
        })
    }
  }, [eduId])
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const columns: FormItemProps[] = [
    {
      name: 'eduType',
      type: 'single',
      title: '教育形式',
      options: educationType,
      rules: { required: true }
    },
    {
      name: 'deductionAmount',
      type: 'text',
      title: '抵扣税额(元)',
      // value: '400',
      disabled: true,
      rules: { required: true }
    },
    {
      isHidden: !flag,
      name: 'eduStartDate',
      type: 'date',
      title: '当前继续教育起始时间',
      rules: { required: true }
    },
    {
      isHidden: !flag,
      name: 'eduEndDate',
      type: 'date',
      title: '(预计)当前继续教育结束时间',
      rules: { required: true }
    },
    {
      isHidden: !flag,
      name: 'eduPhase',
      type: 'select',
      title: '教育阶段',
      options: educationStatu,
      rules: { required: true }
    },

    {
      isHidden: flag,
      name: 'continuingEduType',
      type: 'single',
      title: '继续教育类型',
      level:2,
      options: educationTypes,
      rules: { required: true }
    },
    {
      isHidden: flag,
      name: 'certificationDate',
      type: 'date',
      title: '发证(批准)日期',
      rules: { required: true }
    },
    {
      isHidden: flag,
      name: 'certificationName',
      type: 'text',
      title: '证书名称',
      rules: { required: true }
    },
    {
      isHidden: flag,
      name: 'certificationNumber',
      type: 'text',
      title: '证书编号',
      rules: { required: true }
    },
    {
      isHidden: flag,
      name: 'certificationAuthority',
      type: 'text',
      title: '发证机关',
      rules: { required: true }
    }
  ]
  // const columns2: FormItemProps[] = [
  //   {
  //     name: 'eduType',
  //     type: 'single',
  //     title: '教育形式',
  //     // value: 1,
  //     options: educationType,
  //     // onChange: e => {
  //     //   setFlag(false)
  //     // },
  //     rules: { required: true }
  //   },
  //   {
  //     name: 'deductionAmount',
  //     type: 'text',
  //     title: '抵扣税额(元)',
  //     value: '3600',
  //     rules: { required: true }
  //   },
  //   {
  //     name: 'continuingEduType',
  //     type: 'single',
  //     title: '继续教育类型',
  //     options: educationTypes,
  //     rules: { required: true }
  //   },
  //   {
  //     name: 'certificationDate',
  //     type: 'date',
  //     title: '发证(批准)日期',
  //     rules: { required: true }
  //   },
  //   {
  //     name: 'certificationName',
  //     type: 'text',
  //     title: '证书名称',
  //     rules: { required: true }
  //   },
  //   {
  //     name: 'certificationNumber',
  //     type: 'text',
  //     title: '证书编号',
  //     rules: { required: true }
  //   },
  //   {
  //     name: 'certificationAuthority',
  //     type: 'text',
  //     title: '发证机关',
  //     rules: { required: true }
  //   }
  // ]
  return (
    <View className={styles.wrap}>
      {/* <Form style={scrollStyle} form={form} columns={flag == true ? columns2 : columns} /> */}
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '保存',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
