.group {
  margin-top: 30px;
}

.group_margin {
  height: 20px;
  background: rgb(248, 249, 250);
}

.empty {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 182px;
  padding: 75px 0;
}

.nodata {
  width: 23px;
  height: 29px;
  margin-right: 10px;
}

.notext {
  font-size: 28px;
}
.photoBox {
  // display: block;
  flex:0 0 30%;
  width: 230px;
  height: 242px;
  padding: 0 12px 0 12px;
  background: #FFF;
  border: solid 2px #d7d7d7;
  border-radius: 8px;
  margin: 10px;
}

.boxTitle {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70px;
  font-size: 26px;
}

.img {
  width: 204px;
  height: 152px;
}

.imgIcon {
  @extend .img;
  width: 152px;
}

.boxWrap {
  width:100%;
  display: flex;
  flex-flow:row wrap;
  align-content: flex-start;
  
}

.imgWrap {
  display: flex;
  flex-direction: row;
  justify-content: center;
  border: dashed 2px #d7d7d7;
}
