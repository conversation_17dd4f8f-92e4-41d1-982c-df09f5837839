import { FunctionComponent, useEffect, useMemo } from 'react'
import { View, Text } from '@tarojs/components'
import { Controller } from 'react-hook-form'
import FormLabel from './form-label'
import FormTip from './form-tip'
import CommonItem from './common-item'
import { FormItemProps, FormProps } from './type'
import styles from './index.module.scss'

const FormItem: FunctionComponent<FormItemProps & FormProps> = props => {
  const {
    name = '',
    rules,
    id = '',
    defaultValue,
    shouldUnregister,
    form,
    level = 3,
    title,
    type,
    remind,
    render,
    titleRemind,
    showLine,
    isHidden,
    customMsg,
    inputProps,
    renderTitleRight,
    ...reset
  } = props
  useEffect(() => {
    // Item隐藏 清空对应值
    if (isHidden) {
      form?.setValue(name, '')
    }
  }, [form, isHidden, name])
  const lineStyle = useMemo(() => {
    let _showLine: boolean = false
    if (showLine !== undefined) {
      _showLine = showLine
    } else {
      if (level === 3 && (type || render)) {
        _showLine = true
      } else {
        _showLine = false
      }
    }
    return _showLine ? {} : { borderBottomWidth: 0 }
  }, [level, render, showLine, type])
  if (isHidden) {
    return null
  }

  if (!(type || render) && title) {
    return <FormLabel level={level} title={title || ''} titleRemind={titleRemind || remind} required={reset.required} renderRight={renderTitleRight} />
  } else {
    return (
      <Controller
        name={name}
        rules={rules}
        defaultValue={defaultValue}
        shouldUnregister={shouldUnregister}
        control={form?.control}
        render={({ field, fieldState: { error }, formState }) => {
          return (
           
            <View className={styles.item} style={lineStyle} id={id}>
              {title && (
                <FormLabel
                  level={level}
                  title={title || ''}
                  required={Boolean(rules?.required)}
                  renderRight={renderTitleRight}
                />
              )}
      
              <View style={{ flex: 1, width: '100%' }}>
                {remind && <FormTip tip={remind} />}
                {type && (
                  <CommonItem
                    form={form}
                    title={title}
                    level={level}
                    {...field}
                    name={name}
                    type={type}
                    isCustom={!!render}
                    formState={formState}
                    {...reset}
                    {...inputProps}
                  />
                )}
                {render && render(field)}
                {error && (
                  <View>
                    {/* 单独一行居中 */}
                    <Text className={styles.error} style={level === 2 ? { justifyContent: 'center' } : {}}>
                      {error && (error.message || (error.type === 'required' ? '请填写必填项' : customMsg))}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          )
        }}
      />
    )
  }
}

export { FormItem }
