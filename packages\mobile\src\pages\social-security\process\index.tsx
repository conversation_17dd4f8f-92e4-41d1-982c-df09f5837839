/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-10-11 15:26:32
 * @LastAuthor: 王正荣
 * @LastTime: 2021-11-26 16:44:23
 * @message:
 */
import { View, ScrollView, Image, Text } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { getGlobalData, getScrollStyle } from '@utils'
import { ncmp } from '@apis/ncmp'
import { BottomBtn, Modal, withPage } from '@components'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import no_data from '@assets/no-data.png'
import styles from './index.module.scss'

type ProcessItem = {
  createdt: string;
  statusWx: string;
  products: string;
}

type ProcessResponse = {
  code: string;
  errorMsg: string;
  resultObj: Array<ProcessItem>;
  suc: boolean;
}

const Index = () => {
  const { empId } = getGlobalData<'account'>('account')
  const { type } = useRouter<{ type: 'social-security' | 'accumulation' }>().params
  const [list, setList] = useState<Array<ProcessItem>>()
  const [noData, setNoData] = useState<boolean>()
  const [visible, setVisible] = useState(false)
  const [errMessage, setErrMessage] = useState('')
  const [hideButton, setHideButton] = useState(false)
  const scrollStyle = getScrollStyle({ bottom: 120 })

  useEffect(() => {
    ncmp.ss.getSsProcessList.request({
      // empId: '*********',
      empId,
      groupType: type === 'social-security' ? '1' : '2', // 社保公积金类型（1：社保；2：公积金）
    })
      .then((res: any) => {
        if (res.resultObj?.length > 0) {
          setList(res.resultObj)
        } else {
          setNoData(true)
        }
        if (res.errorMsg) {
          setVisible(true)
          setHideButton(true)
          setErrMessage(res.errorMsg)
        }
      })
  }, [empId, type])
  useDidShow(() => {
    Taro.setNavigationBarTitle({ title: type === 'social-security' ? '社保缴纳进度查询' : '公积金缴纳进度查询' })
  })

  return (
    <View>
      <ScrollView style={scrollStyle} scrollY>
        {!noData ? list?.map(item => (
          <View className={styles.group} key={item.createdt}>
            <View className={styles.wrap}>
              <View className={styles.date}>{item.createdt}</View>
              <View className={styles.content}>
                {item.statusWx}
                <View className={styles.products}>{item.products}</View>
              </View>
            </View>
          </View>
        )) :
          <View className={styles.empty}>
            <Image className={styles.no_data} src={no_data} />
            <Text className={styles.no_text}>暂无数据</Text>
          </View>
        }
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            hide: hideButton,
            onClick: () => {
              Taro.navigateBack({ delta: 1 })
            }
          }
        ]}
      />
      <Modal
        visible={visible}
        title='提示'
        onConfirm={() => {
          setVisible(false)
          Taro.navigateBack({ delta: 1 })
        }}
        content={errMessage}
      />
    </View>
  )
}

export default withPage(Index)
