/*
 * @Author: your name
 * @Date: 2021-08-26 15:54:25
 * @LastEditTime: 2021-10-14 15:25:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\appointment\appointmentQuery\index.tsx
 */
import { View, Text, Image, ScrollView, Icon } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { getGlobalData } from '@utils'
import Taro from '@tarojs/taro'
import { DateSelect, DragUpdataPage, Noticebar, BottomBtn } from '@components'
import { useSystemInfo } from '@utils/hooks'
import styles from './index.module.scss'

const AppointmentQuery = () => {
  const openId = getGlobalData<'account'>('account').openId
  const [pageNo, setPageNo] = useState<string>('')

  const onSelectHandle = data => {
    // console.log(data)
    const newPageNo = '1'
    setPageNo(newPageNo)
    API.pact.appoint.getAppointmentList
      .request({
        createDt: data.startDate + ',' + data.endDate,
        empId: '606485',
        openId: 'oe7kywZLjkdMH_tOZFnxWS125Vvo',
        pageNo: pageNo
      })
      .then(res => {
        // console.log(res)
        //   !isEmpty(res.data) && setStewardInfo(res.data);
      })
  }

  const systemInfo = useSystemInfo()
  const scrollStyle = IS_RN
    ? { height: systemInfo?.safeArea.height }
    : { height: `${systemInfo?.screenHeight}px`, overflow: 'auto', width: '100%' }

  const renderLable = (label: string) => {
    return (
      <View className={styles.label_wrap}>
        <Text className={styles.title}>{label}</Text>
      </View>
    )
  }

  return (
    <View>
      <DateSelect onSelectHandle={onSelectHandle} />
      <DragUpdataPage>
        <Noticebar>提示：查询时间为提交时间</Noticebar>
        <ScrollView style={scrollStyle}>
          <View className={styles.content}>
            <View className={styles.list_text}>
              <View className={styles.list_item}>
                {renderLable('办理类型')}
                <Text>1111</Text>
              </View>
              <View className={styles.list_item}>
                {renderLable('业务大类')}
                <Text>1111</Text>
              </View>
              <View className={styles.list_item}>
                {renderLable('业务小类')}
                <Text>1111</Text>
              </View>
              <View className={styles.list_item}>
                {renderLable('办理开始时间')}
                <Text>1111</Text>
              </View>
              <View className={styles.list_item}>
                {renderLable('办理状态')}
                <Text>1111</Text>
              </View>
              <View className={styles.list_btn}>
                <View className={styles.foot_btn}>
                  <Icon size='14' type='search' color='#fff' />
                  查看明细
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
        <View className={styles.white_cont}></View>
      </DragUpdataPage>
    </View>
  )
}

export default AppointmentQuery
