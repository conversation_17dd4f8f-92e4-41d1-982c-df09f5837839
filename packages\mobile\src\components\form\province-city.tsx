/**
 * 省份城市联动
 */
import React, { FunctionComponent, useEffect, useState } from 'react'
import { View, Text, Picker, Image } from '@tarojs/components'
import icon_arrow from '@assets/icon/icon-arrow.png'
import styles from './index.module.scss'
import { CommonItemProps } from './type'
import { citys } from './city'

const ProvinceCityItem: FunctionComponent<Omit<CommonItemProps, 'ref'> & {ref:any}> = React.forwardRef((props, ref) => {
  const { control, type, level, pageOptions, placeholder, value, ...reset } = props
  const [range, setRange] = useState<{ key: string; value: string }[]>([])
  const [values, setValues] = useState([0, 0])
  const [label, setLabel] = useState('')
  useEffect(() => {
    const [v1, v2 = ''] = value?.split(',') || []
    const options1 = citys['0']
    const options2 = citys[v1 || '10708'] || []
    const index1 = citys['0'].findIndex(c => c.key === v1)
    const index2 = options2.findIndex((c: { key: any }) => c.key === v2)
    setLabel(`${options1[index1]?.value || ''} ${options2[index2]?.value || ''}`)
    setValues([index1, index2])
    setRange([options1, options2])
  }, [value])
  const onChange = (e: any) => {
    const [v1 = 0, v2 = 0]: any = e.detail.value || []
    const [options1 = [], options2 = []] = (range || []) as any
    reset.onChange(`${options1[v1]?.key || ''},${options2[v2].key || ''}`)
  }
  if (type === 'province_city') {
    return (
      <Picker
        className={styles.picker}
        ref={ref}
        style={{ flex: 1, width: 200, height: 60 }}
        mode='multiSelector'
        range={range as any}
        rangeKey='value'
        {...reset}
        value={values}
        onColumnChange={e => {
          const column = e.detail.column
          if (column === 0) {
            const v = e.detail.value || 0
            setRange([range[0], citys[range[0][v].key]])
            setValues([v, 0])
          }
        }}
        onChange={onChange}
      >
        <View className={styles.picker}>
          {label ? (
            <Text className={styles.item_text}>{label}</Text>
          ) : (
            <Text className={styles.placeholder}>{placeholder || '请选择'}</Text>
          )}
          <Image className={styles.item_arrow} src={icon_arrow} />
        </View>
      </Picker>
    )
  } else {
    return <Text>{`不支持${type}类型`}</Text>
  }
})

export default ProvinceCityItem
