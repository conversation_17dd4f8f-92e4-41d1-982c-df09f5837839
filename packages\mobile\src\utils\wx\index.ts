/***
 * 微信公众号相关逻辑
 */
import { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import * as Sentry from '@sentry/react'
import { closeWindow } from '@utils/jssdk'
import { getGlobalData, isComWx, isWx, setGlobalData } from '@utils'
import { getUrlParam } from '@utils/common'
import { pact } from '@apis/pact'
import { auth } from '@apis/auth'
import { AppId, WeComAppId } from '../../../config/env'

const GoToCodeKey = 'GoToCodeKey'
const isIOS = () => /iPhone|iPad|iPod/i.test(navigator.userAgent)
/**
 * appid  生成环境：wx51be6b4ca1db413c 测试环境：wx51be6b4ca1db413c
 */
const goWXAauth = () => {
  /** 微信获取code地址, URL上已有Code, 获取code后，跳授权页*/
  const appid = isWx() ? AppId : WeComAppId
  const redirect_uri = getRedirectUrl()
  const scope = isWx() ? 'snsapi_userinfo' : 'snsapi_base'
  const oauthUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirect_uri}&response_type=code&scope=${scope}#wechat_redirect`
  sessionStorage.setItem(GoToCodeKey, '1')
  window.location.replace(oauthUrl)
  if (isComWx()) {
    setTimeout(() => {
      const needRefresh = sessionStorage.getItem(GoToCodeKey)
      if (needRefresh) {
        sessionStorage.removeItem(GoToCodeKey)
        window.location.reload()
      }
    }, 1000)
  }
}

const getRedirectUrl = (): string => {
  let url = window.location.href
  if (isIOS()) {
    url = window.location.href.split('#')[0] + window.location.hash
  }
  if (url.includes('code=')) {
    url = url.replace(/([?&])code=[^&]*(&|$)/, '$1')
  }
  return encodeURIComponent(url)
}

const handleBindingStatus = (accountInfo: any) => {
  const currentPath = window.location.href
  const excludedPaths = ['#/home', '#/binding', '#/binding-faq', '#/policy_query', '#/robot_chat']

  if (
    !accountInfo.bindStatus &&
    !excludedPaths.some(path => currentPath.includes(path)) &&
    (accountInfo.isOuterEmp !== '1' || !currentPath.includes('#/attendance'))
  ) {
    Taro.redirectTo({ url: '/pages/binding/index' })
    return true
  }
  return false
}

/**
 * 微信公众号鉴权登录
 */

export const useWx = () => {
  const code = getUrlParam('code')
  const corpId = getUrlParam('corpId')
  const [refresh, setRefresh] = useState(0)
  const refreshState = () => {
    setRefresh(refresh + 1)
  }
  const getEpmInfoByOpenId = (openId: string, accountInfo?: any) => {
    if (!(accountInfo?.openId && accountInfo?.globalToken)) {
      return
    }
    pact.wxConfig.getEpmInfoByOpenId.request({ openId }).then((res: any) => {
      if (res) {
        setGlobalData('account', res)
        sessionStorage.setItem('globalToken', res.globalToken)
        if (handleBindingStatus(res)) return
      }
      refreshState()
    })
  }
  const handleAuthError = (err: any) => {
    Taro.showToast({ title: '获取用户信息失败', icon: 'none' })
    console.error('Auth Error:', err)
    Sentry.captureException(new Error('获取用户信息失败'))
    sessionStorage.setItem('AccountErrorInfo', JSON.stringify(err?.data || err))
    refreshState()
  }
  useEffect(() => {
    const account = getGlobalData<'account'>('account')
    const PlatformInfo = sessionStorage.getItem('PlatformInfo')
    const accountInfo = PlatformInfo && JSON.parse(PlatformInfo)
    /**本地测试环境账号写死 */
    if (APP_ENV === 'local') {
      const accountInfo1 = {
        isBind: true,
        cropId: 'wpK2XEEQAAFii9ZyHdRW-Zvg8u1H9lmQ',
        platformOpenid: 'woK2XEEQAAKMILBTcDmeFRwspGgOQLSw',
        globalToken:
          '4ca6ac21ec214ad1a796ced9163e0c077689c76aa9d7d80239889362475acd3e56bc7a1850e8bae372ae44d5ea33c499047499ed256f5f8b7d9fc21d972f84143cf5a0de17c5288e0ae95355a1cfabf88e0338b1ff8054eb37a387acbd9e46cff4be20319bb908e2fcba9b7651e1ec3bdf006df6c70e10475a1615b3513dbfceo199188d72d2l5949e949d1202cccb8a9276e6147cc6a',
        openId: 'oe7kywUUTmfxCqopEAyxPuLROoP8',
        empId: '*********',
        bindStatus: true
      }
      // const accountInfo1 = { "isBind": true, "cropId": "dingbcb5cbb6346fd4c7bc961a6cb783455b", "platformOpenid": "5ViiCQaZ9PJvpdAeRSAV2VgiEiE", "accountId": "2164395", "globalToken": "e07f91cc5e589651a57cc63a42869641f88d1ea8f739417352b3c21cae456b9f39f4e8a79fe90010d0a99458c845066b1863edeebc94b3c883c206d0690fa506527da88d4fb65adbca066002bf4146de12411acbc3763e05027d651c08ac8cce5990c34e1c3771d3d73d50943e7f1e48aff642f651e2187b6f449277dc94b057o1934345ebael670481f74e7d5e3c5a5f9aaaf0b22bc9", "sign": false, "openId": "5ViiCQaZ9PJvpdAeRSAV2VgiEiE", "bindStatus": true }
      setGlobalData('account', accountInfo1)
      sessionStorage.setItem('PlatformInfo', JSON.stringify(accountInfo1))
      /** openId: 'oe7kywYFc_PateYKsNfDv7t-f70E' */
      // getEpmInfoByOpenId('woK2XEEQAAKMILBTcDmeFRwspGgOQLSw')
      return
    }
    if (isWx()) {
      /**微信公众号登录 */
      if (account?.openId) {
        // 已经登录
      } else {
        const openId = sessionStorage.getItem('openId')
        if (openId) {
          // 刷新不用重新授权
          getEpmInfoByOpenId(openId)
          return
        }
        if (code && sessionStorage.getItem(GoToCodeKey) === '1') {
          sessionStorage.removeItem(GoToCodeKey)
          pact.wxConfig.getEpmInfoByCode
            .request({ code })
            .then((res: any) => {
              if (res?.errorMessage === '0') {
                Taro.showToast({ title: '请先关注公众号', icon: 'none' })
                closeWindow()
                return
              }
              // code重复 分享场景时
              if (res?.errorMessage === '40163') {
                goWXAauth()
                return
              }

              if (handleBindingStatus(res)) return
              if (res.openId) {
                sessionStorage.setItem('openId', res?.openId)
                res?.globalToken && sessionStorage.setItem('globalToken', res?.globalToken)
                res && setGlobalData('account', res)
                sessionStorage.setItem('PlatformInfo', JSON.stringify(res))
                refreshState()
              } else {
                handleAuthError(res)
              }
            })
            .catch(handleAuthError)
        } else {
          goWXAauth()
        }
      }
    } else if (isComWx()) {
      /**企业微信登录 */
      if (!PlatformInfo) {
        if (code) {
          sessionStorage.removeItem(GoToCodeKey)
          auth.qywxWeb.getTokenByCode.request({ code }).then(res => {
            const accountInfo1 = {
              ...res.data,
              openId: res.data?.platformOpenid,
              bindStatus: res.data?.isBind
            }
            setGlobalData('account', accountInfo1)
            sessionStorage.setItem('PlatformInfo', JSON.stringify(accountInfo1))
            refreshState()
            if (res.success && !res.data?.isBind) {
              Taro.redirectTo({ url: '/pages/binding/index' })
            }
          })
        } else {
          goWXAauth()
        }
      } else {
        getEpmInfoByOpenId(accountInfo?.openId, accountInfo)
      }
    } else {
      /**钉钉登录 */
      if (!accountInfo) {
        dd.ready(() => {
          // dd.ready参数为回调函数，在环境准备就绪时触发，jsapi的调用需要保证在该回调函数触发后调用，否则无效。
          dd.runtime.permission.requestAuthCode({
            corpId,
            onSuccess: function(result) {
              auth.dingtalkWeb.getTokenByCode.request({ code: result.code, corpId }).then(res => {
                const accountInfo1 = {
                  ...res.data,
                  openId: res.data?.platformOpenid,
                  bindStatus: res.data?.isBind
                }
                setGlobalData('account', accountInfo1)
                sessionStorage.setItem('PlatformInfo', JSON.stringify(accountInfo1))
                refreshState()
                if (res.success && !res.data?.isBind) {
                  Taro.redirectTo({ url: '/pages/binding/index' })
                }
              })
            },
            onFail: function(err) {
              console.log('钉钉获取code失败---', corpId, err)
            }
          })
        })
      } else {
        getEpmInfoByOpenId(accountInfo?.openId, accountInfo)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
}
