import { View, Text, Image, Input } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import { isPhoneNumber } from '@utils/is-type'
import { useEffect, useState } from 'react'
import { pact } from '@apis/pact'
import { getGlobalData, setGlobalData, getScrollStyle, showSuccess } from '@utils'
import { Form, BottomBtn, FormItemProps, withPage, useForm } from '@components'
import styles from './index.module.scss'

type FormValues = {
  idCardNum: string
  mobilePhoneNum: string
  vcode: string
  verificationCode: string
}
const smsMsg = [
  '不是内部员工，无法绑定!',
  '',
  '您是我们的内部员工,但是没开通,请联系企业人事!',
  '系统中没有找到手机号码,请联系企业人事!',
  '您的操作过于频繁,请1分钟后请求!',
  '验证码无效!'
]
const bindMsg = [
  '不是内部员工，无法绑定!',
  '绑定成功!',
  '您是我们的内部员工,但是没开通,请联系企业人事!',
  '',
  '您的操作过于频繁,请1分钟后请求!',
  '验证码无效!'
]
const COUNT_SEC = 60

const SendVerifyBtn = ({ checkValue }) => {
  const { openId, accountId } = getGlobalData<'account'>('account')
  const [sec, setSec] = useState(COUNT_SEC)
  const [loading, setLoading] = useState(false) // 开始调接口
  const [timing, setTiming] = useState(false) // 开始倒计时

  useEffect(() => {
    let interval
    // 开始倒计时
    if (timing) {
      interval = setInterval(() => {
        setSec(preSecond => {
          if (preSecond <= 1) {
            setTiming(false)
            clearInterval(interval)
            // 重置秒数
            return COUNT_SEC
          } else {
            return preSecond - 1
          }
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [timing])

  const sendPhoneCode = async () => {
    const formData = await checkValue()
    if (formData && !loading && !timing) {
      setLoading(true)
      await pact.auth.sendSMS
        .request({ accountId, openId, ...formData })
        .then(res => {
          const result = res.data?.result || 'unexpected wrong'
          if (result == '1') {
            showSuccess('验证码发送成功')
            setTiming(true)
          } else {
            Taro.showToast({title: smsMsg[result] || result, icon: 'none'})
          }
        })
        .finally(() => setLoading(false))
    }
  }
  return (
    <View className={styles.btn_sm_wrap} onClick={sendPhoneCode}>
      <Text className={styles.btn_sm}>{timing ? `${sec}s` : '获取验证码'}</Text>
    </View>
  )
}

const Index = () => {
  const account = getGlobalData<'account'>('account')
  const { openId, accountId } = account
  const [verifyImg, setVerifyImg] = useState<string>('')
  const form = useForm<FormValues>()
  const scrollStyle = getScrollStyle({ bottom: 120, top: 400, hasNav: false })
  const { pageType } = useRouter().params
  const columns: FormItemProps[] = [
    {
      title: '证件号',
      name: 'idCardNum',
      type: 'text',
      rules: { required: '请填写正确的证件号码' },
      inputProps: { maxlength: 18 },
      showLine: false
    },
    {
      title: '手机号码',
      name: 'mobilePhoneNum',
      type: 'mobile',
      rules: { validate: val => isPhoneNumber(val), required: true },
      inputProps: { maxlength: 11 },
      customMsg: '请填写正确的手机号码',
      showLine: false
    },
    {
      title: '验证码',
      name: 'vcode',
      rules: { required: '请填写验证码' },
      showLine: false,
      render: field => (
        <View className={styles.ipt_wrap}>
          <Input onInput={field.onChange} {...field} className={styles.input_code} maxlength={4} />
          <Image src={`data:image/png;base64,${verifyImg}`} className={styles.ver_code} onClick={updateVerifyImg} />
        </View>
      )
    },
    {
      title: '手机验证码',
      name: 'verificationCode',
      rules: { required: '请填写手机验证码', validate: val => val?.length == 6 },
      customMsg: '手机验证码有误',
      showLine: false,
      render: field => (
        <View className={styles.ipt_wrap}>
          <Input onInput={field.onChange} {...field} className={styles.input_code} maxlength={6} />
          <SendVerifyBtn checkValue={checkValue} />
        </View>
      )
    }
  ]

  useEffect(() => {
    updateVerifyImg()
  }, [])

  const updateVerifyImg = async () => {
   await pact.auth.getVcode.request({ openId: openId, accountId: accountId }).then(res => {
      setVerifyImg(res?.data?.base64 || '')
    })
  }

  const checkValue = async () => {
    const { idCardNum, mobilePhoneNum, vcode } = form.getValues()
    const isValidate = await form.trigger(['idCardNum', 'mobilePhoneNum', 'vcode'])

    return isValidate ? { cellphone: mobilePhoneNum, idCardNum, vcode } : null
  }
  const goHomePage = async () => {
    await pact.wxConfig.getEpmInfoByOpenId.request({ openId }).then((res) => {
      setGlobalData('account', { ...account, ...res })
      switch (pageType) {
        case 'wageInquiry':
          Taro.reLaunch({ url: '/pages/wage-inquiry/main/index' })
          return
        case 'housingFund':
          Taro.reLaunch({ url: '/pages/social-security/list/index?type=accumulation'})
          return
        case 'socialSecurity':
          Taro.reLaunch({ url: '/pages/social-security/list/index?type=social-security' })
          return
        case 'city':
          Taro.reLaunch({ url: '/pages/city/index' })
          return
        case 'appointment':
          Taro.reLaunch({ url: '/pages/appointment/add/index' })
          return
        case 'elesignature':
          Taro.reLaunch({ url: '/pages/induction-elesignature/eleContractList/index' })
          return
        case 'elesignatureTag3':
          Taro.reLaunch({ url: '/pages/staff-dimission/eleContractList/index?pageTag=3' })
          return
        case 'elesignatureTag4':
          Taro.reLaunch({ url: '/pages/staff-dimission/eleContractList/index?pageTag=4' })
          return
        default:
          Taro.reLaunch({ url: 'pages/robot_chat/index' })
          return
      }
    })
  }
  const onSubmit = async data => {
    const { verificationCode, idCardNum, mobilePhoneNum } = data
    const res = await pact.auth.authenticationBinding.request({
      openId,
      accountInfo: { verificationCode, accountId, idCardNum, mobilePhoneNum }
    })
    const result = res.data?.result || 'unexpected wrong'
    if (result !== '1'){
      Taro.showToast({title: bindMsg[result] || result,icon: 'none'})
      return
    }
    Taro.showToast({
      title: '绑定成功',
      icon: 'none',
      success: () => {
        goHomePage()
      }
    })
    // if (result === '1') {
    //   // 绑定成功
    //   const response = await pact.wxConfig.getEpmInfoByOpenId.request({ openId })
    //   if (response) {
    //     setGlobalData('account', { ...account, ...response })
    //   }
    // }
    // Taro.showModal({
    //   title: '提示',
    //   content: bindMsg[result] || result,
    //   success: ({ confirm }) => {
    //     if (confirm) {
    //     }
    //   }
    // })
  }

  return (
    <View className={styles.wrap}>
      <Image src={require('../../assets/img-banner.png')} className={styles.banner} />
      <Form form={form} columns={columns} style={scrollStyle} />
      <BottomBtn
        btns={[
          {
            title: '绑定',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
