/* eslint-disable import/no-commonjs */
const path = require('path')
const appConfig = require('./config')
const customRoutes = require('./customRoutes');

const APP_Prefix = process.env.APP_Prefix || '/'
console.log('APP_Prefix---',APP_Prefix);
// const HOST = '"http://*************:8000"'
const config = {
  projectName: 'mobile',
  date: '2021-7-27',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2
  },
  sourceRoot: 'src',
  outputRoot: `dist/${process.env.TARO_ENV}`,
  plugins: [
    [`${path.resolve(__dirname, 'plugins/router.ts')}`, appConfig]
    // path.resolve(__dirname, 'plugins', 'svg.ts'),
  ],
  defineConstants: {
    IS_H5: process.env.TARO_ENV === 'h5',
    IS_RN: process.env.TARO_ENV === 'rn',
    IS_WEAPP: process.env.TARO_ENV === 'weapp',
    APP_ENV: JSON.stringify(process.env.APP_ENV || 'local')
  },
  alias: {
    '@components': path.resolve(__dirname, '..', 'src/components'),
    '@utils': path.resolve(__dirname, '..', 'src/utils'),
    '@assets': path.resolve(__dirname, '..', 'src/assets'),
    '@apis': path.resolve(__dirname, '..', 'src/apis')
  },
  copy: {
    patterns: [
      // { from: 'src/asset/MP_verify_0aMbFeSRh5eXvuEa.txt', to: 'dist/h5/MP_verify_0aMbFeSRh5eXvuEa.txt' }
    ],
    options: {}
  },
  framework: 'react',
  mini: {
    postcss: {
      pxtransform: {
        enable: true,
        config: {}
      },
      url: {
        enable: true,
        config: {
          limit: 1024 // 设定转换尺寸上限
        }
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    }
  },
  h5: {
    publicPath: APP_Prefix,
    staticDirectory: 'static',
    esnextModules: ['taro-ui'],
    postcss: {
      autoprefixer: {
        enable: true,
        config: {}
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    output: {
      filename: 'js/[name].[hash:8].js',
      chunkFilename: 'js/[name].[chunkhash:8].js',
    },
    webpackChain(chain) {
      chain.merge({
        module: {
          rule: {
            myloader: {
              test: /\.js$/,
              use: [
                {
                  loader: "babel-loader",
                  options: {}
                }
              ]
            }
          }
        }
      });
    },
    router: {
      // mode: 'browser',
      customRoutes
    },
    // devServer: {
    //   proxy: {
    //     '/lowcode_admin': {
    //       target: JSON.parse(HOST),
    //       changeOrigin: true,
    //       logLevel: 'debug',
    //       onProxyReq: (proxyReq, req, res) => {
    //         // proxyReq.setHeader('Host', 'http://*************:8000/');
    //         // proxyReq.setHeader('Referer', 'http://*************:8000/');
    //         console.log('Proxy request:', req.method, req.url);
    //       },
    //       onProxyRes: (proxyRes, req, res) => {
    //         proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    //         console.log('Proxy response:', proxyRes.statusCode, req.url);
    //       },
    //       // secure: false,
    //       // pathRewrite: {
    //       //   '^/lowcode_admin':  '/lowcode_admin',
    //       // },
    //     },
    //     '/lowcode': {
    //       target: JSON.parse(HOST),
    //       changeOrigin: true,
    //       //secure: false,
    //       // target: targetTest,
    //       // target:personal,
    //       //changeOrigin: false,
    //       logLevel: 'debug',
    //       onProxyReq: (proxyReq, req, res) => {
    //         // proxyReq.setHeader('Host', 'http://*************:8000/');
    //         // proxyReq.setHeader('Referer', 'http://*************:8000/');
    //         console.log('Proxy request:', req.method, req.url);
    //       },
    //       onProxyRes: (proxyRes, req, res) => {
    //         proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    //         console.log('Proxy response:', proxyRes.statusCode, req.url);
    //       },
    //     },
    //   },
    // },
  },
  rn: {
    postcss: {
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'global', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    output: {
      ios: '../app/ios/main.jsbundle',
      iosAssetsDest: '../app/ios',
      android: '../app/android/app/src/main/assets/index.android.bundle',
      androidAssetsDest: '../app/android/app/src/main/res'
    }
  }
}

module.exports = function(merge) {
  if (process.env.NODE_ENV === 'development') {
    return merge({}, config, require('./dev'))
  }
  return merge({}, config, require('./prod'))
}
