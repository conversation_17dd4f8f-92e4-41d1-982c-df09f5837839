.wrap {
  flex: 1;
}

.tip {
  position: absolute;
  top: 266px;
  left: 0;
  right: 0;
  z-index: 1;
}

.item {
  border: 0px solid #efeff4;
  border-bottom-width: 10px;
  background-color: #ffffff;
  width: 750px;
  padding: 32px;
  box-sizing: border-box;

  &:hover {
    background-color: #f8f9fa;
  }
}

.text_wrap {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin: 16px 0;
  min-height: 60px;

  &:first-child {
    margin-top: 0;
  }

  &:last-of-type {
    margin-bottom: 24px;
  }
}

.title {
  width: 220px;
  font-size: 30px;
  color: #666666;
  font-weight: 500;
  line-height: 1.4;
  flex-shrink: 0;
}

.detail {
  font-size: 30px;
  color: #333333;
  flex: 1;
  line-height: 1.4;
  word-break: break-all;

  &:empty::after {
    content: '-';
    color: #999999;
  }
}

.btn_wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 20px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
