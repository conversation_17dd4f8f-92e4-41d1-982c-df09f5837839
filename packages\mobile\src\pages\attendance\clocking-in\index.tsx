// @ts-nocheck

import { View, ScrollView } from '@tarojs/components'
import dayjs from 'dayjs'
import { useRouter } from '@tarojs/taro'
import { AtTabs, AtTabsPane, AtCalendar } from 'taro-ui'
import 'taro-ui/dist/style/components/tabs.scss'
import 'taro-ui/dist/style/components/calendar.scss'

import { useEffect, useState } from 'react'

import { BottomBtn, Modal, withPage } from '@components'
import { ncmp } from '@apis/ncmp'

import styles from './index.module.scss'

const Index = () => {
  // const account = getGlobalData<'account'>('account')
  const params = useRouter().params

  const custId = params?.custId // 测试用ID ********
  if (!custId) {
    Taro.showToast({ title: '链接无效', icon: 'none' })
    return <View></View>
  }

  const tabList = [{ title: '日考勤记录' }, { title: '月考勤记录' }]
  const [tabCurrent, setTabCurrent] = useState<number>(params?.tab === '1' ? 1 : 0)

  const [currentDay, setCurrentDay] = useState(dayjs().format('YYYY-MM-DD'))
  const [currentDayToMonth, setCurrentDayToMonth] = useState(dayjs().format('YYYYMM'))
  useEffect(() => {
    setCurrentDayToMonth(dayjs(currentDay).format('YYYYMM'))
  }, [currentDay])
  const [dayData, setDayData] = useState()
  useEffect(() => {
    ncmp.acEmpRecord.detailList
      .request({
        stMonth: currentDayToMonth,
        custId: custId,
      })
      .then((res) => {
        setDayData(res?.resultObj)
      })
  }, [currentDayToMonth])
  const thisDayData: any[] = (dayData?.list || []).find((i) => i?.dt === currentDay)
  const validDates: { value: string }[] = (dayData?.list || []).map((i) => ({ value: i?.dt }))
  const [marks, setMarks] = useState<{ value: string }[]>([])
  useEffect(() => {
    const _marks: { value: string }[] = (dayData?.list || []).filter((i) => i?.exception).map((i) => ({ value: i?.dt }))
    setMarks(_marks)
  }, [dayData])

  useEffect(() => {
    if (marks) {
      const _days = marks.map(i => dayjs(i.value).format('D'))
      console.log('===', marks, _days)
      document.querySelectorAll('.at-calendar .flex__item--now.hydrated').forEach(v => {
        console.log('===', v.textContent)
        if (_days.includes(v.textContent)) {
          v.style.color = 'red'
        }
      })
    } else {
    }
  }, [marks])

  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [monthData, setMonthData] = useState()
  useEffect(() => {
    ncmp.acEmpRecord.monthList
      .request({
        stMonth: dayjs(currentMonth).format('YYYYMM'),
        custId: custId,
      })
      .then((res) => {
        setMonthData(res?.resultObj)
      })
  }, [currentMonth])

  /**
   * @param timeSet
   * @param type 1: 上班 2: 下班
   * @param time
   * @returns
   */
  function clockingInResRender(timeSet, type: 1 | 2, time?: string) {
    if (time) {
      if (type === 1 && time > timeSet) {
        return <View className={styles.colorRed}>迟到({time})</View>
      }
      if (type === 2 && time < timeSet) {
        return <View className={styles.colorRed}>早退({time})</View>
      }

      return <View className={styles.colorGray}>打卡({time})</View>
    } else {
      return (
        <View style={{ display: 'flex', justifyContent: 'space-between' }}>
          <View className={styles.colorRed}>未打卡</View>
          <View
            style={{
              transform: 'translateY(-50%)',
            }}
            onClick={() => {
              Taro.navigateTo({ url: `/pages/attendance/clockin/supplementary/index?custId=${custId}` })
            }}
          >
            补卡
          </View>
        </View>
      )
    }
  }

  return (
    <View className={styles.wrap}>
      <AtTabs current={tabCurrent} tabList={tabList} onClick={setTabCurrent}>
        <AtTabsPane current={tabCurrent} index={0} className={styles.tabPane}>
          <AtCalendar
            key={'0'}
            currentDate={currentDay}
            onDayClick={(v) => {
              setCurrentDay(v.value)
            }}
            isSwiper={false}
            maxDate={dayjs().format('YYYY/MM/DD')}
            onMonthChange={setCurrentDay}
            // marks={marks} // 会导致渲染bug
            validDates={validDates.length ? validDates : [{ value: dayjs().add(1, 'M').format('YYYY/MM/DD') }]}
          />

          {thisDayData && (
            <View style={{ marginTop: '30px' }}>
              <View style={{ padding: '0 20px', marginBottom: '10px' }}>打卡</View>
              <View>
                <View className={styles.dayItem}>
                  <View>{thisDayData.checkInSet}</View>
                  <View className={styles.dayItemRight}>
                    <View>上班</View>
                    {clockingInResRender(thisDayData.checkInSet, 1, thisDayData.checkIn)}
                  </View>
                </View>
                <View className={styles.dayItem}>
                  <View>{thisDayData.checkOutSet}</View>
                  <View className={styles.dayItemRight}>
                    <View>下班</View>
                    {clockingInResRender(thisDayData.checkOutSet, 2, thisDayData.checkOut)}
                  </View>
                </View>
              </View>
            </View>
          )}
        </AtTabsPane>

        <AtTabsPane current={tabCurrent} index={1} className={styles.tabPane}>
          <AtCalendar
            key={'1'}
            className={styles.monthCalendar}
            maxDate={dayjs().format('YYYY/MM/DD')}
            currentDate={currentMonth}
            onMonthChange={setCurrentMonth}
          />

          {monthData && (
            <View style={{ padding: '0 20px' }}>
              <View style={{ color: '#108EE9', marginBottom: '10px' }}>正常出勤{monthData?.realWorkDays}天</View>
              <View style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                <View className={styles.monthItem}>
                  <View>{monthData?.lateTime}</View>
                  <View>迟到(小时)</View>
                </View>
                <View className={styles.monthItem}>
                  <View>{monthData?.earlyTime}</View>
                  <View>早退(小时)</View>
                </View>
                <View className={styles.monthItem}>
                  <View>{monthData?.osdCount}</View>
                  <View>外勤(次数)</View>
                </View>
                <View className={styles.monthItem}>
                  <View>{monthData?.dayoffCount}</View>
                  <View>矿工(天数)</View>
                </View>
              </View>
            </View>
          )}
        </AtTabsPane>
      </AtTabs>
    </View>
  )
}

export default withPage(Index)
