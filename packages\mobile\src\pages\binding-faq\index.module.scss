.banner {
  width: 750px;
  height: 370px;
  margin-bottom: 40px;
  position: absolute;
  left: 0;
  right: 0;
  top:0;
}
.item {
  margin-bottom: 40px;
}
.binding_item {
  width: 750px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.label_wrap {
  width: 200px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-direction: row;
}
.required {
  width: 14px;
  height: 16px;
  margin-right: 10px;
}
.title {
  color: #333;
  font-size: 24px;
}
.input {
  color: #333;
  font-size: 28px;
  width: 420px;
  height: 70px;
  margin-left: 30px;
  border: 1px solid rgb(222, 226, 230);
  border-radius: 8px;
  padding-left: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
}
.input:focus {
  border-color: #b51f24;
}
.input:hover {
  border-color: #b51f24;
}
.input_sm {
  @extend .input;
  width: 240px;
  margin-right: 20px;
}

.ver_code {
  width: 160px;
  height: 60px;
}

.btn_sm_wrap {
  width: 160px;
  height: 60px;
  background-color: #b51f24;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.btn_sm {
  color: #fff;
  font-size: 22px;
}

.bottom_wrap {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 120px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-direction: row;
  width: 100%;
}

.btn_bg_wrap {
  width: 340px;
  height: 88px;
  border-radius: 16px;
  background-color: #b51f24;
  display: flex;
  justify-content: center;
  align-items: center;
}
.btn_bg {
  color: #fff;
  font-size: 28px;
}
.btn_wrap {
  display: flex;
  justify-content: space-around;
  // position: fixed;
  width: 100%;
  left: 0;
  bottom: 10px;
}
.bind_btn {
  background: #b51e25;
  flex: 1;
  margin: 0 10px;
  color: #fff;
  font-size: 30px;
}
.err_msg {
  margin-left: 230px;
  font-size: 28px;
  color: red;
  font-weight: 300;
}

.ipt_wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.input_code {
  @extend .input_sm;
  margin-left: 0;
}
.wrap {
  position: relative;
  background-color: rgb(248, 249, 250);
}
