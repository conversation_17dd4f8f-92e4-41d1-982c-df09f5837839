import { FunctionComponent } from 'react'
import { View, Text, Image } from '@tarojs/components'
import icon_star from '@assets/icon/icon-star.png'
import styles from './index.module.scss'
import FormTip from './form-tip'

export interface FormLabelProps {
  title: string
  level: 1 | 2 | 3
  required?: boolean
  width?: string // level == 1 | 2 defalut: 100%; 3
  titleRemind?: string
  renderRight?: () => React.ReactElement
}

const FormLabel: FunctionComponent<FormLabelProps> = props => {
  const { title, level, required, width, renderRight, titleRemind, children } = props
  if (level === 1) {
    return (
      <>
        <View className={styles.label_wrap} style={{ justifyContent: 'center', width: width || '100%' }}>
          <View className={styles.title1_line} />
          <Text className={styles.title1}>{title}</Text>
          <View className={styles.title1_line} />
        </View>
        {titleRemind && <FormTip tip={titleRemind} />}
      </>
    )
  } else if (level === 2) {
    return (
      <View className={styles.label} style={{ width: width || '100%' }}>
        <View className={styles.label_wrap2} style={{ width: width || '100%' }}>
          <View className={styles.title2_line} />
          {required && <Image src={icon_star} className={styles.required} />}
          <Text className={renderRight ? styles.title2 : styles.title22}>{title}</Text>
          {renderRight?.()}
        </View>
        {titleRemind && <FormTip tip={titleRemind} />}
      </View>
    )
  } else {
    return (
      <View className={styles.label_wrap} style={{ width: width || '40%' }}>
        {required && <Image src={icon_star} className={styles.required} />}
        <Text className={styles.title3}>{title}</Text>
        {children}
      </View>
    )
  }
}

export default FormLabel
