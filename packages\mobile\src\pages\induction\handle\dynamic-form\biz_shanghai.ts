import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleShangHaiColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const isFirstSecurity = form.watch('isFirstSecurity')
  if (column.name === 'pfAccount') {
    let flag = !isFirstSecurity || isFirstSecurity !== '0' 
    return { ...column, remind: '公积金账号查询方式：①支付宝：市民中心-公积金；②微信端: 公众号搜索“上海公积金官网”-查公积金账号；③一网通办：下载“一网通办”APP查询个人公积金账号；',isHidden: flag}
  }
  return column;
}

export { handleShangHaiColumn }