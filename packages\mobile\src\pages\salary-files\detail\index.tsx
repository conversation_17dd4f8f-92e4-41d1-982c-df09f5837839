/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-15 16:52:27
 * @LastAuthor: 王正荣
 * @LastTime: 2021-09-18 10:55:54
 * @message:
 */
import { View, ScrollView } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { encryIdNumber, getGlobalData, getScrollStyle } from '@utils'
import { users } from '@apis/users'
import { BottomBtn, withPage, Labels, FormLabel } from '@components'
import Taro, { useRouter } from '@tarojs/taro'
import styles from './index.module.scss'

const Index = () => {
  const { openId: cmpToken, empId } = getGlobalData<'account'>('account')
  const { dataId } = useRouter<{ dataId: string }>().params
  const [detail, setDetail] = useState<defs.users.RequestPsnDetail>()
  const scrollStyle = getScrollStyle({ bottom: 120 })

  useEffect(() => {
    users.psnDetail.getPsnDetail
      .request({
        empId,
        cmpToken,
        dataId
      })
      .then(res => {
        if (res.data) {
          setDetail(res.data[0])
        }
      })
  }, [empId, cmpToken, dataId])

  return (
    <View>
      <ScrollView style={scrollStyle} scrollY>
        <View>
          <FormLabel level={2} title='基本信息' />
          <Labels title='1居民2非居民' detail={detail?.taxPayerType === '1' ? '居民' : '非居民'} />
          <Labels title='姓名' detail={detail?.empName} />
          <Labels title='证件类型' detail={detail?.idCardType} />
          <Labels title='证照号码' detail={encryIdNumber(detail?.idCardNum)} />
          <Labels title='报税起始月' detail={detail?.mintax} />
          <Labels title='报税截止月' detail={detail?.maxtax} />
        </View>
        <View className={styles.group}>
          <FormLabel level={2} title='工资薪金' />
          <Labels title='工资总额' detail={detail?.f9total} />
          <Labels title='养老个人' detail={detail?.f55} />
          <Labels title='医疗个人' detail={detail?.f56} />
          <Labels title='失业个人' detail={detail?.f57} />
          <Labels title='公积金个人' detail={detail?.f58} />
          <Labels title='应税薪资(含个税基数)' detail={detail?.f9} />
          <Labels title='起征点' detail={detail?.f25} />
          <Labels title='薪资税率' detail={detail?.f12} />
          <Labels title='薪资速算扣除数' detail={detail?.f13} />
          <Labels title='个调税/本次扣税' detail={detail?.f16} />
          <Labels title='税后薪资/实发合计' detail={detail?.f44} />
          <Labels title='子女教育支出' detail={detail?.f8261} />
          <Labels title='继续教育支出' detail={detail?.f8362} />
          <Labels title='住房贷款利息' detail={detail?.f8564} />
          <Labels title='住房租金支出' detail={detail?.f8665} />
          <Labels title='赡养老人支出' detail={detail?.f8766} />
          <Labels title='税优健康险' detail={detail?.f67} />
          <Labels title='税延型养老保险' detail={detail?.f68} />
          <Labels title='其他法定扣除项' detail={detail?.f69} />
        </View>
        <View className={styles.group}>
          <FormLabel level={2} title='劳务' />
          <Labels title='税前劳务费' detail={detail?.f33} />
          <Labels title='劳务费起征点' detail={detail?.f37} />
          <Labels title='劳务费税率' detail={detail?.f35} />
          <Labels title='劳务费速算扣除率' detail={detail?.f36} />
          <Labels title='劳务费税' detail={detail?.f38} />
          <Labels title='税后劳务费' detail={detail?.f34} />
        </View>
        <View className={styles.group}>
          <FormLabel level={2} title='年终奖' />
          <Labels title='税前年终奖' detail={detail?.f18} />
          <Labels title='年终奖税率' detail={detail?.f17} />
          <Labels title='年终奖速算扣除率' detail={detail?.f21} />
          <Labels title='年终奖税' detail={detail?.f17} />
          <Labels title='税后年终奖' detail={detail?.f19} />
        </View>
        <View className={styles.group}>
          <FormLabel level={2} title='其他' />
        </View>
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () => {
              Taro.navigateBack({ delta: 1 })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
