import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleTaiAnColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const payedIn_taian = form.watch('payedIn_taian')
  if (column.name === 'payedIn_taian') {
    form.register('payedIn_taian', {value: column.defaultValue})
  }
  if (column.name === 'oriPiArea_taian'){
    return {...column, isHidden: ['0', '否',''].includes(payedIn_taian)}
  }
  if (column.name === 'bankName_taian'){
    return {...column, isHidden: ['1', '是', ''].includes(payedIn_taian)}
  }
  if (column.name === 'bankNo_taian'){
    return {...column, isHidden: ['1', '是', ''].includes(payedIn_taian)}
  }
  return column;
}

export { handleTaiAnColumn }