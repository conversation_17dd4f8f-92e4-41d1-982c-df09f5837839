type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};

declare namespace defs {
  export namespace dynamicsForms {
    export class CustomFormBean {
      /** businessId */
      businessId?: string;

      /** code */
      code?: string;

      /** defaultValue */
      defaultValue?: string;

      /** itemUUID */
      itemUUID?: string;

      /** itemValue */
      itemValue?: string;

      /** name */
      name?: string;

      /** operation */
      operation?: string;

      /** optionList */
      optionList?: Array<ObjectMap<string, string>>;

      /** orderValue */
      orderValue?: number;

      /** parentItemUUID */
      parentItemUUID?: string;

      /** remind */
      remind?: string;

      /** requireFlag */
      requireFlag?: boolean;

      /** restrictValue */
      restrictValue?: string;

      /** subList */
      subList?: Array<defs.dynamicsForms.CustomFormBean>;

      /** titleLevel */
      titleLevel?: number;
    }

    export class DynamicFormReqBean {
      /** birthdate */
      birthdate?: string;

      /** cityCode */
      cityCode?: string;

      /** idCard */
      idCard?: string;

      /** name */
      name?: string;

      /** openId */
      openId?: string;

      /** pageNo */
      pageNo?: number;

      /** sex */
      sex?: string;

      /** uuid */
      uuid?: string;
    }

    export class EntryFileData {
      /** fileId */
      fileId?: string;

      /** fileUrl */
      fileUrl?: string;
    }

    export class FormFileItemBean {
      /** businessId */
      businessId?: string;

      /** data */
      data?: Array<defs.dynamicsForms.EntryFileData>;

      /** itemCode */
      itemCode?: string;

      /** itemName */
      itemName?: string;

      /** maxCount */
      maxCount?: number;

      /** remind */
      remind?: string;

      /** requireValue */
      requireValue?: boolean;
    }

    export class Map<T0 = any, T1 = any> {}
  }
}

declare namespace API {
  export namespace dynamicsForms {
    /**
     * 动态表单模块
     */
    export namespace dynamicsForm {
      /**
       * 获取城市的动态表单项数据
       * /dynamics-form-service/dynamics_form/appformItem
       */
      export namespace appgetFormItemsByCityCode {
        export class Params {}

        export type Response = Array<defs.dynamicsForms.CustomFormBean>;
        export const request: (
          data?: defs.dynamicsForms.DynamicFormReqBean,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.dynamicsForms.DynamicFormReqBean,
          options?: Taro.request.CommonUseRequestOption<defs.dynamicsForms.DynamicFormReqBean>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.dynamicsForms.DynamicFormReqBean
        >;
      }

      /**
       * 获取城市的动态文件条目项
       * /dynamics-form-service/dynamics_form/fileItem/{cityCode}/{openId}
       */
      export namespace getFormFileItemsByCityCode {
        export class Params {
          /** cityCode */
          cityCode: string;
          /** openId */
          openId: string;
        }

        export type Response = Array<defs.dynamicsForms.FormFileItemBean>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取城市的动态文件条目项,不含数据
       * /dynamics-form-service/dynamics_form/fileItemEmpty/{cityCode}
       */
      export namespace getEmptyFormFileItemsByCityCode {
        export class Params {
          /** cityCode */
          cityCode: string;
        }

        export type Response = Array<defs.dynamicsForms.FormFileItemBean>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取城市的动态表单项数据
       * /dynamics-form-service/dynamics_form/formItem
       */
      export namespace getFormItemsByCityCode {
        export class Params {}

        export type Response = Array<defs.dynamicsForms.CustomFormBean>;
        export const request: (
          data?: defs.dynamicsForms.DynamicFormReqBean,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.dynamicsForms.DynamicFormReqBean,
          options?: Taro.request.CommonUseRequestOption<defs.dynamicsForms.DynamicFormReqBean>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.dynamicsForms.DynamicFormReqBean
        >;
      }

      /**
       * 获取城市的动态表单项数据
       * /dynamics-form-service/dynamics_form/formItemEmpty
       */
      export namespace getEmptyFormItemsByCityCode {
        export class Params {}

        export type Response = string;
        export const request: (
          data?: defs.dynamicsForms.DynamicFormReqBean,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.dynamicsForms.DynamicFormReqBean,
          options?: Taro.request.CommonUseRequestOption<defs.dynamicsForms.DynamicFormReqBean>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.dynamicsForms.DynamicFormReqBean
        >;
      }
    }

    /**
     * Test Controller
     */
    export namespace test {
      /**
       * city
       * /parseFile/city
       */
      export namespace city {
        export class Params {}

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * test
       * /parseFile/get/{city}
       */
      export namespace test {
        export class Params {
          /** city */
          city: string;
        }

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }
  }
}
