{"compilerOptions": {"target": "es2017", "module": "commonjs", "removeComments": false, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "outDir": "lib", "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "baseUrl": ".", "rootDir": ".", "jsx": "react-jsx", "allowJs": true, "resolveJsonModule": true, "typeRoots": ["node_modules/@types", "global.d.ts"], "paths": {"@assets/*": ["src/assets/*"], "@components/*": ["src/components/*"], "@components": ["src/components/"], "@utils": ["src/utils/"], "@utils/*": ["src/utils/*"], "@apis/*": ["src/apis/*"]}}, "exclude": ["node_modules", "dist"], "compileOnSave": false}