import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleSuQianColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const payedIn_suqian = form.watch('payedIn_suqian')
  if (column.name === 'payedIn_suqian') {
    form.register('payedIn_suqian', {value: column.defaultValue})
  }
  if (column.name === 'pfundAccount_suqian') {
    return { ...column, isHidden: ['0', '否', ''].includes(payedIn_suqian) }
  }
  if (column.name === 'oriCompanyName_suqian') {
    return { ...column, isHidden: ['0', '否', ''].includes(payedIn_suqian) }
  }
  return column
}

export { handleSuQianColumn }
