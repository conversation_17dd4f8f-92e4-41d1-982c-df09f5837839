type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};

declare namespace defs {
  export namespace auth {
    export class BindAccountParams {
      /** 所属平台的企业id */
      company?: string;

      /** 员工姓名 */
      empName?: string;

      /** 身份证号 */
      idCardNum?: string;

      /** 手机号码 */
      mobilePhoneNum?: string;

      /** 手机验证码 */
      mobileVerifyCode?: string;

      /** 平台 1:公众号 2:企微 3:钉钉 4:飞书  */
      platform?: number;

      /** 所属平台的openid */
      platformOpenid?: string;
    }

    export class BindAccountResp {
      /** accountId */
      accountId?: string;

      /** accountInfoId */
      accountInfoId?: string;

      /** empId */
      empId?: string;

      /** 错误信息 */
      errorMsg?: string;

      /** 全平台用户token */
      globalToken?: string;
    }

    export class BindVerifyCodeResp {
      /** 报错信息 */
      errorMsg?: string;

      /** 0: 失败  1: 成功 */
      success?: number;
    }

    export class GlobalResult<T0 = any> {
      /** 数据对象 */
      data?: T0;

      /** 正常返回0，错误时返回非0的纯数字字符串 */
      errorCode?: string;

      /** 正常返回空，错误时返回错误信息 */
      errorMessage?: string;

      /** 正常返回true，错误时返回false */
      success?: boolean;
    }

    export class MobileVerifyParams {
      /** 身份证号 */
      idCardNum?: string;

      /** 手机号码 */
      mobilePhoneNum?: string;

      /** 平台 1:公众号 2:企微 3:钉钉 4:飞书  */
      platform?: number;

      /** 所属平台的openid */
      platformOpenid?: string;

      /** 图片验证码 */
      vcode?: string;
    }

    export class QySdkSign {
      /** 应用id */
      agentId?: string;

      /** 随机数 */
      noncestr?: string;

      /** 签名 */
      signature?: string;

      /** 时间戳 */
      timestamp?: string;
    }

    export class TokenByCode {
      /** 业务平台accountId，只特定情况下才会有 */
      accountId?: string;

      /** 企业唯一id */
      cropId?: string;

      /** 业务token，有正确绑定关系时才返回 */
      globalToken?: string;

      /** 是否绑定 */
      isBind?: boolean;

      /** 用户唯一id */
      platformOpenid?: string;

      /** 是否签约 */
      sign?: boolean;
    }

    export class VcodeResp {
      /** 图片验证码 */
      base64Img?: string;

      /** 错误消息 */
      errorMsg?: string;
    }
  }
}

declare namespace API {
  export namespace auth {
    /**
     * 业务平台账号对接相关
     */
    export namespace authWeb {
      /**
       * 删除绑定关系
       * /enterprise-auth/web/deleteBind
       */
      export namespace deleteBind {
        export class Params {
          /** 平台 1:公众号 2:企微 3:钉钉 4:飞书 */
          platform: number;
          /** 用户唯一ID，getTokenByCode返回 */
          platformOpenid: string;
        }

        export type Response = defs.auth.GlobalResult<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取图形验证码
       * /enterprise-auth/web/getImgCode
       */
      export namespace getImgCode {
        export class Params {
          /** 平台 1:公众号 2:企微 3:钉钉 4:飞书 */
          platform: number;
          /** 用户唯一ID，getTokenByCode返回 */
          platformOpenid: string;
        }

        export type Response = defs.auth.GlobalResult<defs.auth.VcodeResp>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取手机验证码
       * /enterprise-auth/web/getMobileCode
       */
      export namespace getMobileCode {
        export class Params {}

        export type Response =
          defs.auth.GlobalResult<defs.auth.BindVerifyCodeResp>;
        export const request: (
          data?: defs.auth.MobileVerifyParams,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.auth.MobileVerifyParams,
          options?: Taro.request.CommonUseRequestOption<defs.auth.MobileVerifyParams>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.auth.MobileVerifyParams
        >;
      }

      /**
       * 绑定账号
       * /enterprise-auth/web/updateBind
       */
      export namespace updateBind {
        export class Params {}

        export type Response =
          defs.auth.GlobalResult<defs.auth.BindAccountResp>;
        export const request: (
          data?: defs.auth.BindAccountParams,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.auth.BindAccountParams,
          options?: Taro.request.CommonUseRequestOption<defs.auth.BindAccountParams>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.auth.BindAccountParams
        >;
      }
    }

    /**
     * 钉钉网页应用相关
     */
    export namespace dingtalkWeb {
      /**
       * 获取钉钉JSAPI签名 dd.config
       * /enterprise-auth/web/ding/getConfigSignature
       */
      export namespace getConfigSignature {
        export class Params {
          /** corpId */
          corpId?: string;
          /** 页面地址，#号之前的部分 */
          pageUrl: string;
        }

        export type Response = defs.auth.GlobalResult<defs.auth.QySdkSign>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据钉钉免登录授权返回的code获取token和cropid
       * /enterprise-auth/web/ding/getTokenByCode
       */
      export namespace getTokenByCode {
        export class Params {
          /** 钉钉免登录授权返回的code */
          code: string;
          /** 钉钉免登录授权返回的corpId */
          corpId: string;
        }

        export type Response = defs.auth.GlobalResult<defs.auth.TokenByCode>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * 企业微信网页应用相关
     */
    export namespace qywxWeb {
      /**
       * 获取WECOM-JSSDK签名-应用签名
       * /enterprise-auth/web/qywx/getAgentConfigSignature
       */
      export namespace getAgentConfigSignature {
        export class Params {
          /** 企业ID，getTokenByCode返回的，前端缓存着使用 */
          cropId: string;
          /** 页面地址，#号之前的部分 */
          pageUrl: string;
        }

        export type Response = defs.auth.GlobalResult<defs.auth.QySdkSign>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取WECOM-JSSDK签名-企业签名
       * /enterprise-auth/web/qywx/getConfigSignature
       */
      export namespace getConfigSignature {
        export class Params {
          /** 企业ID，getTokenByCode返回的，前端缓存着使用 */
          cropId: string;
          /** 页面地址，#号之前的部分 */
          pageUrl: string;
        }

        export type Response = defs.auth.GlobalResult<defs.auth.QySdkSign>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据企业微信oauth2返回的code获取token和cropid
       * /enterprise-auth/web/qywx/getTokenByCode
       */
      export namespace getTokenByCode {
        export class Params {
          /** 企业微信oauth2返回的code */
          code: string;
        }

        export type Response = defs.auth.GlobalResult<defs.auth.TokenByCode>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }
  }
}
