/*
 * @Author: your name
 * @Date: 2021-09-10 14:40:39
 * @LastEditTime: 2021-11-23 11:07:01
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\declare\educationLook\index.tsx
 */
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { getGlobalData, setGlobalData, useSystemInfo } from '@utils'
import { getScrollStyle } from '@utils/transforms'
import Taro from '@tarojs/taro'
import icon_user from '@assets/icon/icon-user.png'
import isEmpty from 'lodash/isEmpty'
import { BottomBtn, Modal, withPage } from '@components'
import { users } from '@apis/users'
import useData from '@components/ListView/hooks/useData'
import styles from './index.module.scss'

const Index = () => {
  const empId = getGlobalData<'account'>('account').empId

  const [infolist, setInfolist] = useState<Array<defs.users.MoreContinuingEduInfo> | undefined>()
  useEffect(() => {
    users.user.getContinuingBasicInfo
      .request({
        employeeId: empId
      })
      .then(res => {
        // console.log(res)
        !isEmpty(res?.data) && setInfolist(res?.data)
      })
  }, [empId])

  //   编辑
  const toEdit = eduId => {
    // console.log('修改')
    Taro.navigateTo({ url: '/pages/pre-tax/declare/education/index?eduId=' + eduId })
  }
  const toDelete = eduId => {
    // console.log('删除')
    Taro.showModal({
      title: '删除',
      cancelText: '取消',
      cancelColor: 'xx',
      confirmText: '确认',
      confirmColor: 'xx',
      content: '一旦删除将不再继续抵扣，是否确认删除?',
      showCancel: true,
      success(result) {
        if (result.confirm) {
          // console.log('删除')
          users.user.deleteContinuingEdu
            .request({
              eduId: eduId
            })
            .then(res => {
              // console.log(res)
              if (res?.code == 0) {
                users.user.getContinuingBasicInfo
                  .request({
                    employeeId: empId
                  })
                  .then(ress => {
                    // console.log(ress,'删除的数据')
                    setInfolist(ress?.data)
                    Taro.navigateTo({
                      url: '/pages/pre-tax/declare-list/index'
                    })
                    Taro.showToast({
                      title: '删除成功',
                      icon: 'success',
                      duration: 2000
                    })
                  })
              }
            })
        } else if (result.cancel) {
          // console.log('不删除')
        }
      }
    })
    // users.user.deleteContinuingEdu
    //   .request({
    //     eduId: eduId
    //   })
    //   .then(res => {
    //     console.log(res)
    //     if (res?.code == 0) {
    //       users.user.getContinuingBasicInfo
    //         .request({
    //           employeeId: empId
    //         })
    //         .then(ress => {
    //           console.log(ress)
    //           !isEmpty(ress?.data) && setInfolist(ress?.data)
    //         })
    //     }
    //   })
  }
  return (
    <View className={styles.wrap}>
      <View className={styles.group_item}>
        <View className={styles.title_secondary}>
          <View className={styles.title_mark}></View>
          申报子女
        </View>
        <View className={styles.infoUl}>
          <View className={styles.addSupport}>
            <View className={styles.names}>教育阶段/类型</View>
            <View className={styles.names}>提交日期</View>
          </View>
          <View className={styles.btns}></View>
        </View>

        {infolist ? (
          infolist.map(item => (
            <View key={item.eduId} className={styles.infoUl}>
              <View className={styles.addSupport}>
                <View className={styles.names}>{item.eduPhase == 0 ? '学历教育' : '职业教育'}</View>
                <View className={styles.names}>{item.updateTime}</View>
              </View>
              <View className={styles.btns}>
                <View className={styles.btn_wrap}>
                  <Text className={styles.btn} onClick={() => toDelete(item.eduId)}>
                    删除
                  </Text>
                </View>
                <View className={styles.btn_wrap}>
                  <Text className={styles.btn} onClick={() => toEdit(item.eduId)}>
                    编辑
                  </Text>
                </View>
              </View>
            </View>
          ))
        ) : (
          <View>暂无数据</View>
        )}
      </View>
    </View>
  )
}

export default withPage(Index)
