import { useMemo } from 'react'
import isEmpty from 'lodash/isEmpty'
import { FunctionComponent } from '@tarojs/taro'
import { View, Text } from '@tarojs/components'
import { ModalProps } from './type'
import styles from './index.module.scss'

const Content: FunctionComponent<Omit<ModalProps, 'visible'>> = (props) => {
  const { onClose, onConfirm, btns = [], title, content } = props
  const buttons = useMemo(() => {
    const _buttons: typeof btns = []
    if (isEmpty(btns)) {
      onClose && _buttons.push({ title: '取消', type: 'close', onClick: onClose })
      onConfirm && _buttons.push({ title: '确定', type: 'primary', onClick: onConfirm })
      return _buttons
    } else {
      return btns
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [btns])
  const _content = useMemo(() => {
    if (typeof content === 'string') {
      return <Text className={styles.content_text}>{content}</Text>
    } else {
      return content
    }
  }, [content])
  return (
    <View className={styles.wrap}>
      {title && <Text className={styles.title}>{title}</Text>}
      <View className={styles.content}>{_content}</View>
        <View className={styles.footer}>
          {buttons.map((btn,index) => (
            <View key={btn.title} className={styles.btn} onClick={() => btn.onClick()} style={index + 1 === buttons.length ? {borderRightWidth: 0} : {}}>
              <Text
                className={btn.type === 'primary' ? styles.btn_text_primary : styles.btn_text}
                style={IS_RN ? (btn.type === 'primary' ? styles.btn_text_primary : styles.btn_text) : {}}
              >
                {btn.title}
              </Text>
            </View>
          ))}
      </View>
    </View>
  )
}

export default Content
