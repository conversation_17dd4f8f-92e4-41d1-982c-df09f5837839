import { useRef, useState } from 'react'
import Taro, { useDidShow } from '@tarojs/taro'
import { AtIcon } from 'taro-ui'
import { Icon, Input, View} from '@tarojs/components'
import { BottomBtn, withPage, ListView, usePagination} from '@components'
import { getScrollStyle } from '@utils/index'
import classNames from 'classnames'
import { ncmp } from '@apis/ncmp'
import styles from './index.module.scss'


const Index = () => {
  const [selectedValues, setSelectedValues] = useState({
    specialArea: []
  })
  const [search, setSearch] = useState<any>('')
  const inputRef = useRef<any>(null)
  const scrollStyle = getScrollStyle({ bottom: 120, top: 200,})
  const { SPECIALAREA } = Taro.getCurrentInstance()?.preloadData || {}
  const list = usePagination(
    async () => {
      const result:any = await  ncmp.policyEncyclopedia.listSpecialArea.request({
        cityname: search
      })
      result.resultObj.unshift({SHORTNAME: '全部特区', KEY: '', isAllSpecialArea: 1})
      const data = result.resultObj.reduce((pre,cur) => {
        if (cur.SHORTNAME.includes(search)){
          pre.push(cur)
        }
        return pre
      }, [])
      return data
    },
    { deps: [search] }
  )
  const handleSelectedCity = item => {
    const idx = selectedValues.specialArea.findIndex((cattr: any) => {
      return cattr.KEY === item.KEY
    })
    let newFilterAttrs: any = []
    newFilterAttrs = selectedValues.specialArea.slice()
    if (idx !== -1) {
      newFilterAttrs.splice(idx, 1)
      setSelectedValues({
        ...selectedValues,
        specialArea: newFilterAttrs
      })
    } else {
      newFilterAttrs.push(item)
      let selectedCitys;
      if (!item.KEY){
        selectedCitys = newFilterAttrs.filter((it:any) => !it.KEY )
      } else {
        selectedCitys = newFilterAttrs.filter((it:any) => it.KEY )
      }
      setSelectedValues({
        ...selectedValues,
        specialArea: selectedCitys
      })
    }

    console.log(newFilterAttrs)
  }
  const confirm = () => {
    Taro.preload({
      ...(Taro.getCurrentInstance().preloadData || {}),
      SPECIALAREA: selectedValues.specialArea,
      CITYS: [],
      PROVINCE: [],
    })
    Taro.navigateTo({
      url: `/pages/search_filter/index`
    })
  }
  useDidShow(() => {
    if (!SPECIALAREA) {
      return
    }
    setSelectedValues({
      ...selectedValues,
      specialArea: SPECIALAREA 
    })
  })
  return (
    <View className={styles.wrap1}>
      <View className={styles.header}>
        <View className={styles.input_wrap}>
          <Input className={styles.input_C} placeholder='搜索特区' ref={inputRef} />
          <View
            className={styles.search}
            onClick={() => {
              const value: any = inputRef.current.tmpValue || inputRef.current.value
              setSearch(value)
            }}
          >
            <Icon size='20' type='search' color='#fff' />
          </View>
        </View>
      </View>
      <ListView 
        style={scrollStyle}
        itemSize={100}
        noPull
        renderItem={(item)=>
          <View key={item.KEY} className={styles.item} onClick={() => handleSelectedCity(item)}>
          <View
            className={classNames(
              selectedValues.specialArea.some((cAttr: any) => {
                return cAttr.KEY === item.KEY
              })
                ? styles.active
                : ''
            )}
          >
            {item.SHORTNAME}
          </View>
          <View>
            {selectedValues.specialArea.some((cAttr: any) => {
              return cAttr.KEY === item.KEY
            }) && <AtIcon size={24} value='check' color='#B51E25' />}
          </View>
        </View>
        }
        {...list}
      />
      <BottomBtn
        btns={[
          {
            title: '确定',
            onClick: () => {
              confirm()
            }
          }
        ]}
      />
    </View>
  )
}
export default withPage(Index, {needLogin: false})
