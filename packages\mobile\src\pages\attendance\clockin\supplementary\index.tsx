import styles from './index.module.scss'
import { useState } from 'react'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { BottomBtn, Form, FormItemProps, useForm, withPage } from '@components'
import { request } from '@utils/request'
import { formatDate, getGlobalData, getScrollStyle } from '@utils/index'
import { ncmp } from '@apis/ncmp'

type PickerVisible = Record<string, boolean>
const Index: React.FC = () => {
  const form = useForm()

  const [suppType, setSuppType] = useState(1)

  const { custId } = useRouter()?.params

  const columns: FormItemProps[] = [
    {
      name: 'suppType',
      type: 'select',
      title: '补签类型',
      rules: { required: true },
      options: [
        { key: 1, value: '打卡' },
        { key: 3, value: '外勤' },
      ],
      onChange: (e) => {
        setSuppType(e)
        form.setValue('suppType', e)
      },
    },
    {
      name: 'suppTime',
      type: 'date',
      title: '补签日期',
      rules: { required: true },
      onChange: (e) => onHandleDatePicker('suppTime', e.detail.value),
    },
    {
      name: 'signTime',
      type: 'time',
      title: '补签时间',
      defaultValue: formatDate(new Date(), 'HH:mm'),
      rules: { required: true },
    },
    {
      name: 'reason',
      type: 'textarea',
      title: '补签原因',
      inputProps: { maxlength: 200, placeholder: '请输入补签原因' },
    },
  ]

  const onSubmit = async (values) => {
    let { suppType, suppTime, signType, signTime, reason } = values
    signType = suppType
    if (suppType === 1) {
      signType === 1 && (suppType = 1)
      signType === 2 && (suppType = 2)
    }

    const deviceId = getGlobalData<'systemInfo'>('systemInfo')?.model

    const res = await ncmp.eosCheckIn.saveCheckRepair.request({
      custId: Number(custId),
      checkType: signType,
      checkTime: suppTime + ' ' + signTime + ':00',
      checkRemark: reason,
      deviceDes: deviceId,
      deviceId: deviceId,
    })

    Taro.showToast({
      title: res?.resultObj === 1 ? '补签成功' : '补签失败',
      icon: 'none',
      duration: 2000,
    })

    res?.resultObj === 1 && setTimeout(() => Taro.navigateBack(), 2000)
  }

  const onHandleDatePicker = (name: string, value: any) => {
    form.setValue(name, value)
  }

  const scrollStyle = getScrollStyle({ bottom: 120 })

  return (
    <div className={styles.container}>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '提交',
            onClick: form.handleSubmit(onSubmit),
          },
        ]}
      />
    </div>
  )
}

export default withPage(Index)
