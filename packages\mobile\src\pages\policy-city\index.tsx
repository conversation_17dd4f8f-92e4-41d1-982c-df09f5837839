import { useState, useRef, useEffect } from 'react'
import { View, Text, Input, ScrollView, Icon,Image} from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import { BottomBtn, withPage } from '@components'
import { getGlobalData, getScrollStyle } from '@utils'
import { ncmp } from '@apis/ncmp'
import no_data from '@assets/no-data.png'
import styles from './index.module.scss'


const Index = () => {
  const { eventName } = useRouter<{ eventName: string }>().params
  const [city, setCity] = useState<defs.ncmp.City>()
  const [search, setSearch] = useState('')
  const inputRef = useRef<any>()
  const { empId } = getGlobalData<'account'>('account')
  const { result } = ncmp.policy.getEmpCity.useRequest(
    {
        empId,
        cityName: search

    },
    {
      deps: [search]
    }
  )
  const scrollStyle = getScrollStyle({ bottom: 120, top: 200 })
  const handleSumit = (item) => {
    setCity(item)
    if (item.cityName) {
      const obj: any = {
        cityId: item.cityId,
        cityName: item.cityName
      }
      Taro.eventCenter.trigger(eventName, obj)
      Taro.navigateBack()
      return
    } else {
      Taro.showToast({
        title: '请选择城市',
        icon: 'none'
      })
    }
  }
  useEffect(() => {
    if (result?.resultObj?.length === 0) {
      Taro.showToast({'title': '您当前在易才集团没有社保公积金订单，暂不能预约办理'})
      return 
    }
  }, [])
  return (
    <View className={styles.wrap}>
      <View className={styles.header}>
        <Text className={styles.header_title}>
          已选城市：
          <Text className={styles.header_city}>{city?.cityName || ''}</Text>
        </Text>
        <View className={styles.input_wrap}>
          <Input className={styles.input} placeholder='请输入城市名称' ref={inputRef} />
          <View
            className={styles.search}
            onClick={() => {
              const value = inputRef.current.tmpValue || inputRef.current.value
              setSearch(value)
            }}
          >
            <Icon size='20' type='search' color='#fff' />
          </View>
        </View>
      </View>
      <ScrollView style={scrollStyle} scrollY>
        {result?.resultObj?.length > 0 ? (result?.resultObj?.map(item => (
          <View key={item.cityId} className={styles.item} onClick={() => handleSumit(item)}>
            <Text className={styles.item_title}>{item.cityName}</Text>
          </View>
        ))) : (
          <View className={styles.empty}>
            <Image className={styles.no_data} src={no_data} />
          <Text className={styles.no_text}>您当前在易才集团没有社保公积金订单，暂不能查询。</Text>
        </View>
        )}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () => Taro.navigateBack()
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)