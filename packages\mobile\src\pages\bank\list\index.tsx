/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-08-31 14:05:13
 * @LastAuthor: 王正荣
 * @LastTime: 2021-11-26 16:25:52
 * @message:
 */
import { View, Image } from '@tarojs/components'
import bankbg from '@assets/bankCard/bankbg.png'
import { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import { getGlobalData } from '@utils/global-data'
import { users } from '@apis/users'
import { BottomBtn, withPage } from '@components'
import styles from './index.module.scss'

const Index = () => {
  const account = getGlobalData<'account'>('account')
  const { empId, openId: cmpToken } = account
  const [originListData, setOriginListData] = useState<Array<defs.users.GetBankcardInfo>>()
  const [listData, setListData] = useState<Array<defs.users.GetBankcardInfo>>()
  const getBankCard = async () => await users.getBankcardInfo.getBankcardInfo.request({ empId, cmpToken }).then(res => {
    console.log(res)
    if (res.data) {
      setOriginListData(res.data)
    }
    const infoList = res.data?.map(item => {
      return {
        ...item,
        bankAcct: '**** **** **** ' + (item.bankAcct?.substring(item.bankAcct?.length - 4, item.bankAcct?.length) || '')
      }
    })
    if (infoList) {
      setListData(infoList)
    }
  })

  useEffect(() => {
    getBankCard()
    Taro.eventCenter.on('bankCard', getBankCard)
    return () => {
      Taro.eventCenter.off('bankCard', getBankCard)
    }
  }, [])

  return (
    <View className={styles.wrap}>
      <View className={styles.sun_layout_header}>
        <View className={styles.box_ticket_item}>
          {listData?.map(item => (
            <View key={item.empBankCardId} className={styles.list_text}>
              <Image className={styles.itemColor} src={bankbg} />
              <View className={styles.list_text_item}>
                <View className={styles.widthd}>{item.bankName}</View>
              </View>
              <View className={styles.list_text_item}>
                <View className={styles.widthd1}>储蓄卡</View>
              </View>
              <View className={styles.list_text_item}>
                <View className={styles.widthd2}>{item.bankAcct}</View>
              </View>
            </View>
          ))}
        </View>
      </View>
      <BottomBtn
        btns={[
          {
            title: '更改信息',
            onClick: () => {
              Taro.navigateTo({
                url: '/pages/bank/edit/index?type=update&originListData=' + JSON.stringify(originListData)
              })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
