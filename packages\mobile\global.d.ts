/// <reference path="node_modules/@tarojs/plugin-platform-weapp/types/shims-weapp.d.ts" />

declare module "*.png";
declare module "*.gif";
declare module "*.jpg";
declare module "*.jpeg";
declare module "*.svg";
declare module "*.css";
declare module "*.less";
declare module "*.scss";
declare module "*.sass";
declare module "*.styl";

declare namespace NodeJS {
  interface ProcessEnv {
    TARO_ENV:
    | "weapp"
    | "swan"
    | "alipay"
    | "h5"
    | "rn"
    | "tt"
    | "quickapp"
    | "qq"
    | "jd";
    APP_ENV: "local" | "dev" | "test" | "prod";
  }
  interface Global {
    globalData: object;
  }
}

declare const IS_H5: boolean;
declare const IS_WEAPP: boolean;
declare const IS_RN: boolean;
declare const APP_ENV: "local" | "dev" | "test" | "prod";

// 扩展Taro请求类型
declare namespace Taro {
  namespace request {
    type CommonOption<P = any> = Omit<Taro.request.Option<P>, "url" | "data"> & {
       // default : true
       showLoading?:boolean;
       isToken?:boolean;
    };
    interface CommonUseRequestOption<P = any> extends CommonOption<P> {
      // 是否手动请求
      manual?: boolean;
      // 请求依赖
      deps?: any[];

    }
    // hooks请求返回结果
    interface CommonUseResultType<R = any, P = any> {
      loading: boolean;
      result: R | undefined;
      error: Error | undefined;
      run: (data?: P) => Promise<R>;
    }
  }
}


// 微信sdk
declare const wx: typeof WeixinJsSdk;
declare type POJO<T> = {
  [key: string]: T
}


declare const dd:any