.wrap {
  position: relative;
  background-color: #ffffff;
  width: 750px;
  flex: 1;
  /*  #ifndef rn */
  height: 100vh;
  /*  #endif  */
  display: flex;
  flex-direction: column;
  // overflow: hidden;

}
.center {
  justify-content: center;
  align-items: center;
}

:global {
  //进场前的瞬间
  .global-page-enter,
  .global-page-appear {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 100%;
    // opacity: 0;
    z-index: 1000;
  }
  //进场过程中
  .global-page-enter-active,
  .global-page-appear-active {
    // opacity: 1;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0%;
    transition: all 500ms;

  }
  //进场之后
  .global-page-enter-done {
    position: static;
    left: auto;
    z-index: 1000;

    // opacity: 1;
  }
  //离开前的瞬间
  // .global-page-exit{
  //     opacity: 1;
  // }
  //离开过程中
  // .global-page-exit-active{
  //     opacity: 0;
  //     transition: all 2000ms;
  // }
  //离开后
  // .global-page-exit-done{
  //     opacity: 0;
  // }
}

.error {
  width: 750px;
  padding: 30px;
  white-space: pre-wrap;
  word-break: break-all;
  word-wrap: break-word;
}
