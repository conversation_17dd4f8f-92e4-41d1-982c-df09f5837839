import { useRef, useState } from 'react'
import Taro from '@tarojs/taro'
import { withPage, ListView, usePagination } from '@components'
import { getScrollStyle } from '@utils'
import { Input, View, Text } from '@tarojs/components'
import { AtIcon } from 'taro-ui'
import classNames from 'classnames'
import { ncmp } from '@apis/ncmp'
import styles from './index.module.scss'

export const templateScopeMap = new Map<string, string>([
  ['1', '国家'],
  ['2', '省份'],
  ['3', '城市'],
  ['4', '特区']
])
const Index = () => {
  const { SELECTEDVALUES, CITYS, PROVINCE, SPECIALAREA, SEARCHNAME, selectedLevel1Value, selectedLevel2Value } = Taro.getCurrentInstance()?.preloadData || {}
  const [searchName, setSearchName] = useState<string>(SEARCHNAME)
  const searchRef = useRef<HTMLInputElement>(null)
  const scrollStyle = getScrollStyle({ bottom: 200, top: 0 })
  const list = usePagination(
    async page => {
      const currentYear = new Date().getFullYear()
      const params: any = {
        queryScopes: SELECTEDVALUES?.zCAttrs?.map(i => i.id) || [1, 2, 3, 4],
        infoStates: SELECTEDVALUES?.statusAttrs?.value ? [SELECTEDVALUES?.statusAttrs?.value] : [2],
        serviceTypes: SELECTEDVALUES?.serviceAttrs?.map(i => i.id) || [1, 2, 3],
        policyTemplateInfoYears: SELECTEDVALUES?.yearAttrs?.map(i => i.id) || [currentYear],
        level1Id: selectedLevel1Value?.id,
      }
      if (selectedLevel1Value?.id){
        params.level2Id = selectedLevel2Value?.id;
      }
      if (CITYS && CITYS?.length) {
        if (CITYS?.[0]?.isAllCity) {
          params.isAllCity = 1
        } else {
          params.isAllCity = 0
          params.cityIds = CITYS.map(i => +i.code)
        }
      }
      if (PROVINCE && PROVINCE?.length) {
        if (PROVINCE?.[0]?.isAllProvince) {
          params.isAllProvince = 1
        } else {
          params.isAllProvince = 0
          params.provinceIds = PROVINCE.map(i => i.KEY)
        }
      }
      if (SPECIALAREA && SPECIALAREA?.length) {
        if (SPECIALAREA?.[0]?.isAllSpecialArea) {
          params.isAllSpecialArea = 1
        } else {
          params.isAllSpecialArea = 0
          params.specialAreaIds = SPECIALAREA.map(i => i.KEY)
        }
      }
      const result: any = await ncmp.policyEncyclopedia.select.request({
        ...params,
        startIndex: String(page),
        endIndex: 10,
        templateInfoName: searchName,
      })
      if (!result.resultObj.rows.length){
        Taro.preload({
          ...(Taro.getCurrentInstance().preloadData || {}),
          selectedLevel1Value:{},
          selectedLevel2Value:{}
        })
      }
      return result.resultObj.rows
    },
    { deps: [searchName] }
  )
  const renderField = () => {
    return <Input placeholder='搜索政策' value={searchName} ref={searchRef} className={styles.input} onFocus={focus} />
  }
  const renderSearch = () => {
    return (
      <View className={styles.search_icon}>
        <AtIcon size={24} value='search' />
      </View>
    )
  }
  const handleClear = () => {
    return (
      <View onClick={(e: any) => clearaVal()}>
        <AtIcon size={24} value='close-circle' />
      </View>
    )
  }
  const clearaVal = () => {
    setSearchName('')
  }
  const handleNavigateTo = async (item: any) => {
    Taro.navigateTo({
      url: `/pages/search_result/index?policyTemplateId=${item.policyTemplateInfoId}&templateInfoName=${item.templateInfoName}`
    })
  }
  const renderSearchIcon = () => {
    return (
      <View onClick={handleSearch}>
        <AtIcon size={24} value='search' color='#fff' />
      </View>
    )
  }
  const handleSearch = () => {
    const value: any = searchRef.current?.value
    Taro.preload({
      ...(Taro.getCurrentInstance()?.preloadData || {}),
      SEARCHNAME: value
    })
    setSearchName(value)
  }

  const focus = (event: any) => {
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const { value } = event.target
    setSearchName(value)
  }
  const handleFilter = () => {
    Taro.navigateTo({
      url: `/pages/policy_filter/index`
    })
  }
  const handleMore = () => {
    Taro.preload({
      ...(Taro.getCurrentInstance().preloadData || {}),
      SEARCHNAME: searchRef.current?.value
    })
    Taro.navigateTo({
      url: `/pages/search_filter/index`
    })
  }
  const renderKeywords = (keywords, tip) => {
    const words = keywords?.length <= 19 ? keywords : keywords.substr(0, 19) + '...'
    const key = `${tip}` || ''
    const arr = `${words || ''}`.replace(new RegExp(key, 'g'), `%%${key}%%`).split('%%')
    return arr.map((v, i) => {
      return (
        <Text className={classNames(v === key ? styles.keywords_color : '')} key={i}>
          {v}
        </Text>
      )
    })
  }
  return (
    <View className={styles.page}>
      <View className={styles.wrap}>
        <View className={styles.input_box}>
          <View className={styles.input_search}>
            <View>{renderSearch()}</View>
            <View>{renderField()}</View>
            <View>{searchName && handleClear()}</View>
          </View>
          <View className={styles.input_btn}>{renderSearchIcon()}</View>
        </View>
        <View className={styles.search_filter}>
          <View className={styles.filter} onClick={handleFilter}>
            <View className={styles.font_size}>目录导航</View>
            <View className={styles.down_triangle}>
              {/* <AtIcon size={24} value='chevron-down' /> */}
            </View>
          </View>
          <View className={styles.filter} onClick={handleMore}>
            <View className={styles.font_size}>更多筛选</View>
            <View className={styles.down_triangle}>
              {/* <AtIcon size={24} value='chevron-down' /> */}
            </View>
          </View>
        </View>
      </View>
      <ListView
        refreshEventName='policyQuery'
        style={scrollStyle}
        itemSize={280}
        unlimitedSize
        renderItem={item => (
          <View className={styles.policy} key={item.policyTemplateInfoId} onClick={() => handleNavigateTo(item)}>
            <View className={styles.policy_t}>{renderKeywords(item.templateInfoName, searchName)}</View>
            <View
              className={styles.policy_y}
            >{`所属年份：${item.policyTemplateInfoYear}年  服务类型：${item.serviceTypeName}`}</View>
            <View className={styles.policy_opts}>
              {item?.provinceCity && <View className={styles.policy_c1}>{item?.provinceCity}</View>}
              <View className={styles.policy_c1}>{`${templateScopeMap.get(item?.templateScope)}政策`}</View>
            </View>
          </View>
        )}
        {...list}
      />
    </View>
  )
}

export default withPage(Index, { needLogin: false })
