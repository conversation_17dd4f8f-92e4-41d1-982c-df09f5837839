import { useState } from 'react'
import Taro, { useDidShow } from '@tarojs/taro'
import { ScrollView, View } from '@tarojs/components'
import { BottomBtn, withPage } from '@components'
import { ncmp } from '@apis/ncmp'
import { getScrollStyle } from '@utils'
import { templateScopeMap } from '../policy_query'
import styles from './index.module.scss'

const Index = () => {
  const [dataList, setDataList] = useState<any>([])
  const { params } = Taro.getCurrentInstance()?.router || {}
  const scrollStyle = getScrollStyle({ bottom: 120 })
  useDidShow(() => {
    const data = async () => {
      await ncmp.policyEncyclopedia.detail.request({ policyTemplateId: params?.policyTemplateId! }).then((res: any) => {
        if (res.code == 200) {
          setDataList(res.resultObj)
        }
      })
    }
    data()
  })
  const confirm = () => {
    Taro.navigateBack()
  }
  return (
    <View className={styles.wrap}>
      <ScrollView style={scrollStyle} scrollY>
        <View className={styles.templateInfoName}>
          <View>{params?.templateInfoName}</View>
          <View className={styles.templateScope}>
            <View className={styles.font_size}>适用范围:&nbsp;</View>
            <View className={styles.font_size}>
              {templateScopeMap.get(String(dataList?.templateScope) || '')}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </View>
            <View className={styles.font_size}>所属年份:&nbsp;</View>
            <View className={styles.font_size}>
              {`${dataList?.policyTemplateInfoYear || ''}年`}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </View>
            <View className={styles.font_size}>生效日期:&nbsp;</View>
            <View className={styles.font_size}>{`${dataList?.effectiveDate || ''}`}</View>
          </View>
          <View className={styles.serviceTypeName}>
            <View className={styles.font_size}>失效日期:&nbsp;</View>
            <View className={styles.font_size}>{`${dataList?.expirationDate || ''}`}&nbsp;&nbsp;</View>
            <View className={styles.font_size}>服务类型:&nbsp;</View>
            <View className={styles.font_size}>{`${dataList?.serviceTypeName || ''}`}</View>
          </View>
        </View>
        {dataList.templateGroupRespList?.map(item => {
          return (
            <View key={item.policyTemplateGroupId}>
              <View className={styles.policyTemplateGroup}>
                <View className={styles.policyTemplateIcon}></View>
                <View>{item.templateGroupName}</View>
              </View>
              <View>
                {item.fields?.map(it => {
                  return (
                    <View key={it.policyTemplateFieldId} className={styles.item}>
                      <View className={styles.fieldName}>{`${it.fieldName}:`}</View>
                      {it?.urlName ? (
                        <View className={styles.fieldUrlName}>
                          <View className={styles.urlName}>{it?.urlName || ''}</View>
                          <View className={styles.urlName}>{it?.fieldValue || ''}</View>
                        </View>
                      ) : (
                        <View className={styles.fieldValue}>{it.fieldValue}</View>
                      )}
                    </View>
                  )
                })}
              </View>
            </View>
          )
        })}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () => {
              confirm()
            }
          }
        ]}
      />
    </View>
  )
}
export default withPage(Index, {needLogin: false})
