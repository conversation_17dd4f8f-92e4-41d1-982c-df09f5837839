/**
 * @description getContinuingEduInfo
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** eduId */
  eduId: string;
}

export type Result = defs.users.ExecuteResult<defs.users.ContinuingEduInfo>;
export const path = '/user-server/api/getContinuingEduInfo';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
