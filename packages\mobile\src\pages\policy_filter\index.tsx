import { useEffect, useState } from 'react'
import { useDidShow } from '@tarojs/taro'
import { withPage } from '@components/page'
import { ScrollView, View, Image, Text } from '@tarojs/components'
import BottomBtn from '@components/bottom-btn'
import classNames from 'classnames'
import { ncmp } from '@apis/ncmp'
import no_data from '@assets/no-data.png'
import styles from './index.module.scss'

const Index = () => {
  const { SELECTEDVALUES, CITYS, PROVINCE, SPECIALAREA, SEARCHNAME, selectedLevel1Value: data1, selectedLevel2Value: data2} = Taro.getCurrentInstance()?.preloadData || {}
  const [firstLevel, setFirstLevel] = useState<any>([])
  const [secondLevel, setSecondLevel] = useState<any>([])
  const [selectedLevel1Value, setSelectedLevel1Value] = useState<any>(data1)
  const [selectedLevel2Value, setSelectedLevel2Value] = useState<any>(data2)
  useDidShow(() => {
    const data = async () => {
      try {
        const currentYear = new Date().getFullYear()
        const params: any = {
          queryScopes: SELECTEDVALUES?.zCAttrs?.map(i => i.id) || [1, 2, 3, 4],
          infoStates: SELECTEDVALUES?.statusAttrs?.value ? [SELECTEDVALUES?.statusAttrs?.value] : [2],
          serviceTypes: SELECTEDVALUES?.serviceAttrs?.map(i => i.id) || [1, 2, 3],
          policyTemplateInfoYears: SELECTEDVALUES?.yearAttrs?.map(i => i.id) || [currentYear]
        }
        if (CITYS && CITYS?.length) {
          if (CITYS?.[0]?.isAllCity) {
            params.isAllCity = 1
          } else {
            params.isAllCity = 0
            params.cityIds = CITYS.map(i => +i.code)
          }
        }
        if (PROVINCE && PROVINCE?.length) {
          if (PROVINCE?.[0]?.isAllProvince) {
            params.isAllProvince = 1
          } else {
            params.isAllProvince = 0
            params.provinceIds = PROVINCE.map(i => i.KEY)
          }
        }
        if (SPECIALAREA && SPECIALAREA?.length) {
          if (SPECIALAREA?.[0]?.isAllSpecialArea) {
            params.isAllSpecialArea = 1
          } else {
            params.isAllSpecialArea = 0
            params.specialAreaIds = SPECIALAREA.map(i => i.KEY)
          }
        }
        const res:any = await ncmp.policyEncyclopedia.getPolicyLevelTree.request({
          ...params,
          templateInfoName: SEARCHNAME,
        })
        if (res.code === '200') {
          res.resultObj?.length > 0 && res.resultObj?.unshift({ name: '全部', id: '' })
          setFirstLevel(res.resultObj)
        } else {
          Taro.showToast({ title: '系统异常，请稍后重试!', icon: 'none' })
        }
      } catch (error) {
        Taro.showToast({ title: '系统异常，请稍后重试!', icon: 'none' })
      }
    }
    data()
  })
  useEffect(() => {
    if (!selectedLevel1Value?.children?.length){
      return;
    }
    const d = [{ name: '全部', id: '' }].concat(selectedLevel1Value?.children)
    setTimeout(() => {
      setSecondLevel(d)
    }, 300)
  },[])
  const handleFirstLevelOnClick = (item) => {
    setSelectedLevel1Value(item)
    setSelectedLevel2Value(undefined)
    const children = item.children?.reduce((acc, pre) => {
      acc.push(pre)
      return acc
    }, [])
    children?.length > 0 && children?.unshift({ name: '全部', id: '' })
    setSecondLevel(children)
  }
  const handleSecondLevelOnClick = (item) => {
    setSelectedLevel2Value(item)
  }
  return (
    <View className={styles.policy_filter}>
      <ScrollView className={styles.firstLevel} scrollY>
        {firstLevel?.length > 0 ? (
          firstLevel?.map((item:any) => (
            <View
              key={item.id}
              className={classNames(styles.opts_container, selectedLevel1Value?.id === item.id && styles.active)}
              onClick={() => handleFirstLevelOnClick(item)}
            >
              {item.name}
            </View>
          ))
        ) : (
          <View className={styles.empty}>
            <Image className={styles.no_data} src={no_data} />
            <Text className={styles.no_text}>暂无可选数据</Text>
          </View>
        )}
      </ScrollView>
      <ScrollView className={styles.secondLevel} scrollY>
        {secondLevel?.length > 0 ? (
          secondLevel?.map((item:any) => (
            <View
              key={item?.id}
              className={classNames(styles.opts_container, selectedLevel2Value?.id === item?.id && styles.active)}
              onClick={() => handleSecondLevelOnClick(item)}
            >
              {item?.name}
            </View>
          ))
        ) : (
          <View className={styles.empty}>
            <Image className={styles.no_data} src={no_data} />
            <Text className={styles.no_text}>暂无可选数据</Text>
          </View>
        )}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () => {
              Taro.navigateBack()
            }
          },
          {
            title: '确定',
            onClick: () => {
              Taro.preload({
                ...(Taro.getCurrentInstance().preloadData || {}),
                selectedLevel1Value,
                selectedLevel2Value
              })
              Taro.navigateTo({
                url: `/pages/policy_query/index`
              })
            }
          }
        ]}
      />
    </View>
  )
}
export default withPage(Index)
