/**
 * @description 查询汇算列表
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.hss.GeneralRespBean<Array<defs.hss.HsData>>;
export const path = '/yc-hs/hsInfo/getDataListByOpenid';
export const method = 'POST';
export const request = (data: object, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: object,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
