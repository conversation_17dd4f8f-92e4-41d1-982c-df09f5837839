/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-15 14:25:21
 * @LastAuthor: 王正荣
 * @LastTime: 2021-09-18 10:56:02
 * @message: 
 */
import { View, ScrollView } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { getGlobalData, getScrollStyle } from '@utils'
import { pact } from '@apis/pact'
import { BottomBtn, withPage, Labels, FormLabel } from '@components'
import Taro, { useRouter } from '@tarojs/taro'
import styles from './index.module.scss'

const Index = () => {
  const { openId, empId } = getGlobalData<'account'>('account')
  const { data } = useRouter<{ data: string }>().params
  const { sendMonth, classId, sendId } = JSON.parse(data)
  const [list, setList] = useState<Array<defs.pact.WageDetailListPayBean>>()
  const scrollStyle = getScrollStyle({ bottom: 120 })

  useEffect(() => {
    pact.busi.getPersonWageDetailList
      .request({
        openId,
        empId,
        sendMonth,
        classId,
        sendId
      })
      .then(res => {
        setList(res.data)
      })
  }, [empId, openId, sendMonth, classId, sendId])
  return (
    <View>
      <ScrollView style={scrollStyle} scrollY>
        <View>
          <FormLabel level={2} title='收入项' />
          {list?.map((item) => {
            if (item.paytype === '0') {
              return <Labels title={item.payname} detail={item.payamt} />
            }
          })}
        </View>
        <View className={styles.group}>
          <FormLabel level={2} title='扣减项' />
          {list?.map((item) => {
            if (item.paytype === '1') {
              return <Labels title={item.payname} detail={item.payamt} />
            }
          })}
        </View>
        <View className={styles.group}>
          <FormLabel level={2} title='其他项' />
          {list?.map((item) => {
            if (item.paytype === '2') {
              return <Labels title={item.payname} detail={item.payamt} />
            }
          })}
        </View>
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回工资列表',
            onClick: () => {
              Taro.navigateBack({ delta: 1 })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
