/**
 * @description 新用户注册
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openId */
  openId?: string;
}

export type Result = defs.pact.FollowResponse;
export const path = '/yc-wepact-mobile/login/follow';
export const method = 'POST';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
