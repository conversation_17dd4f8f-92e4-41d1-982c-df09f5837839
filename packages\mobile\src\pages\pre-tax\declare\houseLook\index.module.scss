.wrap {
  width: 100%;
  background-color: #ffffff;
  flex: 1;
  position: relative;
  font-size: 28px;
}
.group_item {
  background: #fff;
  margin: 10px 0;
}
.title_secondary {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 28px 30px;
  height: 97px;
  border: solid 1px #efeff4;
  font-weight: 400;
  position: relative;
}
.title_mark {
  position: absolute;
  left: 0;
  width: 14px;
  height: 24px;
  margin: 10px 0;
  background: #b51e25;
}
.addSupport {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.btn_wrap {
  width: 160px;
  height: 60px;
  background-color: #b51f24;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 10px 0 0;
}
.btn {
  color: #fff;
  font-size: 22px;
}
.chilrendlist {
  display: flex;
  align-items: center;
  justify-content: space-around;
  border:0 solid #ccc;
  border-bottom-width: 1px;
}
.btns {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: row;
  width: 300px;
}

.infoUl {
  width: 100%;
  padding: 20px 20px 20px 20px;
  border: 0 solid #ccc;
  border-bottom-width: 1px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.names {
  width: 200px;
}
