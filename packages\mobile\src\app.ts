import * as Sentry from '@sentry/react'
import './app.scss'

if (IS_H5) {
  // sessionStorage.clear();
  // if (APP_ENV === 'prod') {
  //   console.log = () => {}
  //   console.group = () => {}
  // }
  // && APP_ENV !== 'local'
  if (APP_ENV !== 'prod' && APP_ENV !== 'local') {
    const VConsole = require('vconsole')
    new VConsole()
  }
  // const Sentry = require('@sentry/react')
  // const Tracing = require('@sentry/tracing')
  // const Integrations = Tracing.Integrations
  Sentry.init({
    dsn: 'http://723b56d75ee742d58ec5513dfe2bc07e@180.169.79.83:9000/2',
    // integrations: [new Integrations.BrowserTracing()],
    tracesSampleRate: 1.0,
    environment: APP_ENV,
    release: `nwehr@1.0.0`,
    beforeSend(event, hint) {
      const NonErrorExceptionString = 'Non-Error exception captured with keys: currentTarget, detail, isTrusted'
      const isError = (type: string) =>
        event.exception?.values?.[0]?.value?.startsWith(type) || hint?.originalException?.['message']?.startsWith(type)
      if (isError(NonErrorExceptionString)) {
        return null
      }
      if (isError('ChunkLoadError: Loading chunk')) {
        // 资源加载失败
        event.message = '资源加载失败'
        // window.location.reload()
        return event
      }
      return event
    }
  })
  console.log('Sentry 初始化成功--', APP_ENV)
}

const App = props => {
  return props.children
}

export default App
