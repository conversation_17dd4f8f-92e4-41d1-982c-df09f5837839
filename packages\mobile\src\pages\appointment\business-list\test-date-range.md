# 业务列表日期范围优化测试

## 修改内容

### 1. 主要问题修复
- 修复了 `fetchBusinessList` 函数中硬编码使用 `DEFAULT_DATE_RANGE` 的问题
- 现在正确使用用户选择的日期范围 `dateRange.startDate` 和 `dateRange.endDate`
- 更新了状态管理，使用 `dateRange` 对象替代 `createDt` 字符串

### 2. DateSelect 组件优化
- 修复了 "全部" 选项设置空字符串的问题
- 现在 "全部" 选项设置为从5年前到今天的日期范围
- 添加了适当的注释说明

### 3. 日期范围映射

| 选项 | 开始日期 | 结束日期 | 说明 |
|------|----------|----------|------|
| 最近三个月 | 当前日期 - 3个月 + 1天 | 当前日期 | 默认选项 |
| 最近六个月 | 当前日期 - 6个月 + 1天 | 当前日期 | 页面初始化默认选择 |
| 最近一年 | 当前日期 - 12个月 + 1天 | 当前日期 | |
| 全部 | 当前日期 - 5年 | 当前日期 | 覆盖大部分历史数据 |

## 测试场景

### 1. 功能测试
- [ ] 选择 "最近三个月" 时，API 调用参数正确
- [ ] 选择 "最近六个月" 时，API 调用参数正确
- [ ] 选择 "最近一年" 时，API 调用参数正确
- [ ] 选择 "全部" 时，API 调用参数正确
- [ ] 手动选择日期范围时，API 调用参数正确

### 2. 界面测试
- [ ] 页面初始化时默认选择 "最近六个月"
- [ ] 切换不同选项时，列表数据正确刷新
- [ ] 日期选择器显示正确的日期值

### 3. 边界测试
- [ ] 开始日期大于结束日期时的处理
- [ ] 网络错误时的错误处理
- [ ] 空数据时的显示

## 代码变更摘要

### packages/mobile/src/pages/appointment/business-list/index.tsx
```typescript
// 修改前
const [createDt, setCreateDt] = useState(`${DEFAULT_DATE_RANGE.startDate},${DEFAULT_DATE_RANGE.endDate}`)
const result = await ncmp.ebmBusiness.selectEbmBusinessList.request({
  startDt: DEFAULT_DATE_RANGE.startDate,
  endDt: DEFAULT_DATE_RANGE.endDate
})

// 修改后
const [dateRange, setDateRange] = useState({
  startDate: DEFAULT_DATE_RANGE.startDate,
  endDate: DEFAULT_DATE_RANGE.endDate
})
const result = await ncmp.ebmBusiness.selectEbmBusinessList.request({
  startDt: dateRange.startDate,
  endDt: dateRange.endDate
})
```

### packages/mobile/src/components/time-picker/page-date.tsx
```typescript
// 修改前
} else if (key === '4') {
  endDate = ''
  startDate = ''
}

// 修改后
} else if (key === '4') {
  // 全部：设置一个较大的日期范围，比如从5年前到今天
  startDate = dayjs()
    .subtract(5, 'years')
    .format(stdDateFormat)
  endDate = dayjs().format(stdDateFormat)
}
```
