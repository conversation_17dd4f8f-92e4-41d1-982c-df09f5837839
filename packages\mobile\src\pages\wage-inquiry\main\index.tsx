/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-08-17 10:11:12
 * @LastAuthor: 王正荣
 * @LastTime: 2021-11-26 17:23:03
 * @message:
 */
import { View, Text } from '@tarojs/components'
import { Fragment, useEffect, useState } from 'react'
import { getGlobalData, getScrollStyle } from '@utils'
import { pact } from '@apis/pact'
import { ListView, usePagination, withPage, Buttons, DateYearMonthWage, Modal } from '@components'
import dayjs from 'dayjs'
import Taro, { useRouter } from '@tarojs/taro'
import styles from './index.module.scss'

const itemConfig = [
  { title: '所属月份', key: 'sendMonth' },
  // { title: '客户名称', key: 'custName' },
  { title: '收入合计', key: 'f1' },
  { title: '扣款合计', key: 'f2' },
  { title: '实发合计', key: 'f3' },
  { title: '本次扣税', key: 'f10' },
  { title: '发放状态', key: 'wageBatchStatus' }
]
const Index = () => {
  const { openId, empId } = getGlobalData<'account'>('account')
  // const { type } = useRouter<{ type: 'social-security' | 'accumulation' }>().params
  const scrollStyle = getScrollStyle({ top: 286 })
  const [visible, setVisible] = useState(false)
  const [responseMessage, setResponseMessage] = useState('')
  const [showData, setShowData] = useState<Object>()
  const [month, setMonth] = useState<{ startMonth: string; endMonth: string }>({ startMonth: '', endMonth: '' })
  const {sendMonth: M, empId: employeeId} = useRouter<{ sendMonth: string, empId: string}>().params
  const list = usePagination(
    async page => {
      const total = await pact.busi.getPersonTotalWages.request({
        openId,
        empId:employeeId ?? empId,
        startMonth: Number(dayjs(month.startMonth).format('YYYYMM')),
        endMonth: Number(dayjs(month.endMonth).format('YYYYMM'))
      })
      setShowData({
        f1: total.data?.f1,
        f2: total.data?.f2,
        f3: total.data?.f3,
        f10: total.data?.f10,
      })
      const result = await pact.busi.getPersonWages.request({
        openId,
        empId: employeeId ?? empId,
        pageNum: page,
        pageSize: 5,
        startMonth: Number(dayjs(month.startMonth).format('YYYYMM')),
        endMonth: Number(dayjs(month.endMonth).format('YYYYMM'))
      })
      if (result.message) {
        setVisible(true)
        setResponseMessage('您所在公司未开通工资查询权限，如有疑问请与贵公司人事联系！')
      } else {
        return result.data
      }
    },
    { deps: [month], auto: !!month.startMonth }
  )
  const detail = (classId: string, sendId: string, sendMonth: string) => {
    const data = { classId, sendId, sendMonth }
    Taro.navigateTo({ url: `/pages/wage-inquiry/detail/index?data=${JSON.stringify(data)}` })
  }
  return (
    <Fragment>
      <DateYearMonthWage
        onSelectHandle={e => {
          const { startDate, endDate } = e
          setMonth({ startMonth: startDate, endMonth: endDate })
        }}
        pickerProps={[{ fields: 'month' }, { fields: 'month' }]}
        showData={showData}
        sendMonth={M}
      />
      <ListView
        style={scrollStyle}
        itemSize={566}
        renderItem={item => (
          <View className={styles.item}>
            {itemConfig.map(config => (
              <View className={styles.text_wrap} key={config.key}>
                <Text className={styles.title}>{config.title}</Text>
                <Text className={styles.detail}>{item[config.key]}</Text>
              </View>
            ))}
            <View className={styles.btn_wrap}>
              <Buttons
                title='查看明细'
                icon='detail'
                onClick={() => detail(item.classId, item.sendId, item.sendMonth)}
              />
            </View>
          </View>
        )}
        {...list}
      />
      <Modal
        visible={visible}
        title='提示'
        onConfirm={() => {
          setVisible(false)
        }}
        content={responseMessage}
      />
    </Fragment>
  )
}

export default withPage(Index)
