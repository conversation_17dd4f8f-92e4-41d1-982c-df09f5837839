/**
 * @description 政策详情
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** policyTemplateId */
  policyTemplateId: string;
}

export type Result = defs.ncmp.HttpResult<defs.ncmp.PolicyDetail>;
export const path = '/wx-ncmp/policyEncyclopedia/detail';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
