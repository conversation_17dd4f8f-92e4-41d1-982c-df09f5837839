import { Fragment, useEffect, useState, useRef } from 'react'
import { View } from '@tarojs/components'
import { Form, BottomBtn, useForm, withPage, FormTip } from '@components'
import { getGlobalData, BaseUrl } from '@utils'
import { isEmpty } from 'lodash'
import { useRouter } from '@tarojs/taro'
import { ncmp } from '@apis/ncmp'
import pickBy from 'lodash/pickBy'
import { getScrollStyle } from '@utils/transforms'
import { pact } from '@apis/pact'
import { getColumns, FormItemType } from './biz'
import styles from './index.module.scss'
import { scrollToAnchor } from './biz_beijing'


const Index = () => {
  const [items, setItems] = useState<FormItemType[]>([])
  const [otherItems, setOtherItems] = useState<FormItemType[]>([])
  const [regionData, setRegionData] = useState<any[]>([])
  const [subDistrictData, setSubDistrictData] = useState<any[]>([])
  const [regionData2, setRegionData2] = useState<any[]>([])
  const [subDistrictData2, setSubDistrictData2] = useState<any[]>([])
  const ref = useRef<FormItemType[]>([])
  const address1 = useRef('')
  const address2 = useRef('')
  const huKouAddress1 = useRef('')
  const huKouAddress2 = useRef('')
  const { cityCode, uuid, businessId, flag} = useRouter().params
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const { openId, accountId, empId } = getGlobalData<'account'>('account')
  const form = useForm()
  const isBeijingPaid = form.watch('isBeijingPaid')
  const hasEmpolymentCert = form.watch('hasEmpolymentCert')
  const educationLevel = form.watch('educationLevel')
  const cultureSuzhou = form.watch('culture_suzhou')
  const residenceProvinceCity = form.watch('residenceProvinceCity')
  const hukouProvinceCity = form.watch('hukouProvinceCity')
  const region = form.watch('region')
  const subDistrict = form.watch('subDistrict')
  const hukouRegion = form.watch('hukouRegion')
  const hukouSubDistrict = form.watch('hukouSubDistrict')
  const cityss = [
    { key: '10717', value: '10742' },
    { key: '10708', value: '10740' },
    { key: '10709', value: '10743' },
    { key: '10738', value: '10744' }
  ]
  useEffect(() => {
    if (['大学本科', '大学专科'].includes(cultureSuzhou)) {
      form.setValue('graduationSchool', '')
      form.setValue('graduationDate', '')
      form.setValue('professional', '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cultureSuzhou])
  useEffect(() => {
    if (!residenceProvinceCity) return
    const [v1, v2] = residenceProvinceCity?.split(',')
    form.setValue('region', '')
    form.setValue('subDistrict', '')
    setSubDistrictData([])
    let v3
    if (!v2) {
      let cityVal = cityss.find(it => it.key === v1)
      v3 = cityVal?.value
    }
    getByCode(v2 || v3, setRegionData)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [residenceProvinceCity])

  useEffect(() => {
    if (!hukouProvinceCity) return
    const [v1, v2] = hukouProvinceCity?.split(',')
    form.setValue('hukouRegion', '')
    form.setValue('hukouSubDistrict', '')
    setSubDistrictData2([])
    let v3
    if (!v2) {
      let cityVal = cityss.find(it => it.key === v1)
      v3 = cityVal?.value
    }
    getByCode(v2 || v3, setRegionData2)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hukouProvinceCity])

  useEffect(() => {
    if (!region) return
    getByCode(region, setSubDistrictData)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [region])

  useEffect(() => {
    if (!hukouRegion) return
    getByCode(hukouRegion, setSubDistrictData2)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hukouRegion])

  useEffect(() => {
    if (!region) return
    const regionName = regionData.find(i => i.key === region)
    address1.current = regionName?.value === '暂不选择' ? '' : regionName?.value
    form.setValue('residentAddress', `${address1.current || ''}`)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [region])
  useEffect(() => {
    if (!subDistrict) return
    const streetName = subDistrictData.find(i => i.key === subDistrict)
    address2.current = streetName?.value === '暂不选择' ? '' : streetName?.value
    form.setValue('residentAddress', `${address1.current || ''}${address2.current || ''}`)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subDistrict])

  useEffect(() => {
    if (!hukouRegion) return
    const regionName = regionData2.find(i => i.key === hukouRegion)
    huKouAddress1.current = regionName?.value === '暂不选择' ? '' : regionName?.value
    form.setValue('hukouAddress', `${huKouAddress1.current || ''}`)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hukouRegion])

  useEffect(() => {
    if (!hukouSubDistrict) return
    const streetName = subDistrictData2.find(i => i.key === hukouSubDistrict)
    huKouAddress2.current = streetName?.value === '暂不选择' ? '' : streetName?.value
    form.setValue('hukouAddress', `${huKouAddress1.current || ''}${huKouAddress2.current || ''}`)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hukouSubDistrict])
  const getByCode = async (code, setState) => {
    try {
      await Taro.request({
        url: `${BaseUrl}/region/regionInfo/getByCode`,
        data: { code: code },
        success: res => {
          const data = res.data.data.reduce(
            (acc, cur) => {
              if (cur.code) {
                const item = { key: cur.code, value: cur.name }
                acc.unshift(item)
              }
              return acc
            },
            [{ key: '9999999', value: '暂不选择' }]
          )
          setState(data)
        }
      })
    } catch (error) {
      Taro.showToast({ icon: 'none', title: '系统异常' })
    }
  }
  useEffect(() => {
    // {11: "博士"}, {12: "硕士"}, {21: "大学"}, {31: "大专"} 4: {40: "中专"}{50: "技校"}{61: "高中"}{62: "职高"}{70: "初中"}{80: "小学"}{90: "文盲或半文盲"}
    // 天津特殊处理
    if (cityCode !== '10743') return
    if (['40', '50', '61', '62', '70', '80', '90'].includes(educationLevel)) {
      form.setValue('joinWorkDate', '')
      form.setValue('householdRegistrationDate', '')
      const subList = ref.current?.[0]?.subList || []
      const data = subList?.filter(item => ['joinWorkDate', 'householdRegistrationDate'].includes(item.code))
      setOtherItems([{ ...otherItems?.[0], subList: data }])
    } else {
      ref.current && setOtherItems(ref.current as FormItemType[])
      form.setValue('graduationSchool', '')
      form.setValue('graduationDate', '')
      form.setValue('professional', '')
      form.setValue('joinWorkDate', '')
      form.setValue('householdRegistrationDate', '')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasEmpolymentCert, educationLevel])
  useEffect(() => {
    pact.entry.echoFileSubmit
      .request({
        newBusId: uuid,
        oldBusId: businessId,
        accountId,
        openId,
        cityCode
      })
      .then(res => {
        if (res.code === '200') {
          flag === '0' && delEntryFileWithHRO()
          form.setValue('empName', res.name)
          form.setValue('idCardNum', res.idCard)
          form.setValue('gender', res.sex)
          form.setValue('validStart', res.validStart)
          form.setValue('validEnd', res.validEnd)
          form.setValue('accountName_chifeng', res.name)
          form.setValue('accountName_baotou', res.name)
        }
      })
    pact.cityDynamicsForm.getDynamicItem.request({ cityCode, openId, pageNo: 1 }).then(res => {
      handleData(res)
      setItems(res as any)
    })
    if (cityCode === '10740' || cityCode === '10743') {
      pact.cityDynamicsForm.getDynamicItem.request({ cityCode, openId, pageNo: 2 }).then(res => {
        ref.current = res as any
        handleData(res)
        setOtherItems(res as any)
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const delEntryFileWithHRO = async () => {
    await pact.entry.delEntryFileWithHro.request({
      accountId,
      businessId: uuid,
      cityCode,
      openId,
      type: 'baseEndoInsCert_shaanxi'
    })
  }
  useEffect(() => {
    if (Number(isBeijingPaid)) return
    setTimeout(() => {
      scrollToAnchor('#scrollTop')
    }, 500)
    form.setValue('hukouzipcode_beijing', '100022')
    form.setValue('residentzipcode_beijing', '100022')
  }, [form, isBeijingPaid])
  const handleData = (res:any) => {
    res.length && res?.map((item) => {
      if (item.code === 'residenceProvinceCity'){
        form.register('residenceProvinceCity', {value: item.itemValue})
      }
      if (item.code === 'hukouProvinceCity'){
        form.register('hukouProvinceCity', {value: item.itemValue})
      }
      !isEmpty(item.subList) && handleData(item.subList)
    })
  }
  const onSubmit = data => {
    const code = ['10740', '10743', '10742', '10744']
    const [v1, v2] = data?.residenceProvinceCity?.split(',') || []
    const [v3, v4] = data?.hukouProvinceCity?.split(',') || []
    if (String(data?.idCardNum).length > 25) {
      Taro.showToast({ title: '请输入正确的身份证号', icon: 'none' })
      return
    }
    if (code.includes(v2)) {
      data.residenceProvinceCity = v1
    }
    if (code.includes(v4)) {
      data.hukouProvinceCity = v3
    }
    const formContent = JSON.stringify({
      ...pickBy({
        ...data,
        accountId,
        cityId: cityCode,
        uuid,
      })
    })
    pact.entry.insertEntryInfo
      .request({
        cityCode,
        openid: openId,
        businessId: uuid,
        formContent: formContent
      })
      .then(res => {
        if (res.code === '1') {
          getSignEleContract('2')
        } else {
          Taro.showToast({ title: res.message || '系统异常, 请稍后重试', icon: 'none' })
        }
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  }
  const onError = (errors, e) => {
    if (!e) {
      Taro.showToast({ title: '请完善必填项信息', icon: 'none' })
    }
  }
  const getSignEleContract = (eleContractStatus: string) => {
    ncmp.elecSign.getSignEleContract
      .request(
        {
          empId,
          eleContractStatus
        },
        { isToken: true }
      )
      .then((res: any) => {
        if (res.code === '200') {
          if (res?.resultObj && res?.resultObj?.eleSinUrl) {
            window.location.href = res?.resultObj?.eleSinUrl || ''
            return
          }
          Taro.navigateTo({ url: '/pages/induction/handle/success/index' })
        }
      })
  }
  return (
    <Fragment>
      <View className={styles.tip}>
        <FormTip tip='以下内容带星号为必填' />
      </View>
      <Form
        form={form}
        columns={getColumns(
          items,
          otherItems,
          cityCode,
          form,
          () => null,
          regionData,
          subDistrictData,
          regionData2,
          subDistrictData2
        )}
        style={scrollStyle}
      />
      <BottomBtn btns={[{ title: '提交', onClick: form.handleSubmit(onSubmit, onError) }]} />
    </Fragment>
  )
}

export default withPage(Index)
