import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleBinZhouColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const culture_binzhou = form.watch('educationLevel')

  if (column.name === 'graduationSchool_binzhou') {
    return { ...column, isHidden: !['11','12','21','31'].includes(culture_binzhou)}
  }
  return column
}

export { handleBinZhouColumn }
