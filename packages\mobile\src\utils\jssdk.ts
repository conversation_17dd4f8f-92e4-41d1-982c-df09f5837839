import { getSignature } from '@apis/pact/mods/wxConfig'
import { isComWx, isWx, request } from '@utils'

/** 获取签名 */
export const chooseImage = (opt = {} as any) => {
  const url = window.location.href.split('#')[0]
  // const requestUrl = isWx() ? `${opt.url}?url=${url}` : `${opt.url}?cropId=${opt.cropId}&pageUrl=${url}`
  request(`${opt.url}?cropId=${opt.cropId}&pageUrl=${url}`).then((res) => {
    if (isWx()) {
      const Data = (res || {}) as any
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: Data.appid, // 必填，公众号的唯一标识
        timestamp: Data.timestamp, // 必填，生成签名的时间戳
        nonceStr: Data.noncestr, // 必填，生成签名的随机串
        signature: Data.signature, // 必填，签名，见附录1
        jsApiList: ['checkJsApi', 'chooseImage', 'uploadImage'],
      })
      wx.error((error) => {
        console.log('error---', error)
      })
    } else if (isComWx()) {
      const Data = (res.data || {}) as any
      wx.agentConfig({
        corpid: opt.cropId, // 必填，企业微信的corpid，必须与当前登录的企业一致
        agentid: '1000185', // 必填，企业微信的应用id （e.g. 1000247）
        timestamp: Data.timestamp, // 必填，生成签名的时间戳
        nonceStr: Data.noncestr, // 必填，生成签名的随机串
        signature: Data.signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
        jsApiList: ['checkJsApi', 'chooseImage', 'uploadImage'], //必填，传入需要使用的接口名称
        success: function (res) {
          // 回调
        },
        fail: function (res) {
          console.log('res.errMsg', res.errMsg)
          // if(res.errMsg.indexOf('function not exist') > -1){
          //     alert('版本过低请升级')
          // }
        },
      })
    } else {
      /**钉钉SDK */
    }
  })
}

export const closeWindow = () => {
  /**关闭当前窗口 */
  wx.closeWindow()
}

export const WxConfig = (): Promise<any> => {
  return new Promise(function (resolve, reject) {
    getSignature.request({ url: window.location.href.split('#')[0] }).then((res: any) => {
      wx.config({
        debug: false,
        appId: res?.appid,
        timestamp: res?.timestamp,
        nonceStr: res?.noncestr,
        signature: res?.signature,
        jsApiList: ['getLocation'],
      })

      wx.error((error) => {
        console.log('error---', error)
        reject(error)
      })

      wx.ready(function () {
        resolve({})
      })
    })
  })
}
