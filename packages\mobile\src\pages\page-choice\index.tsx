import { Fragment } from 'react'
import { withPage } from '@components'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import CheckboxContent from './components/checkbox-content'
import AppointmentMaterial from './components/appointment-material'
import { PageParams } from './components/type'

const Index = () => {
  const { title, scen, ...rest } = useRouter<PageParams>().params || {}

  useDidShow(() => {
    title && Taro.setNavigationBarTitle({ title })
  })
  if (scen === 'AppointmentBusType') {
    return <CheckboxContent {...rest} />
  } else if (scen === 'AppointmentMaterial') {
    return <AppointmentMaterial {...rest} />
  }
  return <Fragment />
}

export default withPage(Index)
