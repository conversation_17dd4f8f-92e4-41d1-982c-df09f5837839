/**
 * @description 同时删除微信和hro的入职信息文件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** accountId */
  accountId?: string;
  /** 同一个入职办理数据中文件的busUUID相同 */
  businessId?: string;
  /** cityCode */
  cityCode?: string;
  /** openId */
  openId?: string;
  /** 文件类型 */
  type?: string;
}

export type Result = defs.pact.EntryResponseBean;
export const path = '/yc-wepact-mobile/entry/delEntryFileWithHRO';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
