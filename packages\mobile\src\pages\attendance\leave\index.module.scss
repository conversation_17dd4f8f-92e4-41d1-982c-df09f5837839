@import "~taro-ui/dist/style/components/tag.scss";
@import "~taro-ui/dist/style/components/icon.scss";

.apply {
  display: flex;
  justify-content: space-around;
  padding: 20px;

  .item {
    width: 300px;
    height: 260px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px solid #efefef;
    box-shadow: 10px 5px 5px #efefef;
    margin-bottom: 20px;

    .img {
      width: 100px;
      height: 100px;
      margin-right: 20px;
    }
  }
}

.tags {
  padding: 20px;

  .item {
    margin-right: 10px;
  }
}

.list {
  .item {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    font-size: 10px;
    color: #888;

    .img {
      width: 100px;
      height: 100px;
    }

    .detail {
      flex: 1;
      padding: 0 10px;

      .title {
        color: #333;
        font-size: 14px;
        display: flex;
        align-items: center;
      }
.tag {
  font-size: 20px;
  padding: 3px 10px 1px;
  border: 1px solid #6190E8;
  color: #6190E8;
  border-radius: 6px;
}
    }
  }
}