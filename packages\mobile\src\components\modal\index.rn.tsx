import Modal from "@ant-design/react-native/lib/modal";
import {} from "@ant-design/react-native/lib/style";

// // import { Modal } from "react-native";
// import { View } from "@tarojs/components";
// import Modal from 'react-native-modal';
import { ComponentOptions } from "@tarojs/taro";
import { FunctionComponent } from "react";
import { TaroModalProps } from "./type";
import Content from "./content";

const TaroModal: FunctionComponent<TaroModalProps> & {
  options?: ComponentOptions;
} = props => {
  const { visible = false, ...rest } = props;
  return (
    <Modal
      closable={false}
      bodyStyle={{ paddingBottom: 0,paddingHorizontal:0}}
      style={{ overflow: 'visible', paddingTop: 0 ,with:500,marginLeft: '-16%'}}
      maskClosable={false}
      transparent
      animationType='fade'
      visible={visible}
    >
      <Content {...rest} />
    </Modal>
  );
};

TaroModal.options = {
  addGlobalClass: true
};

export default TaroModal;
