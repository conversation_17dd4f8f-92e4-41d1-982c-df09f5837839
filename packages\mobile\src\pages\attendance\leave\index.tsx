
import { useEffect, useState } from 'react'
import Taro, { useRouter } from '@tarojs/taro'
import { View, Image, Text } from '@tarojs/components'
import { ListView, usePagination, withPage } from '@components'
import { AtIcon, AtTabs, AtTabsPane, AtTag } from 'taro-ui'
import { ncmp } from '@apis/ncmp'

import styles from './index.module.scss'

import leave from '@assets/leave/leave.png';
import resign from '@assets/leave/resign.png';
import { getScrollStyle } from '@utils/index'


// holType 假期（0自定义假期 1年假 2事假 3病假  4婚假 5产检假  6产假 ）
// state	审批状态（1审批通过 2待审批 3驳回 4撤销）
// const stateArr = ['', '审批通过', '待审批', '驳回', '撤销']
export const stateObj = {
  0: { name: '待审批', color: '#6190E8' },
  1: { name: '审批通过', color: 'green' },
  2: { name: '驳回', color: 'red' },
  3: { name: '撤销', color: 'orange' },
}

const Index = () => {
  const { custId = 32098982 } = useRouter().params
  const { type = 0 } = useRouter().params
  const [current, setCurrent] = useState(Number(type));
  const [holId, setHolId] = useState<any>();
  const [holTypeList, setHolTypeList] = useState<any>();
  const scrollStyle = getScrollStyle({ bottom: 166, top: 10 })

  const refreshOpt = usePagination(
    async page => {
      const result = await ncmp.acHolApply.page.request({ start: page, length: 10, holId, custId: Number(custId) })
      return result.resultObj.rows?.length > 0 ? result.resultObj.rows : false
    },
    { deps: [holId] }
  )
  useEffect(() => {
    ncmp.acHolApply.getHolGroupItem.request({ custId: Number(custId) }, { isToken: true }).then(res => {
      setHolTypeList(res.resultObj)
    })

  }, []);

  const tabList = [{ title: '发起申请' }, { title: '申请记录' }]
  return (
    <AtTabs current={current} tabList={tabList} onClick={(cur) => setCurrent(cur)}>
      <AtTabsPane current={current} index={0} >
        <View className={styles.apply}>
          <View className={styles.item} onClick={() => { Taro.navigateTo({ url: '/attendance/leave/apply' }) }}>
            <Image src={leave} className={styles.img} />
            <Text>请假</Text>
          </View>
          <View className={styles.item} onClick={() => { Taro.navigateTo({ url: `/attendance/clockin/supplementary?custId=${custId}` }) }}>
            <Image src={resign} className={styles.img} />
            <Text>补签</Text>
          </View>
        </View>
      </AtTabsPane>
      <AtTabsPane current={current} index={1}>
        <View>
          <View className={styles.tags}>
            {
              holTypeList?.map(({ HOL_ID, HOL_NAME, NUM }) => <AtTag
                name={HOL_NAME}
                type='primary'
                active={holId === HOL_ID}
                circle
                onClick={({ name }) => {
                  // console.log(name, HOL_ID);
                  setHolId(HOL_ID)
                }}
                className={styles.item}
              >
                {HOL_NAME}（{NUM}）
              </AtTag>)
            }
          </View>
          <View className={styles.list}>
            <ListView
              // refreshEventName='applyRecord'
              style={scrollStyle}
              itemSize={224}
              unlimitedSize
              {...refreshOpt}
              renderItem={({ holName, state, startTime, endTime, viewHours, applyId, empName }) => <View className={styles.item} onClick={
                () => Taro.navigateTo({ url: `/attendance/leave/apply-detail?applyId=${applyId}&state=${state}&custId=${custId}` })
              }>
                {/* <Image className={styles.img} src={leave} /> */}
                <View className={styles.detail}>
                  <View className={styles.title}>
                    <Text style={{ marginRight: '10px' }}>{`${empName}${holName}申请`}</Text>
                    <Text className={styles.tag} style={{ color: stateObj[state]?.color, borderColor: stateObj[state]?.color }}>{stateObj[state]?.name}</Text>
                  </View>
                  <View>开始时间：{startTime}</View>
                  <View>结束时间：{endTime}</View>
                  <View>请假时长：{viewHours}</View>
                </View>
                <AtIcon value='chevron-right' size='20' color='#888'></AtIcon>
              </View>}
            />
          </View>
        </View>
      </AtTabsPane>
    </AtTabs>
  )
}

export default withPage(Index)
