import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleDongYingColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const educationLevel = form.watch('educationLevel');
  if (column.name === 'educationLevel'){
    form.register('educationLevel', {value: column.defaultValue})
  }
  if (column.name === 'graduationSchool_dongying'){
    return {...column, isHidden: ['40','50','61','62', '70', '80', '90'].includes(educationLevel)}

  }
  if (column.name === 'isFullTime_dongying'){
    return {...column, isHidden: ['40','50','61','62', '70', '80', '90'].includes(educationLevel)}
  }
  return column;
}

export { handleDongYingColumn }