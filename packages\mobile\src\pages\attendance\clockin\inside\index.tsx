import { useEffect, useState } from 'react'
import clockInBgImg from '@assets/attendance/clockIn_bg.png'
import clockInBgEnabledImg from '@assets/attendance/clockIn_bg_enabled.png'
import WarnImg from '@assets/attendance/warn.png'
import * as imageConversion from 'image-conversion'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import styles from './index.module.scss'
import { formatDate } from '@utils/common'
import { BaseUrl } from '@utils/request'
import { AtDivider, AtIcon } from 'taro-ui'
import { View, Image } from '@tarojs/components'
import { getGlobalData } from '@utils/index'
import { ncmp } from '@apis/ncmp'

export const uploadFile = (_data: any, formData, updateFun: (res) => void) => {
  const PlatformInfo = sessionStorage.getItem('PlatformInfo')

  const globalToken = PlatformInfo ? JSON.parse(PlatformInfo).globalToken : undefined

  const otehrHeader = { wxGlobalToken: globalToken ? globalToken : undefined }
  const { openId, accountId } = getGlobalData<'account'>('account')
  Taro.showLoading({ title: '上传中...' })
  try {
    const { url, count, currentIndex } = _data
    Taro.uploadFile({
      url: `${BaseUrl}${url}`,
      name: '',
      filePath: '',
      header: otehrHeader,
      formData: { file: formData, openId, accountId, ..._data },
      success(res) {
        const data = JSON.parse(res.data)
        setTimeout(() => {
          console.log('上传')
          if (data.code == '500') {
            Taro.hideLoading()
            Taro.showToast({ title: data.message, icon: 'none' })
            return
          }

          updateFun(data?.resultObj)

          if (currentIndex + 1 === count) {
            Taro.hideLoading()
            Taro.showToast({ title: '上传成功' })
          }
        }, 0)
      },
      fail(res) {
        Taro.hideLoading()
        console.log(res.errMsg)
      },
    })
  } catch (error) {
    Taro.hideLoading()
  }
}

interface InsideProps {
  custId: string | undefined
  position: Record<'lat' | 'lng', string | number>
}

const Index = ({ custId, position }: InsideProps) => {
  const currentDate = new Date()
  const [currentTime, setCurrentTime] = useState<Date>(currentDate)
  const [deviceInfo, setDeviceInfo] = useState<string>('')
  const [isHasAuth, setIsHasAuth] = useState<boolean>(false)
  const [isExistPosition, setIsExistPosition] = useState<boolean>(false)

  const [list, setList] = useState<any[]>([])

  const [imgList, setImgList] = useState<any[]>([])

  const [picUrl, setPicUrl] = useState<string[]>([])

  var timer: any = null

  const getSystemDate = async () => {
    //获取服务器时间
    const res: any = await ncmp.eosCheckIn.getSysTime.request({})
    setCurrentTime(new Date(res?.resultObj?.replace(/-/g, '/')))
  }

  const getSystemInfo = async () => {
    const systemInfo = getGlobalData<'systemInfo'>('systemInfo')
    setDeviceInfo(systemInfo?.model)
  }

  const setTimer = () => {
    //清空计时器
    timer && clearInterval(timer)

    timer = setInterval(() => {
      setCurrentTime((pre) => {
        return new Date(pre.getTime() + 1000)
      })
    }, 1000)
  }

  const getIsInScope = async () => {
    const res = await ncmp.eosCheckIn.getIsInScope.request({
      custId: String(custId),
      longitude: position.lng,
      latitude: position.lat,
    })

    setIsHasAuth(true)
    setIsExistPosition(Boolean(res?.resultObj))
  }

  const getCheckLogList = async () => {
    const res = await ncmp.eosCheckIn.getCheckLogList.request({
      custId: String(custId || ''),
      date: formatDate(currentTime, 'yyyy-MM-dd'),
    })
    setList(res?.resultObj)
  }

  const onClockIn = async () => {
    if (!isExistPosition) return

    Taro.showLoading()
    const { lat, lng } = position
    const res: any = await ncmp.eosCheckIn.saveCheck.request({
      custId: custId,
      latitude: lat,
      longitude: lng,
      checkTime: formatDate(currentTime, 'yyyy-MM-dd HH:mm:ss'),
      deviceDes: deviceInfo,
      deviceId: deviceInfo,
      picUrl: picUrl?.join(','),
    })

    Taro.hideLoading()
    Taro.showToast({
      title: res?.msg,
      icon: 'none',
    })

    let redrictUrl = '/pages/attendance/clockin/fail/index?endtime='

    console.log(res,"saveCheckWQW")

    if (res?.resultObj === 1) {
      redrictUrl = '/pages/attendance/clockin/success/index?endtime='
    }

    console.log(redrictUrl,"redrictUrl");
    Taro.navigateTo({
      url: redrictUrl + formatDate(currentTime, 'HH:mm:ss'),
    })
  }

  useEffect(() => {
    if (position.lat === '') return
    setPicUrl([])
    setImgList([])
    getSystemDate()
    getSystemInfo()
    setTimer()
    getIsInScope()
  }, [position])

  useDidShow(()=>{
    getCheckLogList()
  })

  const removeImg = (index: number) => {
    setImgList((pre) => pre.filter((_, i) => i !== index))
    setPicUrl((pre) => pre.filter((_, i) => i !== index))
  }

  const uploadImage = () => {
    if (!isExistPosition) return

    if (imgList?.length >= 3) {
      Taro.showToast({
        title: '最多上传3张图片',
        icon: 'none',
      })
      return
    }

    Taro.chooseImage({
      count: 3,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        console.log('upload', res)
        setImgList((pre) => [...pre, ...res?.tempFilePaths])
        res?.tempFiles?.forEach((item, i) => {
          const file: any = item.originalFileObj
          // 比例 scale
          imageConversion.compressAccurately(file, { size: 300, width: 750 }).then((data) => {
            uploadFile(
              { url: '/wx-ncmp/eos/attendance/checkin/uploadFile', count: res?.tempFiles?.length, currentIndex: i },
              data,
              (filePath) => {
                setPicUrl((pre) => [...pre, filePath])
              }
            )
          })
        })
      },
    })
  }

  return (
    <div className={styles.container}>
      <>
        {!isHasAuth ? (
          <div className={styles.error_wrapper}>
            <div className={styles.error_tip}>
              <img src={WarnImg} className={styles.img} />
              <div className={styles.error_info}>无法定位，请先开启定位</div>
            </div>
          </div>
        ) : (
          <>
            {isExistPosition ? (
              <div className={styles.wrapper}>
                <div className={styles.tip_wrap}>
                  <div className={styles.tip}>{formatDate(currentTime, 'yyyy.MM.dd')} 您已在考勤范围内</div>
                </div>
                <div className={styles.content}>
                  {list?.map((m, i) => {
                    return (
                      <>
                        <div key={i} className={styles.content_title}>
                          <div className={styles.dot}></div>
                          <div className={styles.item}>
                            <div className={styles.time}>
                              {formatDate(new Date(m.checkTime?.replace(/-/g, '/')), 'HH:mm:ss')}
                            </div>
                            <div className={styles.address}>{m.checkAdd}</div>
                          </div>
                        </div>
                        {i < list?.length - 1 && <AtDivider height={1} />}
                      </>
                    )
                  })}
                </div>
              </div>
            ) : (
              <div className={styles.error_wrapper}>
                <div className={styles.error_tip}>
                  <img src={WarnImg} className={styles.img} />
                  <div className={styles.error_info}>您不在办公地点或没有设置打卡地点</div>
                </div>
              </div>
            )}
            <div className={styles.uploader}>
              {imgList?.map((item, index) => {
                return (
                  <View className={styles.img_wrap} key={item?.path}>
                    <Image
                      className={styles.img}
                      src={item}
                      onClick={() => {
                        Taro.previewImage({
                          current: item,
                          urls: imgList,
                        })
                      }}
                    />
                    <View className={styles.del}>
                      <AtIcon
                        className={styles.icon}
                        value='close-circle'
                        size={16}
                        onClick={() => removeImg(index)}
                      ></AtIcon>
                    </View>
                  </View>
                )
              })}
              {imgList?.length < 3 && (
                <View style={{ marginLeft: '20px' }}>
                  <AtIcon value='camera' size='36' onClick={() => uploadImage()}></AtIcon>
                </View>
              )}
            </div>
            <div className={styles.bottom}>
              <div className={styles.clockIn_btn} onClick={onClockIn}>
                <img src={isExistPosition ? clockInBgImg : clockInBgEnabledImg} />
                <div className={styles.btn}>
                  <div className={styles.text}>打卡</div>
                  <div className={styles.time}>{formatDate(currentTime, 'HH:mm:ss')}</div>
                </div>
              </div>
            </div>
          </>
        )}
      </>
    </div>
  )
}

export default Index
