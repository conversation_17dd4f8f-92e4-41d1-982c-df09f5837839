import { useState, useRef, useEffect } from 'react'
import { View, Text, Input, Icon } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'
import { useModal } from '@components/modal/useModal'
import { BottomBtn, Modal, withPage, ListView, usePagination} from '@components'
import { getGlobalData, getScrollStyle } from '@utils'
import { pact } from '@apis/pact'
import ZJContent from './components/zj-tip-content'  
import XIANContent from './components/xian-tip-content'
import styles from './index.module.scss'


const Index = () => {
  const [city, setCity] = useState<defs.pact.MoreCityInfo>()
  const [search, setSearch] = useState('')
  const inputRef = useRef<any>()
  const modal = useModal()
  const { openId, accountId } = getGlobalData<'account'>('account')
  const scrollStyle = getScrollStyle({ bottom: 120, top: 200,})
  const { scen = '1', eventName, keys, type, ...message} = useRouter<any>().params
  const list = usePagination(
    async () => {
      const result = await pact.cityInfo.getAllcitys.request({
        cityname: search
      })
      return result
    },
    { deps: [search] }
  )
  // scen 1:入职办理 2选择城市
  useEffect(() => {
    if (scen === '2') {
      setCity({ code: message.cityName, name: message.cityName, provinceCode: message.provinceId })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  useEffect(() => {
    if (scen === '1') {
      const title = '员工入职办理须知'
      const content =
        '您好！真诚欢迎您加入易才大家庭，在这里易才会为您提供优质、高效、专业的人力资源服务。为保证日常业务的顺利进行，保障您社保的正常缴纳及享受，请您按照易才客服通知的信息及时提供参保资料，同时确保您提供的资料或填写的个人信息属实，若发现信息有任何虚假，需您自行承担全部责任。'
      const onClose = () => {
        modal.setModal({ visible: false })
        setTimeout(() => Taro.navigateBack(), 500)
      }
      const onConfirm = () => modal.setModal({ visible: false })
      setTimeout(() => modal.setModal({ title, content, visible: true, onClose, onConfirm }), 800)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const getUuid = async (cityCode: string) => {
    const result = await pact.entry.getuuid.request({
      cityId: cityCode,
      openId,
      accountId
    })
    return result
  }
  const handleSumit = () => {
    if (city?.name) {
      if (scen === '2') {
        Taro.eventCenter.trigger(eventName, {
          provinceId: city.provinceCode,
          cityId: city.code,
          cityName: city.name
        })
        Taro.navigateBack()
        return
      }
      if (city.code === '10835') {
        // 镇江
        const title = ''
        const content = <ZJContent />
        const onClose = () => modal.setModal({ visible: false })
        const onConfirm = () => {
          getUuid(city?.code!).then((res)=> {
            if (res.code !== '200') {
              Taro.showToast({title: '系统异常,请稍后重试！', icon: 'none'})
              return
            }
            if (res.data?.status === '1') {
              const uuid = res?.data?.uuid
              Taro.navigateTo({ url: `/pages/sample/index?cityCode=${city.code}&uuid=${uuid}&key=${type}` })
            } else {
              const msg = res?.data?.msg
              Taro.showToast({title: msg!, icon: 'none'})
            }
          }).catch(() => {
            Taro.showToast({title: '系统异常,请稍后重试！', icon: 'none'})
            return
          })
          modal.setModal({ visible: false })
        }
        setTimeout(() => modal.setModal({ title, content, visible: true, onClose, onConfirm }), 500)
      } else {
        getUuid(city?.code!).then((res)=> {
          if (res.code !== '200') {
            Taro.showToast({title: '系统异常,请稍后重试！', icon: 'none'})
            return
          }
          if (res.data?.status === '1') {
            const uuid = res?.data?.uuid
            Taro.navigateTo({ url: `/pages/sample/index?cityCode=${city.code}&uuid=${uuid}&key=${type}` })
          } else {
            const msg = res?.data?.msg
            // Taro.navigateTo({ url: `/pages/sample/index?cityCode=${city.code}&uuid=${'13ade51d-c83d-458d-a104-4235ceed3b5f'}` })
            Taro.showToast({title: msg!, icon: 'none'})
          }
        }).catch(() => {
          Taro.showToast({title: '系统异常,请稍后重试！', icon: 'none'})
          return
        })
      }
    } else {
      Taro.showToast({
        title: '请选择城市',
        icon: 'none'
      })
    }
  }
  return (
    <View className={styles.wrap}>
      <View className={styles.header}>
        <Text className={styles.header_title}>
          已选城市：
          <Text className={styles.header_city}>{city?.name || ''}</Text>
        </Text>
        <View className={styles.input_wrap}>
          <Input className={styles.input} placeholder='请输入城市名称' ref={inputRef} />
          <View
            className={styles.search}
            onClick={() => {
              const value = inputRef.current.tmpValue || inputRef.current.value
              setSearch(value)
            }}
          >
            <Icon size='20' type='search' color='#fff' />
          </View>
        </View>
      </View>
      <ListView 
        style={scrollStyle}
        itemSize={100}
        noPull
        renderItem={(item, index)=>
          <View key={index} className={styles.item} onClick={() => setCity(item)}>
            <Text className={styles.item_title}>{item.name}</Text>
          </View>
        }
        {...list}
      />
      <BottomBtn
        btns={[
          {
            title: scen === '1' ? '进入办理' : '确定',
            onClick: handleSumit
          }
        ]}
      />
      <Modal {...modal.modal} />
    </View>
  )
}

export default withPage(Index)
