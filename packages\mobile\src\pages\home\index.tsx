import { useState, useEffect } from 'react'
import Taro from '@tarojs/taro'
import { useModal } from '@components/modal/useModal'
import { View, Text, Image, ScrollView, CheckboxGroup, Checkbox } from '@tarojs/components'
import { Modal, StatusBar, withPage } from '@components'
import { getGlobalData } from '@utils/global-data'
import { closeWindow } from '@utils/jssdk'
import { ncmp } from '@apis/ncmp'
import { isEmpty } from 'lodash'
import Icon1 from '@assets/home/<USER>'
import Icon2 from '@assets/home/<USER>'
import Icon3 from '@assets/home/<USER>'
import Icon4 from '@assets/home/<USER>'
import Icon5 from '@assets/home/<USER>'
import Icon7 from '@assets/home/<USER>'
import Icon8 from '@assets/home/<USER>'
import Icon9 from '@assets/home/<USER>'
import Icon10 from '@assets/home/<USER>'
import Icon11 from '@assets/home/<USER>'
import styles from './index.module.scss'


const items = [
  {
    title: '社保查询',
    img: Icon1,
    key: 'social-security'
  },
  {
    title: '公积金查询',
    img: Icon2,
    key: 'accumulation'
  },
  {
    title: '工资查询及办理',
    img: Icon3,
    key: 'basic-select'
  },
  {
    title: '预约办理及查询',
    img: Icon4,
    key: 'appointment'
  },
  {
    title: '入职办理及电子合同',
    img: Icon5,
    key: 'induction-elesignature'
  },
  {
    title: '电子离职办理',
    img: Icon10,
    key: 'staff-dimission'
  },
  {
    title: '社保卡办理',
    img: Icon7,
    key: 'card-processin'
  },
  {
    title: '个人中心',
    img: Icon8,
    key: 'personal'
  },
  {
    title: '易才管家',
    img: Icon9,
    key: 'steward'
  },
  {
    title: '智能问答',
    img: Icon11,
    key: 'robot-chat'
  }
]
const Index = () => {
  const [isCheck, setIsCheck] = useState(false)
  const [result, setResult] = useState<defs.ncmp.PrivacyRegisterDTO>({})
  const [flag, setFlag] = useState(true)
  const account = getGlobalData<'account'>('account')
  const PlatformInfo = sessionStorage.getItem('PlatformInfo')
  const accountInfo = PlatformInfo && JSON.parse(PlatformInfo)
  const modal = useModal()
  const handleChange = () => {
    setIsCheck(!isCheck)
  }
  const clientH=window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
  const scrollStyle = {
    maxHeight: `${Math.round(clientH/1.8)}px`
  }
  useEffect(() => {
    if(!account?.openId || !accountInfo?.openId) return
    ncmp.privacy.findLastestRegister.request({openId: account?.openId || accountInfo?.openId}).then((res:any) => {
      if (res.code !== '200') {
        Taro.showToast({title: res.errorMsg, icon: 'none'})
        setTimeout(() => closeWindow(), 500)
        return
      }
      if (res?.resultObj?.version === -1) {
        Taro.showToast({title: '系统异常,请稍后重试！', icon: 'none'})
        setTimeout(() => closeWindow(), 500)
        return
      }
      if (isEmpty(res.resultObj)) return
      setResult(res.resultObj)
      setFlag(res.resultObj?.registed)
    })
  }, [account?.openId, accountInfo?.openId])
  useEffect(() => {
    const title = '隐私协议'
    const content =
    <View>
      <ScrollView scrollY style={scrollStyle}>
        <View dangerouslySetInnerHTML={{__html: result?.content!}}></View>
      </ScrollView>
      <CheckboxGroup onChange={handleChange}>
        <Checkbox value='' checked={isCheck}></Checkbox>
      </CheckboxGroup>
      <Text className={styles.tips}>本人已详细阅读并知晓以上内容</Text>
    </View>
    const onClick = () => {
      ncmp.privacy.postRegister.request({privacyId: result?.privacyId, version: result?.version, openId: account?.openId || accountInfo?.openId}).then((resp)=>{
        if (resp.code !== '200') {
          Taro.showToast({title: resp.errorMsg || '系统异常', icon: 'none'})
          modal.setModal({ visible: false })
          return
        }
        modal.setModal({ visible: false })
      }).catch((err)=> {
        Taro.showToast({title: '系统异常,请稍后重试！', icon: 'none'})
        modal.setModal({ visible: false })
        console.log(err)
      })
    }
    const btns:any = [{title: '同意', type: isCheck ? 'primary' : '', onClick: isCheck ? onClick : () => {}}]
    setTimeout(() => modal.setModal({title, content, visible: true, btns }), 0)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCheck, result?.content])
  const onItemClick = (item: any) => {
    if (item.key === 'robot-chat') {
      Taro.navigateTo({ url: '/pages/robot_chat/index' })
      return
    }
    if(!account.bindStatus || !accountInfo.bindStatus){
      if (account.isOuterEmp === '1' || accountInfo.isOuterEmp === '1'){
        Taro.showToast({title: '您不是易才委托员工，无法查看人事服务!', icon: 'none'})
        setTimeout(() => wx.closeWindow(), 800)
        return
      }
      Taro.navigateTo({ url: '/pages/binding/index'})
      return;
    }
    if (item.key === 'induction-elesignature') {
      // 入职办理及电子合同
      Taro.navigateTo({ url: '/pages/induction-elesignature/index'})
    } else if (item.key === 'staff-dimission') {
      // 离职职办理及电子合同
      Taro.navigateTo({ url: '/pages/staff-dimission/index'})
    }else if (item.key === 'appointment') {
      // 预约办理查询
      Taro.navigateTo({ url: '/pages/appointment/index' })
    } else if (item.key === 'personal') {
      // 个人中心
      Taro.navigateTo({ url: '/pages/personal/index' })
    } else if (item.key === 'steward') {
      Taro.navigateTo({ url: '/pages/steward/index' })
    } else if (item.key === 'card-processin') {
      console.log(item)
      Taro.navigateTo({ url: '/pages/cardProcessin/index' })
    } else if (item.key === 'basic-select') {
      // 工资查询及办理
      Taro.navigateTo({ url: '/pages/basic-select/index' })
    } else if (item.key === 'social-security') {
      // 社保查询
      Taro.navigateTo({ url: '/pages/social-security/index?type=social-security' })
    } else if (item.key === 'accumulation') {
      // 公积金查询
      Taro.navigateTo({ url: '/pages/social-security/index?type=accumulation' })
    } else {
      Taro.navigateTo({ url: '/pages/binding/index' })
    }
  }
  return (
    <View className={styles.home}>
      <StatusBar backgroundColor='#fff' />
      <Image
        src={require('@assets/home/<USER>')}
        className={styles.banner}
      />
      <View className={styles.item_wrap}>
        {items.map(item => (
          <View key={item.key} className={styles.item} onClick={() => onItemClick(item)}>
            <Image src={item.img} className={styles.item_img} />
            <Text className={styles.item_title}>{item.title}</Text>
          </View>
        ))}
      </View>
      {!flag && <Modal {...modal.modal} />}
    </View>
  )
}

export default withPage(Index)
