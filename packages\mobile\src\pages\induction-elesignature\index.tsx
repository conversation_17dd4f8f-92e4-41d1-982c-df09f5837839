import { useEffect, useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import { withPage } from '@components'
import { getGlobalData } from '@utils'
import Taro from '@tarojs/taro'
import { ncmp } from '@apis/ncmp'
import { pact } from '@apis/pact'
import icon_elesign_1 from '@assets/elesign/induction1.png'
import icon_elesign_2 from '@assets/elesign/induction2.png'
import icon_elesign_3 from '@assets/elesign/elesignature.png'
import icon_elesign_4 from '@assets/elesign/nullify.png'
import icon_elesign_5 from '@assets/elesign/view.png'
import icon_elesign_6 from '@assets/elesign/added.png'
import styles from './index.module.scss'

const list = [
  // {
  //   title: '入职办理',
  //   img: icon_elesign_2,
  //   key: '1'
  // },
  {
    title: '入职办理',
    img: icon_elesign_2,
    key: '100'
  },
  // {
  //   title: '入职办理查看',
  //   img: icon_elesign_1,
  //   key: '8'
  // },
  {
    title: '入职办理查看',
    img: icon_elesign_1,
    key: '200'
  },
  {
    title: '签署电子合同',
    img: icon_elesign_3,
    key: '2'
  },
  {
    title: '作废电子合同',
    img: icon_elesign_4,
    key: '4'
  },
  {
    title: '查看电子合同',
    img: icon_elesign_5,
    key: '6'
  },
  {
    title: '待补充证件',
    img: icon_elesign_6,
    key: '7'
  }
]

const Index = () => {
  const accountData = getGlobalData<'account'>('account')
  const [arrItem, setArritem] = useState<any[]>([])
  const [enclosure, setEnclosure] = useState<any>([])
  useEffect(() => {
    ncmp.elecSign.getHzSupplyCountByEmpId.request({ empId: accountData?.empId || '' }, { isToken: true }).then(res => {
      if (res.code === '200') {
        setEnclosure(res?.resultObj)
        setArritem(
          list.filter(item => {
            return res?.resultObj?.supplyId ? item : item.key !== '7'
          })
        )
      }
    })
  }, [])

  const toEelSignaturePage = (key: string) => {
    switch (key) {
      case '1':
        Taro.navigateTo({ url: `/pages/city/index?type=${key}` })
        break
      case '100':
        Taro.navigateTo({ url: `/pages/city/index?type=${key}` })
        break
      case '2':
      case '4':
        getSignEleContract(key)
        break
      case '6':
        getEleContractList()
        break
      case '7':
        Taro.navigateTo({
          url: `/pages/addedmetarialdata/index?supplyId=${enclosure?.supplyId}&reasonText=${enclosure?.rejectReason ||
            ''}`
        })
        break
      case '8':
      case '200':
        getLastestHiresep(key)
        break
      default:
        break
    }
  }
  const getEleContractList = () => {
    Taro.navigateTo({ url: 'pages/induction-elesignature/eleContractList/index' })
  }
  const getSignEleContract = (eleContractStatus: string) => {
    ncmp.elecSign.getSignEleContract
      .request(
        {
          empId: accountData?.empId || '',
          eleContractStatus
        },
        { isToken: true }
      )
      .then(res => {
        if (res.code === '200') {
          const eleSinUrl = res?.resultObj?.eleSinUrl || ''
          if (!eleSinUrl) {
            const title = eleContractStatus === '2' ? '暂无签署合同' : '暂无作废合同'
            Taro.showToast({ title, icon: 'none' })
            return
          }
          window.location.href = eleSinUrl
          // Taro.navigateTo({ url: `/pages/web/index?src=${eleSinUrl}` })
        }
      })
  }
  const getLastestHiresep = (key:string) => {
    ncmp.hiresep.findLastestHiresep
      .request({
        accountId: accountData?.accountId || ''
      })
      .then((res: any) => {
        if (res.code === '200') {
          const { cityId, uuid } = res?.resultObj || {}
          pact.cityDynamicsForm.getDynamicItem
            .request({ cityCode: cityId, openId: accountData?.openId || '', uuid, pageNo: 1 })
            .then((data: any) => {
              const flag = data?.map(item => item.subList)[0]?.some(it => it.itemValue)
              if (!flag) {
                Taro.showToast({ title: '您没有提交的入职办理信息，无法查看', icon: 'none' })
                return
              }
              if (key === '200'){
                Taro.navigateTo({ url: 'pages/induction-elesignature/cityHireFormList/index' })
                return
              }
              Taro.navigateTo({
                url: `/pages/induction/handle/form/index?cityId=${cityId}&uuid=${uuid}`
              })
            })
        }
      })
  }
  return (
    <View className={styles.wrap}>
      {arrItem?.map(item => (
        <View
          className={styles.item}
          key={item.title}
          onClick={() => {
            toEelSignaturePage(item.key)
          }}
        >
          <Image className={styles.img} src={item.img} />
          <Text className={styles.text}>{item.title}</Text>
        </View>
      ))}
    </View>
  )
}

export default withPage(Index)
