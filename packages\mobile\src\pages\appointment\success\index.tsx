import { View, Text, Image, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { BottomBtn,withPage } from '@components'
import { Fragment } from 'react'
import { getScrollStyle } from '@utils'
import SuccessImg from '@assets/img-success.png'
import styles from './index.module.scss'

const Index = () => {
  const scrollStyle = getScrollStyle({ bottom: 120 })
  return (
    <Fragment>
      <ScrollView style={scrollStyle} scrollY>
        <View className={styles.wrap}>
          <Image className={styles.img} src={SuccessImg} />
          <Text className={styles.title}>预约成功</Text>
          <Text className={styles.detail}>
            您已成功提交预约，请等待工作人员审核。如有问题，请随时联系您的易才管家。
          </Text>
        </View>
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回预约列表',
            onClick: () => {
              Taro.navigateBack({ delta: 1 })
              Taro.eventCenter.trigger('Appointment')
            }
          }
        ]}
      />
    </Fragment>
  )
}

export default withPage(Index,{showAfterLogin:false})
