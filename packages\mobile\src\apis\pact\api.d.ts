type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};

declare namespace defs {
  export namespace pact {
    export class AccountInfo {
      /** accountId */
      accountId?: string;

      /** idCardNum */
      idCardNum?: string;

      /** mobilePhoneNum */
      mobilePhoneNum?: string;

      /** openId */
      openId?: string;

      /** verificationCode */
      verificationCode?: string;
    }

    export class AppBindInfo {
      /** accountId */
      accountId?: string;

      /** bindStatus */
      bindStatus?: boolean;

      /** bindTime */
      bindTime?: string;

      /** credentialsType */
      credentialsType?: string;

      /** empId */
      empId?: string;

      /** globalToken */
      globalToken?: string;

      /** id */
      id?: number;

      /** idCard */
      idCard?: string;

      /** latestToken */
      latestToken?: string;

      /** mobile */
      mobile?: string;

      /** openId */
      openId?: string;

      /** token */
      token?: string;

      /** updateTime */
      updateTime?: string;
    }

    export class AppointmentCancelBean {
      /** code */
      code?: string;

      /** message */
      message?: string;
    }

    export class AppointmentCancelParam {
      /** bookingId */
      bookingId?: string;

      /** cancelReason */
      cancelReason?: string;

      /** empId */
      empId?: string;

      /** openId */
      openId?: string;
    }

    export class AppointmentDetailBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.AppointmentDetailData;

      /** message */
      message?: string;
    }

    export class AppointmentDetailData {
      /** bookingDt */
      bookingDt?: string;

      /** bookingRemark */
      bookingRemark?: string;

      /** bookingStatus */
      bookingStatus?: string;

      /** busSubTypeName */
      busSubTypeName?: string;

      /** busTypeName */
      busTypeName?: string;

      /** cancelReason */
      cancelReason?: string;

      /** categoryId */
      categoryId?: string;

      /** categoryName */
      categoryName?: string;

      /** contactTel */
      contactTel?: string;

      /** createDt */
      createDt?: string;

      /** departmentName */
      departmentName?: string;

      /** empName */
      empName?: string;

      /** idCardNum */
      idCardNum?: string;

      /** mobilePhoneNum */
      mobilePhoneNum?: string;

      /** updateDt */
      updateDt?: string;
    }

    export class AppointmentListBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.AppointmentListData>;

      /** message */
      message?: string;
    }

    export class AppointmentListData {
      /** bookingDt */
      bookingDt?: string;

      /** bookingId */
      bookingId?: string;

      /** bookingStatus */
      bookingStatus?: string;

      /** busSubTypeName */
      busSubTypeName?: string;

      /** busTypeName */
      busTypeName?: string;

      /** categoryId */
      categoryId?: string;

      /** categoryName */
      categoryName?: string;

      /** createDt */
      createDt?: string;
    }

    export class AppointmentNumBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.AppointmentNumData;

      /** message */
      message?: string;
    }

    export class AppointmentNumData {
      /** code */
      code?: number;

      /** data */
      data?: number;
    }

    export class AuthParamBean {
      /** accountInfo */
      accountInfo?: defs.pact.AccountInfo;

      /** openId */
      openId?: string;
    }

    export class AuthResponseBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.AuthResponseData;

      /** message */
      message?: string;
    }

    export class AuthResponseData {
      /** cmpToken */
      cmpToken?: string;

      /** empId */
      empId?: string;

      /** result */
      result?: string;
    }

    export class BusinessDetailBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.BusinessDetailData;

      /** message */
      message?: string;
    }

    export class BusinessDetailData {
      /** busSubTypeName */
      busSubTypeName?: string;

      /** busTypeName */
      busTypeName?: string;

      /** businessId */
      businessId?: string;

      /** businessStatus */
      businessStatus?: string;

      /** pAgent */
      pAgent?: string;

      /** pOperator */
      pOperator?: string;

      /** procResult */
      procResult?: string;

      /** procStartDt */
      procStartDt?: string;

      /** procStatus */
      procStatus?: string;

      /** processorType */
      processorType?: string;

      /** result */
      result?: string;
    }

    export class BusinessItemBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.BusinessItemData>;

      /** message */
      message?: string;
    }

    export class BusinessItemData {
      /** key */
      key?: string;

      /** value */
      value?: string;
    }

    export class BusinessListBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.BusinessListData>;

      /** message */
      message?: string;
    }

    export class BusinessListData {
      /** busSubTypeName */
      busSubTypeName?: string;

      /** busTypeName */
      busTypeName?: string;

      /** businessId */
      businessId?: string;

      /** businessStatus */
      businessStatus?: string;

      /** categoryId */
      categoryId?: string;

      /** categoryName */
      categoryName?: string;

      /** createDt */
      createDt?: string;
    }

    export class ButlerResponseBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.ButlerResponseData>;

      /** message */
      message?: string;

      /** result */
      result?: number;
    }

    export class ButlerResponseData {
      /** address */
      address?: string;

      /** contact */
      contact?: string;

      /** email */
      email?: string;

      /** providerType */
      providerType?: string;

      /** stewardName */
      stewardName?: string;
    }

    export class CityBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.CityData>;

      /** message */
      message?: string;
    }

    export class CityData {
      /** key */
      key?: string;

      /** value */
      value?: string;
    }

    export class ClcData {
      /** 表单配置 */
      clcTemplateInfo?: defs.pact.ClcTemplateInfo;

      /** 表单数据 */
      jsonData?: string;

      /** 图片数据 */
      photos?: Array<ObjectMap<string, ObjectMap>>;
    }

    export class ClcTemplateInfo {
      /** createBy */
      createBy?: string;

      /** createTime */
      createTime?: string;

      /** creatorName */
      creatorName?: string;

      /** id */
      id?: string;

      /** isDelete */
      isDelete?: number;

      /** status */
      status?: string;

      /** templateCode */
      templateCode?: string;

      /** templateId */
      templateId?: string;

      /** templateJson */
      templateJson?: string;

      /** updateBy */
      updateBy?: string;

      /** updateName */
      updateName?: string;

      /** updateTime */
      updateTime?: string;

      /** version */
      version?: string;

      /** versionName */
      versionName?: string;
    }

    export class CompanyResponseBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.CompanyResponseData>;

      /** message */
      message?: string;
    }

    export class CompanyResponseData {
      /** address */
      address?: string;

      /** areaId */
      areaId?: number;

      /** cityId */
      cityId?: number;

      /** contactTel */
      contactTel?: string;

      /** id */
      id?: number;

      /** key */
      key?: string;

      /** name */
      name?: string;

      /** value */
      value?: string;
    }

    export class DynamicFormReqBean {
      /** birthdate */
      birthdate?: string;

      /** cityCode */
      cityCode?: string;

      /** idCard */
      idCard?: string;

      /** name */
      name?: string;

      /** openId */
      openId?: string;

      /** pageNo */
      pageNo?: number;

      /** sex */
      sex?: string;

      /** uuid */
      uuid?: string;
    }

    export class EchoFileSubmitReqBean {
      /** accountId */
      accountId?: string;

      /** cityCode */
      cityCode?: string;

      /** newBusId */
      newBusId?: string;

      /** oldBusId */
      oldBusId?: string;

      /** openId */
      openId?: string;
    }

    export class EchoFileSubmitResp {
      /** address */
      address?: string;

      /** code */
      code?: string;

      /** data */
      data?: string;

      /** idCard */
      idCard?: string;

      /** message */
      message?: string;

      /** name */
      name?: string;

      /** sex */
      sex?: string;

      /** validEnd */
      validEnd?: string;

      /** validStart */
      validStart?: string;
    }

    export class EntryParam {
      /** businessId */
      businessId?: string;

      /** cityCode */
      cityCode?: string;

      /** formContent */
      formContent?: string;

      /** openid */
      openid?: string;

      /** token */
      token?: string;
    }

    export class EntryResponseBean {
      /** code */
      code?: string;

      /** message */
      message?: string;
    }

    export class FeedbackBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.FeedbackData>;

      /** message */
      message?: string;
    }

    export class FeedbackData {
      /** businessId */
      businessId?: number;

      /** categoryName */
      categoryName?: string;

      /** cetfId */
      cetfId?: number;

      /** fbContext */
      fbContext?: string;

      /** fbDate */
      fbDate?: string;

      /** isRead */
      isRead?: number;
    }

    export class FollowResponse {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.FollowResponseData;

      /** message */
      message?: string;
    }

    export class FollowResponseData {
      /** accountId */
      accountId?: string;

      /** cmpToken */
      cmpToken?: string;
    }

    export class FollowerInfo {
      /** accountId */
      accountId?: string;

      /** appId */
      appId?: string;

      /** city */
      city?: string;

      /** country */
      country?: string;

      /** createtime */
      createtime?: string;

      /** delFlag */
      delFlag?: boolean;

      /** groupId */
      groupId?: number;

      /** headImgUrl */
      headImgUrl?: string;

      /** id */
      id?: number;

      /** language */
      language?: string;

      /** lastSubscribeTime */
      lastSubscribeTime?: string;

      /** nickname */
      nickname?: string;

      /** openId */
      openId?: string;

      /** province */
      province?: string;

      /** queryNickname */
      queryNickname?: string;

      /** remark */
      remark?: string;

      /** remarkNickname */
      remarkNickname?: string;

      /** sex */
      sex?: string;

      /** subscribe */
      subscribe?: string;

      /** subscribeTime */
      subscribeTime?: string;

      /** tagidList */
      tagidList?: string;

      /** thumbnail */
      thumbnail?: string;

      /** unionId */
      unionId?: string;

      /** unsubscribeTime */
      unsubscribeTime?: string;
    }

    export class FundData {
      /** code */
      code?: string;

      /** eTotalAmt */
      eTotalAmt?: string;

      /** list */
      list?: Array<defs.pact.FundLiatData>;

      /** message */
      message?: string;

      /** pTotalAmt */
      pTotalAmt?: string;
    }

    export class FundDetailBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.FundDetailData>;

      /** message */
      message?: string;
    }

    export class FundDetailData {
      /** amt */
      amt?: string;

      /** city */
      city?: string;

      /** eAdditionalAmt */
      eAdditionalAmt?: string;

      /** eAmt */
      eAmt?: string;

      /** eBase */
      eBase?: string;

      /** eRatio */
      eRatio?: string;

      /** pAdditionalAmt */
      pAdditionalAmt?: string;

      /** pAmt */
      pAmt?: string;

      /** pBase */
      pBase?: string;

      /** pRatio */
      pRatio?: string;

      /** productName */
      productName?: string;

      /** serviceMonth */
      serviceMonth?: string;
    }

    export class FundDetailParam {
      /** category */
      category?: string;

      /** empId */
      empId?: string;

      /** openId */
      openId?: string;

      /** serviceMonth */
      serviceMonth?: string;
    }

    export class FundLiatData {
      /** amt */
      amt?: string;

      /** custName */
      custName?: string;

      /** eAmt */
      eAmt?: string;

      /** pAmt */
      pAmt?: string;

      /** serviceMonth */
      serviceMonth?: string;
    }

    export class FundParam {
      /** openId */
      openId?: string;

      /** paramMap */
      paramMap?: defs.pact.ParamMap;
    }

    export class HosResponseBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.HosResponseData>;

      /** message */
      message?: string;
    }

    export class HosResponseData {
      /** hosCode */
      hosCode?: string;

      /** hosCountyName */
      hosCountyName?: string;

      /** hosLevel */
      hosLevel?: string;

      /** hosType */
      hosType?: string;

      /** key */
      key?: string;

      /** value */
      value?: string;
    }

    export class IdCardComposePram {
      /** accountId */
      accountId?: string;

      /** businessId */
      businessId?: string;

      /** cityCode */
      cityCode?: string;

      /** endMediaId */
      endMediaId?: string;

      /** endUrl */
      endUrl?: string;

      /** frontMediaId */
      frontMediaId?: string;

      /** frontUrl */
      frontUrl?: string;

      /** openId */
      openId?: string;

      /** type */
      type?: string;
    }

    export class IdCardComposeRespBean {
      /** code */
      code?: string;

      /** data */
      data?: string;

      /** message */
      message?: string;
    }

    export class InsertParam {
      /** accountInfo */
      accountInfo?: defs.pact.AccountInfo;

      /** bookingDt */
      bookingDt?: string;

      /** bookingRemark */
      bookingRemark?: string;

      /** busSubTypeIdStr */
      busSubTypeIdStr?: string;

      /** busTypeId */
      busTypeId?: string;

      /** cancelReason */
      cancelReason?: string;

      /** categoryId */
      categoryId?: string;

      /** cityId */
      cityId?: string;

      /** createBy */
      createBy?: string;

      /** dateType */
      dateType?: string;

      /** departmentId */
      departmentId?: string;

      /** openId */
      openId?: string;

      /** uuid */
      uuid?: string;
    }

    export class InsertResponseBean {
      /** code */
      code?: string;

      /** message */
      message?: string;
    }

    export class JsonMessage {
      /** data */
      data?: object;

      /** errorCode */
      errorCode?: string;

      /** errorMessage */
      errorMessage?: string;

      /** successful */
      successful?: boolean;
    }

    export class Map<T0 = any, T1 = any> {}

    export class MaterBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.MaterData>;

      /** message */
      message?: string;
    }

    export class MaterData {
      /** busSubtypeId */
      busSubtypeId?: number;

      /** isReturn */
      isReturn?: number;

      /** materialsAccount */
      materialsAccount?: number;

      /** materialsInfo */
      materialsInfo?: defs.pact.MaterialsInfo;
    }

    export class MaterialsInfo {
      /** isOriginal */
      isOriginal?: string;

      /** materialsId */
      materialsId?: string;

      /** materialsName */
      materialsName?: string;
    }

    export class MenuResponseBean {
      /** code */
      code?: number;

      /** data */
      data?: defs.pact.MenuResponseData;

      /** message */
      message?: string;
    }

    export class MenuResponseData {
      /** view_Implementation */
      view_Implementation?: number;

      /** view_order */
      view_order?: number;

      /** view_providentFund */
      view_providentFund?: number;

      /** view_salary */
      view_salary?: number;

      /** view_socialInsurance */
      view_socialInsurance?: number;
    }

    export class MoreCityInfo {
      /** code */
      code?: string;

      /** name */
      name?: string;

      /** orderValue */
      orderValue?: number;

      /** provinceCode */
      provinceCode?: string;
    }

    export class OcrParamBean {
      /** empId */
      empId?: number;

      /** mediaId */
      mediaId?: string;

      /** openid */
      openid?: string;
    }

    export class OcrResponseBean {
      /** code */
      code?: number;

      /** data */
      data?: defs.pact.OcrResponseData;

      /** message */
      message?: string;
    }

    export class OcrResponseData {
      /** id_code */
      id_code?: string;

      /** name */
      name?: string;

      /** openid */
      openid?: string;
    }

    export class ParamMap {
      /** category */
      category?: number;

      /** empId */
      empId?: number;

      /** endMonth */
      endMonth?: string;

      /** pageNum */
      pageNum?: number;

      /** pageSize */
      pageSize?: number;

      /** startMonth */
      startMonth?: string;
    }

    export class PersonInfoRespBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.PersonInfoRespData;

      /** message */
      message?: string;
    }

    export class PersonInfoRespData {
      /** accountId */
      accountId?: string;

      /** bankAccount */
      bankAccount?: string;

      /** bankCity */
      bankCity?: string;

      /** bankName */
      bankName?: string;

      /** birthday */
      birthday?: string;

      /** company */
      company?: string;

      /** currentSalary */
      currentSalary?: string;

      /** educationLevel */
      educationLevel?: string;

      /** email */
      email?: string;

      /** empId */
      empId?: string;

      /** empName */
      empName?: string;

      /** ethnic */
      ethnic?: string;

      /** gender */
      gender?: string;

      /** healthStatus */
      healthStatus?: string;

      /** idCardNegative */
      idCardNegative?: string;

      /** idCardNum */
      idCardNum?: string;

      /** idCardPositive */
      idCardPositive?: string;

      /** jobStatus */
      jobStatus?: string;

      /** mobilePhoneNum */
      mobilePhoneNum?: string;

      /** otherBankName */
      otherBankName?: string;

      /** picUrl */
      picUrl?: string;

      /** politicalStatus */
      politicalStatus?: string;

      /** relation */
      relation?: Array<defs.pact.PersonRelationData>;

      /** residentAddress */
      residentAddress?: string;

      /** residentCity */
      residentCity?: string;

      /** residentProvince */
      residentProvince?: string;

      /** residentZipCode */
      residentZipCode?: string;

      /** socialSecurity */
      socialSecurity?: Array<defs.pact.PersonSocialSecurityData>;

      /** subbranch */
      subbranch?: string;

      /** workExperience */
      workExperience?: string;
    }

    export class PersonRelationData {
      /** birthDate */
      birthDate?: string;

      /** idCardNum */
      idCardNum?: string;

      /** name */
      name?: string;

      /** proofRelPath */
      proofRelPath?: string;

      /** relationship */
      relationship?: string;
    }

    export class PersonSocialSecurityData {
      /** acct */
      acct?: string;

      /** cityName */
      cityName?: string;

      /** empId */
      empId?: string;

      /** empName */
      empName?: string;

      /** insuranceName */
      insuranceName?: string;

      /** ssGroupType */
      ssGroupType?: string;
    }

    export class QueryKmCategoryBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.QueryKmCategoryData>;

      /** message */
      message?: string;
    }

    export class QueryKmCategoryData {
      /** categoryName */
      categoryName?: string;

      /** id */
      id?: string;

      /** parentCategoryName */
      parentCategoryName?: string;

      /** parentId */
      parentId?: string;

      /** tenantId */
      tenantId?: string;
    }

    export class QueryKnowledgeBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.QueryKnowledgeData>;

      /** message */
      message?: string;
    }

    export class QueryKnowledgeData {
      /** allowReview */
      allowReview?: string;

      /** approvalProcess */
      approvalProcess?: string;

      /** categoryName */
      categoryName?: string;

      /** content */
      content?: string;

      /** creationBy */
      creationBy?: string;

      /** creationByName */
      creationByName?: string;

      /** creationDate */
      creationDate?: string;

      /** creationUser */
      creationUser?: string;

      /** id */
      id?: string;

      /** isFavorites */
      isFavorites?: string;

      /** keywords */
      keywords?: string;

      /** kmCategoryFk */
      kmCategoryFk?: string;

      /** kmCategoryName */
      kmCategoryName?: string;

      /** kmGCategoryFk */
      kmGCategoryFk?: string;

      /** kmGPCategoryFk */
      kmGPCategoryFk?: string;

      /** kmPCategoryFk */
      kmPCategoryFk?: string;

      /** knowledgeType */
      knowledgeType?: string;

      /** lastModifiedBy */
      lastModifiedBy?: string;

      /** lastModifiedByName */
      lastModifiedByName?: string;

      /** lastModifiedDate */
      lastModifiedDate?: string;

      /** matchingDegree */
      matchingDegree?: string;

      /** name */
      name?: string;

      /** source */
      source?: string;

      /** status */
      status?: string;

      /** subject */
      subject?: string;

      /** summary */
      summary?: string;

      /** tenantId */
      tenantId?: string;

      /** validDateTo */
      validDateTo?: string;

      /** viewTimes */
      viewTimes?: string;
    }

    export class RespWrapper<T0 = any> {
      /** 返回状态code */
      code?: number;

      /** 具体的返回结果 */
      data?: T0;

      /** 错误信息 */
      msg?: string;
    }

    export class SmsParam {
      /** accountId */
      accountId?: string;

      /** cellphone */
      cellphone?: string;

      /** idCardNum */
      idCardNum?: string;

      /** openId */
      openId?: string;

      /** vcode */
      vcode?: string;
    }

    export class SmsResponseBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.SmsResponseData;

      /** message */
      message?: string;
    }

    export class SmsResponseData {
      /** mobilePhoneNum */
      mobilePhoneNum?: string;

      /** result */
      result?: string;
    }

    export class TemplateMessageParam {
      /** busId */
      busId?: string;

      /** empId */
      empId?: string;

      /** first */
      first?: string;

      /** id */
      id?: string;

      /** idCard */
      idCard?: string;

      /** keyword1 */
      keyword1?: string;

      /** keyword2 */
      keyword2?: string;

      /** keyword3 */
      keyword3?: string;

      /** keyword4 */
      keyword4?: string;

      /** messageId */
      messageId?: string;

      /** openId */
      openId?: string;

      /** remark */
      remark?: string;

      /** reservationId */
      reservationId?: string;

      /** typeCode */
      typeCode?: string;
    }

    export class TestInfo {
      /** id */
      id?: number;

      /** name */
      name?: string;

      /** pwd */
      pwd?: string;
    }

    export class ThingItemsBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.ThingItemsData>;

      /** message */
      message?: string;
    }

    export class ThingItemsData {
      /** key */
      key?: string;

      /** value */
      value?: string;
    }

    export class ThingsResponseBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.ThingsResponseData>;

      /** message */
      message?: string;
    }

    export class ThingsResponseData {
      /** key */
      key?: string;

      /** value */
      value?: string;
    }

    export class UploadEntryFileParam {
      /** accountId */
      accountId?: string;

      /** businessId */
      businessId?: string;

      /** cityCode */
      cityCode?: string;

      /** mediaId */
      mediaId?: string;

      /** openId */
      openId?: string;

      /** type */
      type?: string;
    }

    export class UploadEntryFileRespBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.UploadEntryFileRespData;

      /** message */
      message?: string;
    }

    export class UploadEntryFileRespData {
      /** address */
      address?: string;

      /** fileId */
      fileId?: string;

      /** idCard */
      idCard?: string;

      /** name */
      name?: string;

      /** picUrl */
      picUrl?: string;

      /** result */
      result?: string;

      /** sex */
      sex?: string;

      /** validEnd */
      validEnd?: string;

      /** validStart */
      validStart?: string;
    }

    export class UuidBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.UuidData;

      /** message */
      message?: string;
    }

    export class UuidData {
      /** msg */
      msg?: string;

      /** status */
      status?: string;

      /** uuid */
      uuid?: string;
    }

    export class VcodeBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.VcodeData;

      /** message */
      message?: string;
    }

    export class VcodeData {
      /** base64 */
      base64?: string;
    }

    export class WageDetailListPayBean {
      /** payamt */
      payamt?: string;

      /** payname */
      payname?: string;

      /** paytype */
      paytype?: string;
    }

    export class WageDetailListRespBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.WageDetailListPayBean>;

      /** message */
      message?: string;
    }

    export class WageDetailTotalRespBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.WageDetailTotalRespData;

      /** message */
      message?: string;
    }

    export class WageDetailTotalRespData {
      /** classId */
      classId?: string;

      /** custName */
      custName?: string;

      /** empId */
      empId?: string;

      /** f1 */
      f1?: string;

      /** f10 */
      f10?: string;

      /** f2 */
      f2?: string;

      /** f3 */
      f3?: string;

      /** sendId */
      sendId?: string;

      /** sendMonth */
      sendMonth?: string;
    }

    export class WageListRespBean {
      /** code */
      code?: string;

      /** data */
      data?: Array<defs.pact.WageListRespData>;

      /** message */
      message?: string;
    }

    export class WageListRespData {
      /** classId */
      classId?: string;

      /** custName */
      custName?: string;

      /** empId */
      empId?: string;

      /** f1 */
      f1?: string;

      /** f10 */
      f10?: string;

      /** f2 */
      f2?: string;

      /** f3 */
      f3?: string;

      /** pages */
      pages?: number;

      /** sendId */
      sendId?: string;

      /** sendMonth */
      sendMonth?: string;

      /** wageBatchStatus */
      wageBatchStatus?: string;

      /** wageTaxStatus */
      wageTaxStatus?: string;
    }

    export class WageListTotalRespBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.pact.WageListTotalRespData;

      /** message */
      message?: string;
    }

    export class WageListTotalRespData {
      /** f1 */
      f1?: string;

      /** f10 */
      f10?: string;

      /** f2 */
      f2?: string;

      /** f3 */
      f3?: string;
    }
  }
}

declare namespace API {
  export namespace pact {
    /**
     * App Bind Controller
     */
    export namespace appBind {
      /**
       * 根据open_id查询account_id
       * /yc-wepact-mobile/appbind/getFollowerInfoByOpenId
       */
      export namespace getFollowerInfoByOpenId {
        export class Params {
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.FollowerInfo;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取员工绑定信息
       * /yc-wepact-mobile/appbind/getbindInfo
       */
      export namespace getbindInfo {
        export class Params {
          /** empId */
          empId?: string;
        }

        export type Response = defs.pact.JsonMessage;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 取消绑定/绑定
       * /yc-wepact-mobile/appbind/updateStatus
       */
      export namespace updateStatus {
        export class Params {}

        export type Response = defs.pact.JsonMessage;
        export const request: (
          data?: defs.pact.AppBindInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.AppBindInfo,
          options?: Taro.request.CommonUseRequestOption<defs.pact.AppBindInfo>,
        ) => Taro.request.CommonUseResultType<Response, defs.pact.AppBindInfo>;
      }
    }

    /**
     * Appoint Controller
     */
    export namespace appoint {
      /**
       * 新建预约接口
       * /yc-wepact-mobile/appoint/appointment
       */
      export namespace appointment {
        export class Params {}

        export type Response = defs.pact.InsertResponseBean;
        export const request: (
          data?: defs.pact.InsertParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.InsertParam,
          options?: Taro.request.CommonUseRequestOption<defs.pact.InsertParam>,
        ) => Taro.request.CommonUseResultType<Response, defs.pact.InsertParam>;
      }

      /**
       * 取消预约接口
       * /yc-wepact-mobile/appoint/cancelAppointment
       */
      export namespace cancelAppointment {
        export class Params {}

        export type Response = defs.pact.AppointmentCancelBean;
        export const request: (
          data?: defs.pact.AppointmentCancelParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.AppointmentCancelParam,
          options?: Taro.request.CommonUseRequestOption<defs.pact.AppointmentCancelParam>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.pact.AppointmentCancelParam
        >;
      }

      /**
       * 获取城市接口
       * /yc-wepact-mobile/appoint/getAllcitys
       */
      export namespace getAllcitys {
        export class Params {
          /** cityName */
          cityName?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.CityBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 预约详情接口
       * /yc-wepact-mobile/appoint/getAppointmentDetail
       */
      export namespace getAppointmentDetail {
        export class Params {
          /** bookingId */
          bookingId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.AppointmentDetailBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 预约列表接口
       * /yc-wepact-mobile/appoint/getAppointmentList
       */
      export namespace getAppointmentList {
        export class Params {
          /** createDt */
          createDt?: string;
          /** empId */
          empId?: string;
          /** openId */
          openId?: string;
          /** pageNo */
          pageNo?: string;
        }

        export type Response = defs.pact.AppointmentListBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 预约人数接口
       * /yc-wepact-mobile/appoint/getAppointmentNum
       */
      export namespace getAppointmentNum {
        export class Params {
          /** bookingDate */
          bookingDate?: string;
          /** cityId */
          cityId?: string;
          /** dateType */
          dateType?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.AppointmentNumBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 分公司信息拉取接口
       * /yc-wepact-mobile/appoint/getBranchCompanies
       */
      export namespace getBranchCompanies {
        export class Params {
          /** cityId */
          cityId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.CompanyResponseBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 预约业务项目
       * /yc-wepact-mobile/appoint/getBusinessItems
       */
      export namespace getBusinessItems {
        export class Params {
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.BusinessItemBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 具体项目
       * /yc-wepact-mobile/appoint/getConcreteItems
       */
      export namespace getConcreteItems {
        export class Params {
          /** openId */
          openId?: string;
          /** typeId */
          typeId?: string;
        }

        export type Response = defs.pact.ThingItemsBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 所属材料列表接口
       * /yc-wepact-mobile/appoint/getMaterialList
       */
      export namespace getMaterialList {
        export class Params {
          /** openId */
          openId?: string;
          /** subTypeId */
          subTypeId?: string;
        }

        export type Response = defs.pact.MaterBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 事务类别接口
       * /yc-wepact-mobile/appoint/getTransactionCategories
       */
      export namespace getTransactionCategories {
        export class Params {
          /** categoryId */
          categoryId?: string;
          /** cityId */
          cityId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.ThingsResponseBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Auth Controller
     */
    export namespace auth {
      /**
       * 身份绑定验证
       * /yc-wepact-mobile/auth/authenticationBinding
       */
      export namespace authenticationBinding {
        export class Params {}

        export type Response = defs.pact.AuthResponseBean;
        export const request: (
          data?: defs.pact.AuthParamBean,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.AuthParamBean,
          options?: Taro.request.CommonUseRequestOption<defs.pact.AuthParamBean>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.pact.AuthParamBean
        >;
      }

      /**
       *  获取kaptcha验证码
       * /yc-wepact-mobile/auth/getVcode
       */
      export namespace getVcode {
        export class Params {
          /** accountId */
          accountId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.VcodeBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 绑定获取验证码
       * /yc-wepact-mobile/auth/sendSMS
       */
      export namespace sendSMS {
        export class Params {}

        export type Response = defs.pact.SmsResponseBean;
        export const request: (
          data?: defs.pact.SmsParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.SmsParam,
          options?: Taro.request.CommonUseRequestOption<defs.pact.SmsParam>,
        ) => Taro.request.CommonUseResultType<Response, defs.pact.SmsParam>;
      }

      /**
       *  用户解绑
       * /yc-wepact-mobile/auth/unbindAccount
       */
      export namespace unbindAccount {
        export class Params {
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.AuthResponseBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Busi Controller
     */
    export namespace busi {
      /**
       * 业务详细信息接口
       * /yc-wepact-mobile/busi/getBusinessDetail
       */
      export namespace getBusinessDetail {
        export class Params {
          /** businessId */
          businessId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.BusinessDetailBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 业务查询接口
       * /yc-wepact-mobile/busi/getBusinessList
       */
      export namespace getBusinessList {
        export class Params {
          /** createDt */
          createDt?: string;
          /** empId */
          empId?: string;
          /** openId */
          openId?: string;
          /** pageNo */
          pageNo?: string;
        }

        export type Response = defs.pact.BusinessListBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 具体业务中的反馈信息
       * /yc-wepact-mobile/busi/getFeedbackInfo
       */
      export namespace getFeedbackInfo {
        export class Params {
          /** businessId */
          businessId?: string;
          /** empId */
          empId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.FeedbackBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 工资列表合计查询接口
       * /yc-wepact-mobile/busi/getPersonTotalWages
       */
      export namespace getPersonTotalWages {
        export class Params {
          /** empId */
          empId?: string;
          /** endMonth */
          endMonth?: number;
          /** openId */
          openId?: string;
          /** pageNum */
          pageNum?: number;
          /** pageSize */
          pageSize?: number;
          /** startMonth */
          startMonth?: number;
        }

        export type Response = defs.pact.WageListTotalRespBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 工资详情列表接口
       * /yc-wepact-mobile/busi/getPersonWageDetailList
       */
      export namespace getPersonWageDetailList {
        export class Params {
          /** classId */
          classId?: string;
          /** empId */
          empId?: string;
          /** openId */
          openId?: string;
          /** paytype */
          paytype?: string;
          /** sendId */
          sendId?: string;
          /** sendMonth */
          sendMonth?: string;
        }

        export type Response = defs.pact.WageDetailListRespBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 工资详情合计接口
       * /yc-wepact-mobile/busi/getPersonWageDetailTotal
       */
      export namespace getPersonWageDetailTotal {
        export class Params {
          /** empId */
          empId?: string;
          /** openId */
          openId?: string;
          /** sendMonth */
          sendMonth?: string;
        }

        export type Response = defs.pact.WageDetailTotalRespBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 工资列表查询接口
       * /yc-wepact-mobile/busi/getPersonWages
       */
      export namespace getPersonWages {
        export class Params {
          /** empId */
          empId?: string;
          /** endMonth */
          endMonth?: number;
          /** openId */
          openId?: string;
          /** pageNum */
          pageNum?: number;
          /** pageSize */
          pageSize?: number;
          /** startMonth */
          startMonth?: number;
        }

        export type Response = defs.pact.WageListRespBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 社保公积金查询详情接口
       * /yc-wepact-mobile/busi/getSocialnsuranceAndfundDetail
       */
      export namespace getSocialnsuranceAndfundDetail {
        export class Params {}

        export type Response = defs.pact.FundDetailBean;
        export const request: (
          data?: defs.pact.FundDetailParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.FundDetailParam,
          options?: Taro.request.CommonUseRequestOption<defs.pact.FundDetailParam>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.pact.FundDetailParam
        >;
      }

      /**
       * 社保公积金列表查询接口
       * /yc-wepact-mobile/busi/getSocialnsuranceAndfundList
       */
      export namespace getSocialnsuranceAndfundList {
        export class Params {}

        export type Response = defs.pact.FundData;
        export const request: (
          data?: defs.pact.FundParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.FundParam,
          options?: Taro.request.CommonUseRequestOption<defs.pact.FundParam>,
        ) => Taro.request.CommonUseResultType<Response, defs.pact.FundParam>;
      }
    }

    /**
     * City Dynamics Form Controller
     */
    export namespace cityDynamicsForm {
      /**
       * 获取城市的动态表单项数据
       * /yc-wepact-mobile/dynamics-form/appformItem
       */
      export namespace appgetDynamicItem {
        export class Params {}

        export type Response = object;
        export const request: (
          data?: defs.pact.DynamicFormReqBean,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.DynamicFormReqBean,
          options?: Taro.request.CommonUseRequestOption<defs.pact.DynamicFormReqBean>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.pact.DynamicFormReqBean
        >;
      }

      /**
       * 获取城市的动态文件条目项
       * /yc-wepact-mobile/dynamics-form/file/{cityCode}/{openId}
       */
      export namespace getDynamicFileItem {
        export class Params {
          /** cityCode */
          cityCode: string;
          /** openId */
          openId: string;
          /** uuid */
          uuid?: string;
        }

        export type Response = object;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取城市的动态表单项数据
       * /yc-wepact-mobile/dynamics-form/formItem
       */
      export namespace getDynamicItem {
        export class Params {}

        export type Response = object;
        export const request: (
          data?: defs.pact.DynamicFormReqBean,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.DynamicFormReqBean,
          options?: Taro.request.CommonUseRequestOption<defs.pact.DynamicFormReqBean>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.pact.DynamicFormReqBean
        >;
      }
    }

    /**
     * City Info Controller
     */
    export namespace cityInfo {
      /**
       * 获取所有城市接口
       * /yc-wepact-mobile/cityinfo/getAllcitys
       */
      export namespace getAllcitys {
        export class Params {
          /** cityname */
          cityname?: string;
        }

        export type Response = Array<defs.pact.MoreCityInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Entry Controller
     */
    export namespace entry {
      /**
       * 获取易才HRO系统中定点医院的信息
       * /yc-wepact-mobile/entry/appdesignatedHospitals
       */
      export namespace appdesignatedHospitals {
        export class Params {
          /** cityId */
          cityId?: string;
          /** filterValue */
          filterValue?: string;
          /** hosCountyName */
          hosCountyName?: string;
          /** hosLevel */
          hosLevel?: string;
          /** token */
          token?: string;
        }

        export type Response = defs.pact.HosResponseBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 入职办理-新建入职办理接口
       * /yc-wepact-mobile/entry/appinsertEntryInfo
       */
      export namespace appinsertEntryInfo {
        export class Params {}

        export type Response = defs.pact.EntryResponseBean;
        export const request: (
          data?: defs.pact.EntryParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.EntryParam,
          options?: Taro.request.CommonUseRequestOption<defs.pact.EntryParam>,
        ) => Taro.request.CommonUseResultType<Response, defs.pact.EntryParam>;
      }

      /**
       * 删除入职信息文件
       * /yc-wepact-mobile/entry/delEntryFile/{fileId}
       */
      export namespace delEntryFile {
        export class Params {
          /** fileId */
          fileId: number;
        }

        export type Response = defs.pact.UploadEntryFileRespBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 同时删除微信和hro的入职信息文件
       * /yc-wepact-mobile/entry/delEntryFileWithHRO
       */
      export namespace delEntryFileWithHro {
        export class Params {
          /** accountId */
          accountId?: string;
          /** 同一个入职办理数据中文件的busUUID相同 */
          businessId?: string;
          /** cityCode */
          cityCode?: string;
          /** openId */
          openId?: string;
          /** 文件类型 */
          type?: string;
        }

        export type Response = defs.pact.EntryResponseBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取易才HRO系统中定点医院的信息
       * /yc-wepact-mobile/entry/designatedHospitals
       */
      export namespace designatedHospitals {
        export class Params {
          /** cityId */
          cityId?: string;
          /** filterValue */
          filterValue?: string;
          /** hosCountyName */
          hosCountyName?: string;
          /** hosLevel */
          hosLevel?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.HosResponseBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 回显文件再次提交
       * /yc-wepact-mobile/entry/echoFileSubmit
       */
      export namespace echoFileSubmit {
        export class Params {}

        export type Response = defs.pact.EchoFileSubmitResp;
        export const request: (
          data?: defs.pact.EchoFileSubmitReqBean,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.EchoFileSubmitReqBean,
          options?: Taro.request.CommonUseRequestOption<defs.pact.EchoFileSubmitReqBean>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.pact.EchoFileSubmitReqBean
        >;
      }

      /**
       * 入职办理-身份证识别接口
       * /yc-wepact-mobile/entry/getOCRImageInfo
       */
      export namespace getOCRImageInfo {
        export class Params {}

        export type Response = defs.pact.OcrResponseBean;
        export const request: (
          data?: defs.pact.OcrParamBean,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.OcrParamBean,
          options?: Taro.request.CommonUseRequestOption<defs.pact.OcrParamBean>,
        ) => Taro.request.CommonUseResultType<Response, defs.pact.OcrParamBean>;
      }

      /**
       * 根据accountId和cityId来获取能否创建新入职记录的状态，以及uuid
       * /yc-wepact-mobile/entry/getuuid
       */
      export namespace getuuid {
        export class Params {
          /** accountId */
          accountId?: string;
          /** cityId */
          cityId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.UuidBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 身份证正反面合成
       * /yc-wepact-mobile/entry/idCardCompose
       */
      export namespace idCardCompose {
        export class Params {}

        export type Response = defs.pact.IdCardComposeRespBean;
        export const request: (
          data?: defs.pact.IdCardComposePram,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.IdCardComposePram,
          options?: Taro.request.CommonUseRequestOption<defs.pact.IdCardComposePram>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.pact.IdCardComposePram
        >;
      }

      /**
       * 入职办理-新建入职办理接口
       * /yc-wepact-mobile/entry/insertEntryInfo
       */
      export namespace insertEntryInfo {
        export class Params {}

        export type Response = defs.pact.EntryResponseBean;
        export const request: (
          data?: defs.pact.EntryParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.EntryParam,
          options?: Taro.request.CommonUseRequestOption<defs.pact.EntryParam>,
        ) => Taro.request.CommonUseResultType<Response, defs.pact.EntryParam>;
      }

      /**
       * 上传入职信息文件
       * /yc-wepact-mobile/entry/uploadEntryFile
       */
      export namespace uploadEntryFile {
        export class Params {}

        export type Response = defs.pact.UploadEntryFileRespBean;
        export const request: (
          data?: defs.pact.UploadEntryFileParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.pact.UploadEntryFileParam,
          options?: Taro.request.CommonUseRequestOption<defs.pact.UploadEntryFileParam>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.pact.UploadEntryFileParam
        >;
      }

      /**
        * form形式上传入职信息文件
form形式上传入职信息文件
        * /yc-wepact-mobile/entry/uploadEntryFileByForm
        */
      export namespace uploadEntryFileByForm {
        export class Params {}

        export type Response = defs.pact.UploadEntryFileRespBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Login Controller
     */
    export namespace login {
      /**
       * 取消关注
       * /yc-wepact-mobile/login/cancelFollow
       */
      export namespace cancelFollow {
        export class Params {
          /** accountId */
          accountId?: string;
          /** cmpToken */
          cmpToken?: string;
        }

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 新用户注册
       * /yc-wepact-mobile/login/follow
       */
      export namespace follow {
        export class Params {
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.FollowResponse;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 再次关注
       * /yc-wepact-mobile/login/followAgain
       */
      export namespace followAgain {
        export class Params {
          /** accountId */
          accountId?: string;
          /** cmpToken */
          cmpToken?: string;
        }

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Lowcode Controller
     */
    export namespace lowcode {
      /**
       * 根据cityCode, mainId 和openId 获取最新的数据和模板
       * /yc-wepact-mobile/clc/fetchDataAndTemplate
       */
      export namespace fetchDataAndTemplate {
        export class Params {
          /** cityCode */
          cityCode: string;
          /** mainId */
          mainId: string;
          /** openId */
          openId: string;
        }

        export type Response = defs.pact.RespWrapper<defs.pact.ClcData>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据cityCode获取最新的模板
       * /yc-wepact-mobile/clc/fetchTemplateByJsonId
       */
      export namespace fetchTemplateByJsonId {
        export class Params {
          /** accountId */
          accountId: string;
          /** templateJsonId */
          templateJsonId: string;
        }

        export type Response = defs.pact.RespWrapper<defs.pact.ClcTemplateInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据cityCode获取最新的模板
       * /yc-wepact-mobile/clc/lastestTemplateByCity
       */
      export namespace fetchLastestTemplate {
        export class Params {
          /** accountId */
          accountId: string;
          /** cityCode */
          cityCode: string;
        }

        export type Response = defs.pact.RespWrapper<defs.pact.ClcTemplateInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Per Controller
     */
    export namespace per {
      /**
       * 个人菜单权限接口
       * /yc-wepact-mobile/per/getMenuPermissions
       */
      export namespace getMenuPermissions {
        export class Params {
          /** empId */
          empId?: string;
        }

        export type Response = defs.pact.MenuResponseBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取易才HRO系统中易才管家信息
       * /yc-wepact-mobile/per/getSteward
       */
      export namespace getSteward {
        export class Params {
          /** empId */
          empId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.ButlerResponseBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 查询HRO系统中的个人信息
       * /yc-wepact-mobile/per/personInformation
       */
      export namespace personInformation {
        export class Params {
          /** accountId */
          accountId?: string;
          /** openId */
          openId?: string;
        }

        export type Response = defs.pact.PersonInfoRespBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Policy Controller
     */
    export namespace policy {
      /**
       * 知识类别查询接口
       * /yc-wepact-mobile/policy/queryKmCategory
       */
      export namespace queryKmCategory {
        export class Params {
          /** parentId */
          parentId?: string;
        }

        export type Response = defs.pact.QueryKmCategoryBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 知识详情查询接口
       * /yc-wepact-mobile/policy/queryKnowledge
       */
      export namespace queryKnowledge {
        export class Params {
          /** categoryId */
          categoryId?: string;
        }

        export type Response = defs.pact.QueryKnowledgeBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Template Message Controller
     */
    export namespace templateMessage {
      /**
       * 批量处理模板消息发送
       * /yc-wepact-mobile/template-msg/send
       */
      export namespace batchSend {
        export class Params {}

        export type Response = object;
        export const request: (
          data?: Array<defs.pact.TemplateMessageParam>,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Array<defs.pact.TemplateMessageParam>,
          options?: Taro.request.CommonUseRequestOption<
            Array<defs.pact.TemplateMessageParam>
          >,
        ) => Taro.request.CommonUseResultType<
          Response,
          Array<defs.pact.TemplateMessageParam>
        >;
      }
    }

    /**
     * Test Controller
     */
    export namespace test {
      /**
       * test
       * /test/get/{cityCode}
       */
      export namespace test {
        export class Params {}

        export type Response = defs.pact.TestInfo;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * restful风格
     */
    export namespace wxConfig {
      /**
       * 根据code获取emp
       * /yc-wepact-mobile/getEpmInfoByCode
       */
      export namespace getEpmInfoByCode {
        export class Params {
          /** code */
          code: string;
        }

        export type Response = defs.pact.JsonMessage;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据code获取emp
       * /yc-wepact-mobile/getEpmInfoByOpenId
       */
      export namespace getEpmInfoByOpenId {
        export class Params {
          /** openId */
          openId: string;
        }

        export type Response = defs.pact.JsonMessage;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据code获取openid
       * /yc-wepact-mobile/getOpenidByCode
       */
      export namespace getOpenidByCode {
        export class Params {
          /** code */
          code: string;
        }

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取微信签名
       * /yc-wepact-mobile/getSignature
       */
      export namespace getSignature {
        export class Params {
          /** url */
          url?: string;
        }

        export type Response = defs.pact.JsonMessage;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }
  }
}
