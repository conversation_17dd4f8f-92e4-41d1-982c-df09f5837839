/*
 * @Author: your name
 * @Date: 2021-09-01 14:57:00
 * @LastEditTime: 2021-11-25 13:37:36
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\pre-tax\declare\chidrenEducation\index.tsx
 */
/**
 * 税前抵扣申请-子女教育
 */
import { Fragment, useEffect, useState } from 'react'
import { View } from '@tarojs/components'
import { BottomBtn, Form, FormItemProps, withPage,useForm } from '@components'
import { useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils/transforms'
import { navigateTo, getGlobalData } from '@utils'
import { users } from '@apis/users'
import isEmpty from 'lodash/isEmpty'
import styles from './index.module.scss'

const ratio = [
  { key: 0, value: '50%' },
  { key: 1, value: '100%' }
]
const cardType = [
  { key: '1', value: '居民身份证' },
  { key: '2', value: '中国护照' },
  { key: '3', value: '港澳居民来往内地通行证' },
  { key: '4', value: '港澳居民居住证' },
  { key: '5', value: '台湾居民来往大陆通行证' },
  { key: '6', value: '台湾居民居住证' },
  { key: '7', value: '外国护照' },
  { key: '8', value: '外国人永久居留身份证' },
  { key: '9', value: '其他个人证件' }
]
const educationStatu = [
  { key: '0', value: '学前教育阶段' },
  { key: '1', value: '义务教育' },
  { key: '2', value: '高中教育阶段' },
  { key: '3', value: '高等教育' }
]
const Index = () => {
  const [country, setCountry] = useState<string>('')
  const { childrenId = '' } = useRouter().params
  const { empId } = getGlobalData<'account'>('account')
  const { type = 'create' } = useRouter<{ type: 'create' | 'update' }>().params
  const form = useForm()
  const childrenCardType = form.watch('childrenCardType')
  const { result } = users.baseData.getBaseData.useRequest({ pageNum: '1', typeId: '911' })
  const openBankOptions = result?.data || ([] as defs.users.BaseData[])
  const onSubmit = (values: any) => {
    // console.log('values----', values)
    if(childrenId !== '') {
      users.user.saveChildrenEdu.request(values).then(ress => {
        if (ress?.code == 0) {
          Taro.navigateTo({
            url: '/pages/pre-tax/declare/submitSuccessfully/index'
          })
        }
      })
    }else {
      users.user.getChildrenBasicInfo
      .request({
        employeeId: empId
      })
      .then(res => {
        // console.log(res,'查看重复信息')
        if (!isEmpty(res.data)) {
          let temp = res.data?.find(item => {
            return item.childrenCardNumber === values.childrenCardNumber 
          })
          // console.log(temp, 'temp')
          if (temp) {
            // console.log('身份证重复')
            Taro.showToast({
              title: '身份证重复',
              icon: 'none',
              duration: 2000
            })
            return
          }
        }
        users.user.saveChildrenEdu.request(values).then(ress => {
          if (ress?.code == 0) {
            Taro.navigateTo({
              url: '/pages/pre-tax/declare/submitSuccessfully/index'
            })
          }
        })
      })
    }
  }
  useEffect(() => {
    form.setValue('employeeId', empId)
    form.setValue('childrenId', '')
    form.setValue('deductionType', 1)
  }, [empId])
  useEffect(() => {
    if (childrenId !== '') {
      users.user.getChildrenEduInfo
        .request({
          childrenId: childrenId
        })
        .then(res => {
          // console.log(res,'有数据res')
          !isEmpty(res?.data) && form.reset({ ...res?.data, employeeId: empId, deductionType: 1, deductionMonth: '', childrenRelation: 1 })
          users.user.getCountryInfo.request({
            countryName: '',
          }).then((countryRes) => {
            // console.log(countryRes,'countryRes')
            !isEmpty(countryRes?.data) && countryRes?.data.map(async (item,index) => {
              if(item.countryId == res.data?.countryId) {
                await form.setValue('countryName', item.countryName)
                await form.setValue('countryId', item.countryId)
              }
              if(item.countryId == res.data?.eduCountry) {
                form.setValue('eduCountryName', item.countryName)
                form.setValue('eduCountry', item.countryId)
              }
            })
          })
        })
    }else {
      form.setValue('employeeId', empId)
      form.setValue('childrenId', '')
      form.setValue('deductionType', 1)
      form.setValue('deductionMonth', '')
      form.setValue('childrenRelation', 1)
    }
  }, [childrenId])

  const scrollStyle = getScrollStyle({ bottom: 120 })
  const columns: FormItemProps[] = [
    {
      name: 'childrenProportion',
      type: 'single',
      title: '本人扣除比例',
      options: ratio,
      rules: { required: true }
    },
    {
      name: 'childrenName',
      type: 'text',
      title: '子女姓名',
      rules: { required: true, maxLength: 10 }
    },
    {
      name: 'countryId',
      type: 'page_choice',
      title: '国籍(地区)',
      rules: { required: true },
      pageOptions: {
        keys: ['countryId', 'countryName'],
        labelKey: 'countryName',
        url: '/country',
        eventName: 'countryId'
      }
    },
    {
      name: 'childrenCardType',
      type: 'select',
      title: '身份证件类型',
      options: cardType,
      rules: { required: true }
    },
    {
      name: 'childrenCardNumber',
      type: 'id_card',
      title: '身份证件号码',
      rules: { 
        required: true,
        pattern: childrenCardType == 1 ? {
          value: /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/,
          message: '请输入正确的身份证格式'
        }: {
          value: /([^\.\d]|^)(\d+)([^\.\d]|$)/,
          message: ''
        }
      }
    },
    {
      name: 'childrenBirthDate',
      type: 'date',
      title: '出生日期',
      rules: { required: true }
    },
    {
      name: 'eduPhase',
      type: 'select',
      title: '当前受教育阶段',
      options: educationStatu,
      rules: { required: true }
    },
    {
      name: 'eduStartDate',
      type: 'date',
      title: '当前受教育阶段起始时间',
      rules: { required: true }
    },
    {
      name: 'eduEndDate',
      type: 'date',
      title: '当前受教育阶段结束时间',
      rules: { required: false }
    },
    {
      name: 'eduStopDate',
      type: 'date',
      title: '教育终止时间',
      rules: { required: false }
    },
    {
      name: 'eduCountry',
      type: 'page_choice',
      title: '当前就读国家(地区)',
      rules: { required: true },
      pageOptions: {
        keys: ['eduCountry', 'eduCountryName'],
        labelKey: 'eduCountryName',
        url: '/country',
        eventName: 'eduCountry'
      }
    },
    {
      name: 'eduSchoolName',
      type: 'text',
      title: '当前就读学校',
      rules: { required: true, maxLength: 20 }
    }
  ]
  return (
    <Fragment>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '保存',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </Fragment>
  )
}

export default withPage(Index)
