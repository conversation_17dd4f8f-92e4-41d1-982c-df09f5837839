/**
 * @description form形式上传入职信息文件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.UploadEntryFileRespBean;
export const path = '/yc-wepact-mobile/entry/uploadEntryFileByForm';
export const method = 'POST';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
