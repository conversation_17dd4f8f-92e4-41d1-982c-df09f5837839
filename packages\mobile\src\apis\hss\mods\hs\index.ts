/**
 * @description Hs Controller
 */
import * as HsDeclareConfirm from './HsDeclareConfirm';
import * as getBankInfoByOpenid from './getBankInfoByOpenid';
import * as getDataListByOpenid from './getDataListByOpenid';
import * as getDeductionOtherInfoByOpenid from './getDeductionOtherInfoByOpenid';
import * as getDeductionSpecialInfoByOpenid from './getDeductionSpecialInfoByOpenid';
import * as getHsCity from './getHsCity';
import * as getHsDataByYear from './getHsDataByYear';
import * as getHsDeclare from './getHsDeclare';
import * as getHsResult from './getHsResult';
import * as getIncomeInfoByOpenid from './getIncomeInfoByOpenid';
import * as insertBankInfo from './insertBankInfo';
import * as insertDeductionOtherInfo from './insertDeductionOtherInfo';
import * as insertDeductionSpecialInfo from './insertDeductionSpecialInfo';
import * as insertIncomeInfo from './insertIncomeInfo';
import * as submitHs from './submitHs';
import * as updatePayInfo from './updatePayInfo';

export {
  HsDeclareConfirm,
  getBankInfoByOpenid,
  getDataListByOpenid,
  getDeductionOtherInfoByOpenid,
  getDeductionSpecialInfoByOpenid,
  getHsCity,
  getHsDataByYear,
  getHsDeclare,
  getHsResult,
  getIncomeInfoByOpenid,
  insertBankInfo,
  insertDeductionOtherInfo,
  insertDeductionSpecialInfo,
  insertIncomeInfo,
  submitHs,
  updatePayInfo,
};
