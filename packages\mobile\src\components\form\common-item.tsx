import React, { FunctionComponent, useEffect } from 'react'
import { View, Text, Textarea, Picker, Image } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { navigateTo } from '@utils'
import isNumber from 'lodash/isNumber'
import isString from 'lodash/isString'
import isPlainObject from 'lodash/isPlainObject'
import isEmpty from 'lodash/isEmpty'
import icon_arrow from '@assets/icon/icon-arrow.png'
import styles from './index.module.scss'
import { CommonItemProps, FormProps } from './type'
import ProvinceCity from './province-city'
// import DateSelector from './date-selector'
import Input from '../input'
import WorkdayPicker from './work-day-picker'
import DateSelector from './date-selector'

const typeToPickerModeMap = {
  select: 'selector',
  province_city: 'multiSelector',
}

const typeToInputTypeMap = {
  email: 'selector',
  id_card: 'idcard',
  mobile: 'number',
  post_code: 'number'
}

const CommonItem: FunctionComponent<Omit<CommonItemProps, 'ref'> & FormProps> = React.forwardRef((props, ref) => {
  const {
    form,
    type,
    level,
    options = [] as any,
    pageOptions,
    rangeKeys = ['key', 'value'],
    placeholder,
    ...reset
  } = props
  const { name, onChange, value } = reset
  useEffect(() => {
    if (type === 'page_choice') {
      const handle = (args: any) => {
        // console.log('监听页面事件回调----', name, args)
        // form?.setValue(name,args)
        // 更新
        form?.reset({ ...form?.getValues(), ...args })
        // onChange(args[name])
      }
      // console.log('监听页面事件--', name)
      Taro.eventCenter.on(name, handle)
      return () => {
        Taro.eventCenter.off(name, handle)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rangeKeys, onChange, name, type])
  if (['text', 'email', 'id_card', 'mobile', 'post_code', 'medical'].includes(type)) {
    let inputType = Number.isNaN(Number(value)) ? 'test' : typeToInputTypeMap[type]
    return (
      <Input
        ref={ref}
        onInput={(e: any) => onChange(e)}
        className={level === 2 ? styles.input_text : styles.input}
        type={inputType}
        {...reset}
      />
    )
  } else if (type === 'textarea') {
    return <Textarea ref={ref} className={styles.textarea} {...reset} />
  } else if (type === 'province_city') {
    return <ProvinceCity {...props} ref={ref as any} />
  } else if (type === 'date_selector') {
    // return <DateSelector {...props} ref={ref as any} />
    return <WorkdayPicker {...props} />  
  } else if (['select', 'date', 'region', 'time', 'province_city', 'multiSelector'].includes(type)) {
    const mode = typeToPickerModeMap[type] || type
    const isObject = isPlainObject(options?.[0]) && !isEmpty(options?.[0])
    let label = isObject ? options.find(item => item[rangeKeys[0]] == value)?.[rangeKeys[1]] : reset.value
    if (label == -1) {
      label = ''
    }
    let restObj = {}
    if (mode === 'time' && reset.start && !value) {
      // 解决start 大于当前值的报错的bug
      restObj = { value: reset.start }
    }
    if (mode === 'date') {
      restObj = { value: reset.start, start: reset.start || '1930-01-01' }
    }
    //解决time类型，value为空时，报无效值问题
    let otherValue = mode === 'time' ? {} : { value : '' }    
    return (
      <Picker
        ref={ref}
        mode={mode}
        range={options}
        rangeKey={isObject ? rangeKeys[1] : undefined}
        {...reset}
        {...restObj}
        onChange={
          isObject
            ? (e: any) => {
                const obj = options[e.detail.value]
                reset.onChange(obj?.[rangeKeys[0]])
              }
            : reset.onChange
        }
        {...otherValue}
      >
        <View className={level === 2 ? styles.full_pick : styles.picker}>
          {label ? (
            <Text className={styles.item_text}>{label}</Text>
          ) : (
            <Text className={styles.placeholder}>{placeholder || '请选择'}</Text>
          )}
          <Image className={styles.item_arrow} src={icon_arrow} />
        </View>
      </Picker>
    )
  } else if (type === 'single') {
    return (
      <View className={styles.single}>
        {options?.map((option: any) => {
          let key: any // 录入
          let cValue: any // 显示
          if (isString(option) || isNumber(option)) {
            key = option
            cValue = option
          } else {
            key = option[rangeKeys[0]]
            cValue = option[rangeKeys[1]]
          }
          return (
            <View
              className={styles.tag_wrap}
              key={key}
              style={reset.value === key ? { borderColor: '#b51e25' } : {}}
              onClick={() => {
                if (reset.disabled) {
                  return
                }
                reset.onChange(key)
              }}
            >
              <Text className={styles.tag} style={reset.value === key ? { color: '#b51e25' } : {}}>
                {cValue}
              </Text>
            </View>
          )
        })}
      </View>
    )
  } else if (type === 'page_choice') {
    const { keys = [], eventName = name, url = '/page-choice', labelKey = '', title = reset.title, scen, message } =
      pageOptions || {}
    const values = form?.control?._formValues || {}
    const label = values?.[labelKey]
    if (reset.isCustom) return null
    return (
      <View
        className={styles.picker}
        onClick={e => {
          if (reset.disabled) {
            reset.onClick?.(e)
          } else {
            navigateTo(url as any, { ...message, title, eventName, scen, keys })
          }
        }}
      >
        {label ? (
          <Text className={styles.item_text}>{label}</Text>
        ) : (
          <Text className={styles.placeholder}>{placeholder || '请选择'}</Text>
        )}
        <Image className={styles.item_arrow} src={icon_arrow} />
      </View>
    )
  } else {
    return <Text>{`不支持${type}类型`}</Text>
  }
})

export default CommonItem
