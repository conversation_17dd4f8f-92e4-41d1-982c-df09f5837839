// 基础按钮样式
.wrap {
  width: 190px;
  padding: 16px 20px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: #b51e25;
  margin: 0px 0px 0px 20px;
  border-radius: 8px;
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    background-color: #a01b22;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;

    &:hover {
      background-color: #cccccc;
      transform: none;
    }
  }
}

// 取消按钮样式
.cancel_wrap {
  @extend .wrap;
  background-color: #949C88;

  &:hover {
    background-color: #7a8275;
  }

  &:disabled {
    background-color: #cccccc;

    &:hover {
      background-color: #cccccc;
    }
  }
}

// 基础图标样式
.icon {
  margin-right: 10px;
  width: 30px;
  height: 18px;
  flex-shrink: 0;
}

// 包装图标样式
.icon_wrap {
  @extend .icon;
  width: 24px;
  height: 24px;
}

// 订单图标样式
.order_icon {
  @extend .icon;
  width: 24px;
  height: 24px;
}

// 按钮文本样式
.text {
  font-size: 20px;
  color: #ffffff;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 尺寸变体
.small {
  width: 120px;
  padding: 12px 16px;

  .text {
    font-size: 18px;
  }

  .icon,
  .icon_wrap,
  .order_icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
}

.large {
  width: 240px;
  padding: 20px 24px;

  .text {
    font-size: 24px;
  }

  .icon,
  .icon_wrap,
  .order_icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }
}

// 加载状态
.loading {
  opacity: 0.7;
  pointer-events: none;
}
