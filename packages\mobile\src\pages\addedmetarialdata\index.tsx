import { useEffect, useState } from 'react'
import Taro, { useRouter } from '@tarojs/taro'
import { View, Text, Image } from '@tarojs/components'
import { isEmpty } from 'lodash'
import { BaseUrl, chooseImage } from '@utils'
import { BottomBtn, withPage } from '@components'
import { ncmp } from '@apis/ncmp'
import RequiredImg from '@assets/icon/icon-star.png'
import AddImg from '@assets/getadd.png'
import delImg from '@assets/icon/icon-delete.png'
import styles from './index.module.scss'

const Index = () => {
  const [flag, setflag] = useState(false)
  const [reason, setReason] = useState('')
  const { supplyId, reasonText } = useRouter().params
  const { result, run } = ncmp.elecSign.queryHzSupplyCert.useRequest({
    supplyId
  }, {isToken: true})
  useEffect(() => {
    setReason(reasonText || '')
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  let fileItems = (result?.resultObj as any) || []

  fileItems = fileItems.map(item => {
    return {
      ...item,
      filePath: item.filePath ? `${BaseUrl}/wx-ncmp/elecsign/view?filePath=${item.filePath}` : ''
    }
  })
 
  const delEntryFile = async (item) => {
    await ncmp.elecSign.deteleLaborSupplyCertHz.request({ ...item }, {isToken: true})
    await run()
    Taro.showToast({ title: '删除成功' })
  }
  const previewImage = (url: string) => {
    Taro.previewImage({
      current: url,
      urls: [url]
    })
  }
  const uploadImage = (item: any) => {
    const {supplyCertId, certName} = item
    const data = {
      url: '/wx-ncmp/elecsign/certFileUploadByForm',
      supplyCertId,
      certName
    }
    chooseImage(data, run)
  }
  const handleSumit = () => {
    const requireFlag = fileItems.find(item => item.isMust === '1' && !item.filePath)
    setflag(requireFlag)
    if (requireFlag) {
      Taro.showToast({ title: '请上传必传资料照片', icon: 'none' })
      return
    }
    ncmp.elecSign.submitCert
      .request({
        supplyId
      }, {isToken: true})
      .then(res => {
        if (res.code === '200') {
          const rejectReason = res.resultObj?.rejectReason || ''
          setReason(rejectReason)
          Taro.showToast({ title: '提交成功' })
          Taro.navigateTo({ url: '/pages/induction-elesignature/index' })
        } else {
          Taro.showToast({ title: res.errorMsg || '提交出错了' })
        }
      })
  }
  return (
    <View>
      <View className={styles.wrap}>
        <View className={styles.content}>
          <View className={styles.item_wrap}>
            {fileItems.map(item => (
              <View key={item.certId} className={styles.item}>
                <View className={styles.header}>
                  {item.isMust === '1' && <Image className={styles.required} src={RequiredImg} />}
                  <Text className={styles.title}>{item.certName}</Text>
                </View>
                <View className={styles.img_wrap}>
                  {!isEmpty(item.filePath) && (
                    <Image
                      className={styles.img}
                      src={item.filePath || ''}
                      onClick={() => previewImage(item.filePath || '')}
                    />
                  )}
                  <View className={styles.require_cls}>
                    {item.isMust === '1' && isEmpty(item.filePath) && flag && (
                      <Text className={styles.require_tips}>请填写必填项</Text>
                    )}
                  </View>
                  <View className={styles.del_cls}>
                    {!isEmpty(item.filePath) ? (
                      <Image className={styles.delImg} src={delImg} onClick={() => delEntryFile(item)} />
                    ) : (
                      <Image className={styles.add} src={AddImg} onClick={() => uploadImage(item)} />
                    )}
                  </View>
                </View>
              </View>
            ))}
          </View>
          <View className={styles.text}>{reason && <Text >{`驳回原因：${reason}`}</Text>}</View>
          
        </View>
      
        <BottomBtn btns={[{ title: '提交', onClick: handleSumit }]} />
      </View>
    </View>
  )
}

export default withPage(Index, { needSignature: true })
