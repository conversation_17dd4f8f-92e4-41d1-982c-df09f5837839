/**
 * @description 查询业务办理列表
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** endDt */
  endDt?: string;
  /** startDt */
  startDt?: string;
}

export type Result = defs.ncmp.HttpResult<Array<defs.ncmp.EbmBusinessResp>>;
export const path = '/wx-ncmp/ebmbusiness/selectEbmBusinessList';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
