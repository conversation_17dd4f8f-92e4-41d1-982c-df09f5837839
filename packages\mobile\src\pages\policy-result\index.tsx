import { View, ScrollView, Text, Image } from '@tarojs/components'
import { BottomBtn, withPage } from '@components'
import Taro, { useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils'
import icon_time from '@assets/icon/icon-time.png'
import icon_keyword from '@assets/icon/icon-keyword.png'
import { ncmp } from '@apis/ncmp'
import styles from './index.module.scss'
import './index.global.scss'


const Index = () => {
  const { categoryId, busnameSubtypeId } = useRouter().params
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const { result } = ncmp.policy.getSingleFringeBenefits.useRequest({categoryId, busnameSubtypeId})
  const resultObj = result?.resultObj?.[0] || []
  const list = [
    {title: '基本信息', titleE: '', name: '1'},
    {title: '所属类型', titleE: 'categoryName', name: ''},
    {title: '业务项目', titleE: 'busnameClassName', name: ''},
    {title: '业务大类', titleE: 'busnameTypeName', name: ''},
    {title: '人员类型', titleE: 'personCategoryName', name: ''},
    {title: '业务小类', titleE: 'busnameSubtypeName', name: ''},
    {title: '法定办结时限', titleE: 'statutoryDeadline', name: ''},
    {title: '办件类型', titleE: 'handleTypeText', name: ''},
    {title: '办理形式', titleE: 'handleForm', name: ''},
    {title: '是否需要提前在政府网上窗口进行业务办理预约', titleE: 'makReservationsText', name: ''},
    {title: '业务办理预约网上窗口1', titleE: 'handleWindow1', name: ''},
    {title: '业务办理预约网上窗口2', titleE: 'handleWindow2', name: ''},
    {title: '业务办理预约网上窗口3', titleE: 'handleWindow3', name: ''},
    {title: '是否能全市跨区通办', titleE: 'crossRegionHandleText', name: ''},
    {title: '全市跨区通办区域', titleE: 'crossRegionHandleArea', name: ''},
    {title: '是否能全省跨市通办', titleE: 'crossCityHandleText', name: ''},
    {title: '全省跨市通办区域', titleE: 'crossCityHandleArea', name: ''},
    {title: '是否能跨省通办', titleE: 'crossProvinceHandleText', name: ''},
    {title: '跨省通办区域', titleE: 'crossProvinceHandleArea', name: ''},
    {title: '办理流程', titleE: '',name: '2'},
    {title: '用工状态要求', titleE: 'orderStatusText', name: ''},
    {title: '缴费状态要求', titleE: 'ssStatusText', name: ''},
    {title: '是否收费办理', titleE: 'tollHandleText', name: ''},
    {title: '收费标准', titleE: 'tollStandard', name: ''},
    {title: '政府部门支付待遇的收款方', titleE: 'payee', name: ''},
    {title: '单位办理', titleE: '',name: '3'},
    {title: '单位办理条件', titleE: 'eHandleCondition', name: ''},
    {title: '单位线上办理流程1', titleE: 'eHandleProcess1', name: ''},
    {title: '单位线上办理流程2', titleE: 'eHandleProcess2', name: ''},
    {title: '单位线上办理流程3', titleE: 'eHandleProcess3', name: ''},
    {title: '单位线下办理流程', titleE: 'eHandleOfflineProcess', name: ''},
    {title: '个人办理', titleE: '', name: '4'},
    {title: '个人办理条件', titleE: 'pHandleCondition', name: ''},
    {title: '个人线上办理流程1', titleE: 'pHandleProcess1', name: ''},
    {title: '个人线上办理流程2', titleE: 'pHandleProcess2', name: ''},
    {title: '个人线上办理流程3', titleE: 'pHandleProcess3', name: ''},
    {title: '个人线下办理流程', titleE: 'pHandleOfflineProcess', name: ''},
    {title: '单位办理与个人办理的区别', titleE: 'processDifference', name: ''},
    {title: '经办窗口地址信息', titleE: 'windowAddress', name: ''},
    {title: '其他办理信息', titleE: '', name: '5'},
    {title: '其他办理信息1', titleE: 'otherHandleInfo1', name: ''},
    {title: '其他办理信息2', titleE: 'otherHandleInfo2', name: ''},
    {title: '其他办理信息3', titleE: 'otherHandleInfo3', name: ''},
    {title: '其他办理信息4', titleE: 'otherHandleInfo4', name: ''},
    {title: '其他办理信息5', titleE: 'otherHandleInfo5', name: ''},
    {title: '其他办理信息6', titleE: 'otherHandleInfo6', name: ''},
    {title: '政策依据1', titleE: '', name: '6'},
    {title: '政策文件名称', titleE: 'policyFileName1', name: ''},
    {title: '依据文号', titleE: 'accordingFileNumber1', name: ''},
    {title: '政策来源', titleE: 'policySource1', name: ''},
    {title: '执行日期', titleE: 'effectiveDate1', name: ''},
    {title: '其他政策信息1', titleE: 'otherPolicyInfo1', name: ''},
    {title: '其他政策信息2', titleE: 'otherPolicyInfo2', name: ''},
    {title: '其他政策信息3', titleE: 'otherPolicyInfo3', name: ''},
    {title: '条款内容', titleE: 'termsContent1', name: ''},
    {title: '政策链接', titleE: 'policyUrl1', name: ''},
    {title: '政策依据2', titleE: '', name: '7'},
    {title: '政策文件名称', titleE: 'policyFileName2', name: ''},
    {title: '依据文号', titleE: 'accordingFileNumber2', name: ''},
    {title: '政策来源', titleE: 'policySource2', name: ''},
    {title: '执行日期', titleE: 'effectiveDate2', name: ''},
    {title: '其他政策信息1', titleE: 'otherPolicyInfo3', name: ''},
    {title: '其他政策信息2', titleE: 'otherPolicyInfo5', name: ''},
    {title: '其他政策信息3', titleE: 'otherPolicyInfo6', name: ''},
    {title: '条款内容', titleE: 'termsContent2', name: ''},
    {title: '政策链接', titleE: 'policyUrl2', name: ''},
    {title: '政策依据3', titleE: '', name: '8'},
    {title: '政策文件名称', titleE: 'policyFileName3', name: ''},
    {title: '依据文号', titleE: 'accordingFileNumber3', name: ''},
    {title: '政策来源', titleE: 'policySource3', name: ''},
    {title: '执行日期', titleE: 'effectiveDate3', name: ''},
    {title: '其他政策信息1', titleE: 'otherPolicyInfo7', name: ''},
    {title: '其他政策信息2', titleE: 'otherPolicyInfo8', name: ''},
    {title: '其他政策信息3', titleE: 'otherPolicyInfo9', name: ''},
    {title: '条款内容', titleE: 'termsContent3', name: ''},
    {title: '政策链接', titleE: 'policyUrl3', name: ''},
  ]
  const dataList = list.map((item) => {
    let data:any
    for(let it in resultObj){
      if (!item.titleE) {
        data = {
          ...item
        }
      }
      if (item.titleE === it) {
        data = {
          ...item,
          name: resultObj[it]
        }
      }
    }
    return data
  })
  const renderItem = () => {
    return (
      <View className={styles.content} >
        {
          dataList.filter((i => i && i.name)).map((item:any) => {
            if (!item.titleE) {
              return (<View key={item.title}><Text className={styles.title1}>{item.title}</Text></View>)
            } else {
              return (
                <>
                  <View className={styles.flex} key={item.title}>
                   <Text className={styles.text1}>{item.title}</Text>
                   <Text className={styles.text2}>{item.name || ''}</Text>
                 </View>
                </>
              )
            }
          })
        }
      </View>
    )
  }
  return (
    <View className={styles.wrap}>
      <ScrollView style={scrollStyle} scrollY>
        <View className={styles.header}>
          <Text className={styles.title}>{`${resultObj?.cityName ?? ''}${resultObj?.busnameSubtypeName ?? ''}`}</Text>
          <Image className={styles.img1} src={icon_keyword} />
          <Text className={styles.detail1}>关键词：{`${resultObj?.cityName ?? ''}${resultObj?.busnameSubtypeName ?? ''}`}</Text>
          <Image className={styles.img2} src={icon_time} />
          <Text className={styles.detail2}>更新时间：{resultObj?.updateDt ?? ''}</Text>
        </View>
        {renderItem()}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () => Taro.navigateBack()
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
