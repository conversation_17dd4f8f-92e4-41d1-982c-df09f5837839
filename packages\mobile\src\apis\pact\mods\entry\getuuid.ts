/**
 * @description 根据accountId和cityId来获取能否创建新入职记录的状态，以及uuid
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** accountId */
  accountId?: string;
  /** cityId */
  cityId?: string;
  /** openId */
  openId?: string;
}

export type Result = defs.pact.UuidBean;
export const path = '/yc-wepact-mobile/entry/getuuid';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
