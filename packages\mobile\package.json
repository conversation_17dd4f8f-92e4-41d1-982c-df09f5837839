{"name": "mobile", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": true, "css": "less"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:h5:dev": "cross-env APP_ENV=dev APP_Platform=wow taro build --type h5", "build:h5:test": "cross-env APP_ENV=test taro build --type h5", "build:h5:veri": "cross-env APP_ENV=veri taro build --type h5", "build:h5:prod": "cross-env APP_ENV=prod taro build --type h5", "build:h5:dd:test": "cross-env APP_ENV=test APP_Prefix=dd taro build --type h5", "build:h5:dd:prod": "cross-env APP_ENV=prod APP_Prefix=dd taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "cross-env NODE_ENV=production npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "node scripts/createRoute && npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "resolutions": {"sass": "1.62.0", "@types/react": "17.0.2"}, "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@hookform/error-message": "^2.0.0", "@react-native-community/async-storage": "^1.12.1", "@react-native-community/cameraroll": "^4.0.4", "@react-native-community/segmented-control": "^2.2.2", "@react-native-community/slider": "^3.0.3", "@react-pdf/renderer": "^2.0.21", "@sentry/react": "^6.13.2", "@sentry/tracing": "^6.13.2", "@tarojs/cli": "3.3.7", "@tarojs/components": "3.3.7", "@tarojs/plugin-platform-alipay": "3.3.7", "@tarojs/plugin-platform-swan": "3.3.7", "@tarojs/plugin-platform-weapp": "3.3.7", "@tarojs/react": "3.3.7", "@tarojs/rn-runner": "3.3.7", "@tarojs/runtime": "3.3.7", "@tarojs/taro": "3.3.7", "@tarojs/taro-rn": "3.3.5", "@tarojs/webpack-runner": "3.3.7", "babel-preset-taro": "3.3.7", "chinese-days": "^1.4.0", "dayjs": "^1.10.6", "default-passive-events": "^2.0.0", "image-conversion": "^2.1.1", "lodash": "^4.17.21", "lodash.js": "^0.0.1-security", "nervjs": "^1.5.7", "pont-engine": "^1.3.3", "pulltorefreshjs": "^0.1.22", "react": "^17.0.0", "react-dom": "^17.0.0", "react-hook-form": "^7.19.1", "react-native": "^0.64.0", "react-native-gesture-handler": "^1.10.3", "react-native-keyboard-aware-scroll-view": "^0.9.4", "react-native-linear-gradient": "^2.5.6", "react-native-pager-view": "^5.4.0", "react-native-svg": "^12.1.1", "react-pdf": "^5.5.0", "react-pdf-js": "^5.1.0", "react-redux": "^7.2.4", "react-transition-group": "^4.4.2", "redux": "^4.1.1", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "taro-ui": "^3.0.0-alpha.10", "tarojs-router-next": "^2.3.1", "tlbs-map-react": "^1.0.1", "vconsole": "^3.9.1"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/mini-runner": "3.3.7", "@tarojs/webpack-runner": "3.3.7", "@types/react": "^17.0.2", "@types/react-native": "^0.64.13", "@types/react-pdf": "^5.0.9", "@types/webpack-env": "^1.13.6", "@types/xmldom": "^0.1.31", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "babel-preset-taro": "3.3.7", "cross-env": "^7.0.3", "eslint": "^6.8.0", "eslint-config-taro": "3.3.7", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "stylelint": "9.3.0", "typescript": "^4.1.0"}}