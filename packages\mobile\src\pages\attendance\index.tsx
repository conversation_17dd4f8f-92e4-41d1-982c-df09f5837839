import { useState, useEffect } from 'react'
import Taro from '@tarojs/taro'
import { useModal } from '@components/modal/useModal'
import { View, Text, Image, ScrollView, CheckboxGroup, Checkbox, Picker } from '@tarojs/components'
import { BottomBtn, Modal, StatusBar, withPage } from '@components'
import { getGlobalData } from '@utils/global-data'
import { closeWindow } from '@utils/jssdk'
import { ncmp } from '@apis/ncmp'
import { isEmpty } from 'lodash'
import arrowDown from '@assets/icon/icon-arrow-down.png'
import styles from './index.module.scss'

const Index = () => {
  const [isCheck, setIsCheck] = useState(false)
  const [result, setResult] = useState<defs.ncmp.PrivacyRegisterDTO>({})
  const [flag, setFlag] = useState(true)
  const [selectorValue, setSelectorValue] = useState<any>([])
  const [selectedValue, setSelectedValue] = useState<any>(undefined)
  const account = getGlobalData<'account'>('account')
  const PlatformInfo = sessionStorage.getItem('PlatformInfo')
  const accountInfo = PlatformInfo && JSON.parse(PlatformInfo)
  const modal = useModal()
  const handleChange = () => {
    setIsCheck(!isCheck)
  }
  const clientH = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
  const scrollStyle = {
    maxHeight: `${Math.round(clientH / 1.8)}px`
  }
  useEffect(() => {
    if (!account?.openId || !accountInfo?.openId) return
    ncmp.privacy.findLastestRegister.request({ openId: account?.openId || accountInfo?.openId }).then((res: any) => {
      if (res.code !== '200') {
        Taro.showToast({ title: res.errorMsg, icon: 'none' })
        setTimeout(() => closeWindow(), 500)
        return
      }
      if (res?.resultObj?.version === -1) {
        Taro.showToast({ title: '系统异常,请稍后重试！', icon: 'none' })
        setTimeout(() => closeWindow(), 500)
        return
      }
      if (isEmpty(res.resultObj)) return
      setResult(res.resultObj)
      setFlag(res.resultObj?.registed)
    })
  }, [account?.openId, accountInfo?.openId])
  useEffect(() => {
    const title = '隐私协议'
    const content = (
      <View>
        <ScrollView scrollY style={scrollStyle}>
          <View dangerouslySetInnerHTML={{ __html: result?.content! }}></View>
        </ScrollView>
        <CheckboxGroup onChange={handleChange}>
          <Checkbox value='' checked={isCheck}></Checkbox>
        </CheckboxGroup>
        <Text className={styles.tips}>本人已详细阅读并知晓以上内容</Text>
      </View>
    )
    const onClick = () => {
      ncmp.privacy.postRegister
        .request({
          privacyId: result?.privacyId,
          version: result?.version,
          openId: account?.openId || accountInfo?.openId
        })
        .then(resp => {
          if (resp.code !== '200') {
            Taro.showToast({ title: resp.errorMsg || '系统异常', icon: 'none' })
            modal.setModal({ visible: false })
            return
          }
          modal.setModal({ visible: false })
        })
        .catch(err => {
          Taro.showToast({ title: '系统异常,请稍后重试！', icon: 'none' })
          modal.setModal({ visible: false })
          console.log(err)
        })
    }
    const btns: any = [{ title: '同意', type: isCheck ? 'primary' : '', onClick: isCheck ? onClick : () => {} }]
    setTimeout(() => modal.setModal({ title, content, visible: true, btns }), 0)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCheck, result?.content])
  const handleSelectorChange = e => {
    setSelectedValue(e.detail.value)
  }
  useEffect(() => {
    ncmp.eosAttendance.getSignEleContract.request({}).then((res) => {
      if (res.code === '200'){
        setSelectorValue(res.resultObj)
      }
    })
  }, [])
  return (
    <View className={styles.home}>
      <StatusBar backgroundColor='#fff' />
      <Image src={require('@assets/home/<USER>')} className={styles.banner} />
      <View className={styles.wrap}>
        <View className={styles.text}>您在以下多家公司供职，</View>
        <View className={styles.text}>选择需要打卡考勤的公司:</View>
        <View>
          <Picker
            mode='selector'
            range={selectorValue}
            rangeKey='custName'
            onChange={handleSelectorChange}
            onCancel={() => {
              console.log('onCancel')
            }}
          >
            <View className={styles.picker}>
              <Text className={styles.text}>{selectorValue[selectedValue]?.custName || '请选择公司'}</Text>
              <Image className={styles.arrow} src={arrowDown} />
            </View>
          </Picker>
        </View>
      </View>
      <BottomBtn
        btns={[
          {
            title: '进入该公司',
            onClick: () => {
              const {custId, custName, empName = ''} = selectorValue[selectedValue] || {};
              if (!custId){
                Taro.showToast({ title: '请选择需要打卡考勤的公司!', icon: 'none' })
                return;
              }
              Taro.navigateTo({ url: `/pages/attendance/home/<USER>
            }
          }
        ]}
      />
      {!flag && <Modal {...modal.modal} />}
    </View>
  )
}

export default withPage(Index)
