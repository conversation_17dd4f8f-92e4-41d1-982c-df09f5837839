/**
 * @description 具体业务中的反馈信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** businessId */
  businessId?: string;
  /** empId */
  empId?: string;
  /** openId */
  openId?: string;
}

export type Result = defs.pact.FeedbackBean;
export const path = '/yc-wepact-mobile/busi/getFeedbackInfo';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
