/*  #ifdef  h5  */
@mixin animate($animation, $duration, $method, $times) {
  animation: $animation $duration $method $times;
}

@mixin spinKeyframes($name) {
  @-webkit-keyframes :global(#{$name}) {
    @content;
  }

  @-moz-keyframes :global(#{$name}) {
    @content;
  }

  @-ms-keyframes :global(#{$name}) {
    @content;
  }

  @keyframes :global(#{$name}) {
    @content;
  }
}
/*  #endif  */

.footer {
  margin: 30px;
  padding-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  height: 100px;
  width: 690px;
}
.footer_text {
  font-size: 24px;
  color: #666666;
}
/*  #ifdef  h5  */
:global {
  @include spinKeyframes(spin) {
    0% {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(180deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  .ptr--ptr {
    box-shadow: none !important;
    svg {
      @include animate(spin, 2s, linear, infinite);
      width: 30px;
    }
    &.ptr--refresh {
      .ptr--icon {
        display: none;
      }
      .ptr--text {
        //             width: 25px;
        // margin: auto;
      }
    }
  }
}
/*  #endif  */

.empty {
  height: 100%;
  flex-direction: column;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -20%;
}
.no_data {
  width: 160px;
  height: 103px;
  margin-bottom: 40px;
}

.no_text {
  color: #d9d9d9;
  font-size: 28px;
}
