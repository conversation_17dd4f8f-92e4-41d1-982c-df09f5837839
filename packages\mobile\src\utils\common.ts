import Taro from '@tarojs/taro'

export const getStorageData = async (key: string) => {
  let result: any
  try {
    const { data } = await Taro.getStorage({ key })
    result = data
  } catch (error) {
    console.log(error)
  }
  return result
}

// num为传入的值，n为保留的小数位
export const fomatFloat = (num: number | string, n: number) => {
  let f = parseFloat(num as string)
  if (Number.isNaN(f)) {
    return false
  }
  f = Math.round((num as number) * Math.pow(10, n)) / Math.pow(10, n) // n 幂
  return f
}

export function getUrlParam(name?: string) {
  let url = window.location.href
  let obj = {}
  let reg = new RegExp('[?&][^?&]+=[^?&]+','g')
  let arr = url.match(reg)
  if (arr) {
    arr.forEach(item => {
      let tempArr = item.substring(1).split('=')
      let key = decodeURIComponent(tempArr[0])
      let val = decodeURIComponent(tempArr[1])
      obj[key] = val
    })
  }
  return name ? obj[name] : obj
}

export function delUrlCode() {
  const obj = getUrlParam()
  delete obj.code
  delete obj.state
  let url = ''
  let isF = true
  for (const key in obj) {
    const value = obj[key]
    url += `${isF ? '?' : '&'}${key}=${value}`
  }
  window.location.href = window.location.pathname + url
  // window.history.replaceState({},'',window.location.pathname + url)
}

export const getObjFromkeys = (obj: any, keys: string[]) => {
  return keys.reduce((total, item) => ({ ...total, [item]: obj?.[item] }), {})
}

export const getGuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export const idCardReg = /^[0-9a-zA-Z]+$/

export const mobileReg = /^(1)\d{10}$/

export const emailReg = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/

// 身份号证脱敏 前6位后4位
export const encryCertificateNumber = (str: string = '') => {
  return str.replace(/(\w{6})\w*(\w{4})/, '$1********$2')
}

// 手机号脱敏
export const encryPhoneNumber = (str: string = '') => {
  const start = str.substring(0,3)
  const end = str.substring(str.length - 4,11)
  return start + '****' + end
}

// 其他证件号 *替代中间的三分之一长度内容（向上取整，例如长度11位，从第四位开始隐藏4位，最后显示3位）
export const encryIdNumber = (str: string = '') => {
  const count = Math.ceil(str.length / 3)
  const left = Math.ceil((str.length - count) / 2)
  const rirht = str.length - count - left
  const start = str.substring(0,left)
  const end = str.substring(str.length - rirht,rirht)
  return start + '*'.repeat(count) + end
}

/**
 * 日期格式化
 * @param date
 * @param format
 * @returns
 */
export function formatDate(date: Date, format: string): string {
  const map: { [key: string]: string } = {
    MM: date.getMonth() + 1 + "",
    dd: date.getDate() + "",
    yyyy: date.getFullYear() + "",
    HH: date.getHours() + "",
    mm: date.getMinutes() + "",
    ss: date.getSeconds() + "",
  };

  return format.replace(/MM|dd|yyyy|HH|mm|ss/g, (match) => {
    return map[match].padStart(2, "0");
  });
}