/**
 * @description 社保公积金列表查询接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.FundData;
export const path = '/yc-wepact-mobile/busi/getSocialnsuranceAndfundList';
export const method = 'POST';
export const request = (
  data: defs.pact.FundParam,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.FundParam,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
