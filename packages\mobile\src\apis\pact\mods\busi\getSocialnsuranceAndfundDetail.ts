/**
 * @description 社保公积金查询详情接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.FundDetailBean;
export const path = '/yc-wepact-mobile/busi/getSocialnsuranceAndfundDetail';
export const method = 'POST';
export const request = (
  data: defs.pact.FundDetailParam,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.FundDetailParam,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
