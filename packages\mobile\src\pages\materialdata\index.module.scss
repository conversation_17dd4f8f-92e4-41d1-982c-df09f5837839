.wrap {
  width: 100%;
  background-color: #ffffff;
}

.sample {
  background-color: #b51e25;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 180px;
  height: 60px;
  border-radius: 6px;
  margin-top: 40px;
  margin-left: 540px;
}
.sample_img {
  width: 32px;
  height: 20px;
  margin-right: 10px;
}
.sample_text {
  color: #ffffff;
  font-size: 24px;
}

.item_wrap {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 40px;
  padding: 15px;
}

.item {
  width: 210px;
  // height: 240px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #efeff4;
  margin: 15px;
  padding-bottom: 20px;
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
  // height: 40px;
}

.required {
  width: 12px;
  height: 12px;
  margin-right: 10px;
}

.title {
  color: #333;
  font-size: 24px;
}

.img_wrap {
  width: 160px;
  height: 160px;
  border: 1px dashed #efeff4;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.img {
  position: absolute;
  width: 150px;
  height: 150px;
}

.del_cls {
  display: flex;
  justify-content: center;
  align-items: center;
}
.delImg {
  width: 40px;
  height: 40px;
  margin-bottom: 115px;
  margin-left: 115px;
}
.add {
  width: 44px;
  height: 44px;
}

.require_cls {
  position: absolute;
  top: 0px;
}
.require_tips {
  color: #b51e25;
  font-size: 24px;
  position: absolute;
  width: 160px;
  top: 177px;
  left: -73px;
}
