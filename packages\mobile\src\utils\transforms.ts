/**
 *  样式转行处理
 *  单位：px 转小程序(rpx) h5(rem) RN(DP)
 *  TODO: 1px问题
 *  TODO: 单位转化
 */

import Taro from '@tarojs/taro'
import { useEffect, useState } from 'react'
import { getGlobalData } from './global-data'
import { isAndriod } from './across-api'

// 获取屏幕宽度
export const scalePx2dp = async (px: number) => {
  const systemInfo = await Taro.getSystemInfo()
  return (px / 750) * systemInfo.screenWidth
}

/**
 * @returns 小程序导航栏高度 单位px
 */
export const getWeappNavBarHeight = () => {
  const { statusBarHeight } = Taro.getSystemInfoSync()
  const { top, height } = Taro.getMenuButtonBoundingClientRect()
  const navBarHeight = height + (top - statusBarHeight) * 2
  return navBarHeight + statusBarHeight
}

/**
 * 获取可滚动区域大小
 * @param options top/bottom 额外的大小 单位px
 * @returns
 *  hasNav 小程序端默认为true H5 默认为false
 *  迁移成Hook函数 解决android 键盘收起 界面bug
 */
export const getScrollStyle = (options?: { top?: number; bottom?: number; hasNav?: boolean }) => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [count, setCount] = useState(0)
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (IS_H5 && isAndriod) {
      var originalHeight = document.documentElement.clientHeight || document.body.clientHeight
      window.onresize = function() {
        //键盘弹起与隐藏都会引起窗口的高度发生变化
        var resizeHeight = document.documentElement.clientHeight || document.body.clientHeight
        if (resizeHeight - 0 < originalHeight - 0) {
          //当软键盘弹起，在此处操作
          // console.log('键盘弹起---');
          setCount(count + 1)
        } else {
          //当软键盘收起，在此处操作
          // console.log('键盘收起---');
          setCount(count + 1)
        }
      }
    }
  }, [count])

  const { top = 0, bottom = 0, hasNav = true } = options || {}
  const systemInfo = getGlobalData<'systemInfo'>('systemInfo')
  // 有height的情况下rn端会自动计算高度 若无会自适应
  // style rn端可以是Number h5/weapp端要是带单位的字符串
  const _bottom = Taro.pxTransform(bottom)
  const _top = Taro.pxTransform(top)
  if (IS_RN) {
    const topDp = (systemInfo.windowWidth * top) / 750
    const bottomDp = (systemInfo.windowWidth * bottom) / 750
    const safeAreaBottom = systemInfo.screenHeight - systemInfo.safeArea.bottom
    const navBarHeight = hasNav ? systemInfo.statusBarHeight + 44 : 0
    return {
      height: systemInfo?.screenHeight - topDp - bottomDp - navBarHeight - safeAreaBottom,
      marginTop: _top,
      marginBottom: _bottom + safeAreaBottom,
      overflow: 'scroll'
    }
  } else if (IS_WEAPP) {
    const navBarHeight = hasNav ? getWeappNavBarHeight() : 0
    const safeAreaBottom = systemInfo.screenHeight - systemInfo.safeArea.bottom
    const topPX = (systemInfo.windowWidth * top) / 750
    const bottomPX = (systemInfo.windowWidth * bottom) / 750
    return {
      height: systemInfo?.screenHeight - navBarHeight - topPX - bottomPX - safeAreaBottom,
      marginTop: topPX,
      marginBottom: bottomPX + safeAreaBottom
    }
  } else {
    const topPX = (systemInfo.windowWidth * top) / 750
    const bottomPX = (systemInfo.windowWidth * bottom) / 750
    return {
      height: `${systemInfo?.windowHeight - topPX - bottomPX}px`,
      marginTop: _top,
      marginBottom: _bottom
      // overflow: 'scroll'
    }
  }
}

/**
 *  单位转化
 */
export const getNumberSize = (size?: string | number) => {
  if (!size) return 0
  if (typeof size === 'number') return size
  if (size.includes('px')) return Number(size.substr(0, size.length - 2))
  if (size.includes('rpx')) return Number(size.substr(0, size.length - 3))
  console.log('未知单位----', size)
  return 0
}
