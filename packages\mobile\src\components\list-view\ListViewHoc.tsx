/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-16 13:27:00
 * @LastAuthor: 王正荣
 * @LastTime: 2021-11-18 17:42:08
 * @message: 
 */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from 'react'
import { getNumberSize } from '@utils/transforms'
import { View, Image, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import no_data from '@assets/no-data.png'
import ListView from './index'
import { ListViewProps } from './type'
import styles from './index.module.scss'

type Empty = {
  Empty?: React.ReactNode
}

const ListViewHoc = (props: ListViewProps & Empty) => {
  const { refreshEventName, onRefresh } = props
  useEffect(() => {
    if (refreshEventName) {
      const handle = () => {
        onRefresh?.()
      }
      Taro.eventCenter.on(refreshEventName, handle)
      return () => {
        Taro.eventCenter.off(refreshEventName, handle)
      }
    }
  }, [])
  if (props.itemData?.length) {
    return <ListView {...props} />
  }
  const height = props?.height || getNumberSize((props?.style as any)?.height)
  return (
    <ListView
      {...props}
      itemSize={height}
      //占位
      itemData={[null]}
      renderItem={() =>
        props.Empty ? (
          props.Empty
        ) : (
          <View className={styles.empty} style={{ height: (props?.style as any)?.height }}>
            <Image className={styles.no_data} src={no_data} />
            <Text className={styles.no_text}>暂无数据</Text>
          </View>
        )
      }
    />
  )
}

export default ListViewHoc
