import { Picker, View, Text, Image } from '@tarojs/components'
import { useState, useEffect } from 'react'
import { getWorkdaysInRange } from 'chinese-days'
import icon_arrow from '@assets/icon/icon-arrow.png'
import dayjs from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'

import styles from './index.module.scss'

interface WorkdayPickerProps {
  value?: string
  onChange?: (date: string) => void
  title?: string
}
dayjs.extend(isSameOrAfter)
const WorkdayPicker: React.FC<WorkdayPickerProps> = ({ value, onChange }) => {
  const [selectedDate, setSelectedDate] = useState(value || '')
  const [workdayMap, setWorkdayMap] = useState<Record<string, string[]>>({})
  const [range, setRange] = useState<string[][]>([[], [], []])
  const [selectedIndexes, setSelectedIndexes] = useState<number[]>([0, 0, 0])
  useEffect(() => {
    const fetchWorkdays = async () => {
      const currentYear = dayjs().year()
      const currentMonth = dayjs().format('MM')
      const today = dayjs().startOf('day') // 获取今天的开始时间
      const startDate = today.format('YYYY-MM-DD') // 从当前日期开始（包含当天）
      const endDate = `${currentYear}-12-31`

      const days = await getWorkdaysInRange(startDate, endDate, true)

      // 按月份整理工作日，包含当前日期及之后的日期
      const workdays: Record<string, string[]> = {}
      days.forEach(day => {
        if (
          dayjs(day)
            .startOf('day')
            .isSameOrAfter(today)
        ) {
          const month = day.substring(0, 7)
          if (!workdays[month]) {
            workdays[month] = []
          }
          workdays[month].push(day)
        }
      })
      setWorkdayMap(workdays)

      // 设置年月选项 - 从当前月份开始
      const years = [currentYear.toString()]
      const months = Object.keys(workdays).map(key => key.split('-')[1]) // 获取所有有工作日的月份

      // 设置当前月份的日期选项，只显示当前日期之后的日期
      const currentMonthKey = `${currentYear}-${currentMonth}`
      const currentMonthDays = workdays[currentMonthKey]?.map(d => d.split('-')[2]) || []

      setRange([years, months, currentMonthDays])

      // 设置默认选中值为当前日期
      setSelectedIndexes([0, 0, 0])
    }

    fetchWorkdays()
  }, [value, onChange])

  const handleColumnChange = (e: any) => {
    const { column, value: index } = e.detail
    const newIndexes = [...selectedIndexes]
    newIndexes[column] = index

    if (column === 1) {
      // 月份改变时更新日期选项
      const year = range[0][newIndexes[0]]
      const month = range[1][index]
      const monthKey = `${year}-${month}`
      const days = workdayMap[monthKey]?.map(d => d.split('-')[2]) || []

      const newRange = [...range]
      newRange[2] = days
      setRange(newRange)

      // 重置日期索引
      newIndexes[2] = 0
    }

    setSelectedIndexes(newIndexes)
  }

  // 格式化显示日期
  const formatDate = (dateStr: string) => {
    if (!dateStr) return ''
    const date = dayjs(dateStr)
    return `${date.year()}年${date.month() + 1}月${date.date()}日`
  }

  return (
    <View className='workday-picker'>
      <Picker
        mode='multiSelector'
        range={range}
        value={selectedIndexes}
        onChange={e => {
          const indexes = e.detail.value
          const date = `${range[0][indexes[0]]}-${range[1][indexes[1]]}-${range[2][indexes[2]]}`
          setSelectedDate(date)
          onChange?.(date)
        }}
        onColumnChange={handleColumnChange}
      >
        <View className={styles.picker}>
          <Text className={styles.item_text}> {selectedDate ? formatDate(selectedDate) : '请选择'}</Text>
          <Image className={styles.item_arrow} src={icon_arrow} />
        </View>
      </Picker>
    </View>
  )
}

export default WorkdayPicker
