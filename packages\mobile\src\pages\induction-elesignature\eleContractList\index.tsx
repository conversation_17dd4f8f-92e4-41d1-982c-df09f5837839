import { useEffect, useState } from 'react'
import Taro from '@tarojs/taro'
import { View, Text, Button, Image } from '@tarojs/components'
import { getGlobalData } from '@utils'
import { withPage } from '@components'
import { ncmp } from '@apis/ncmp'
import no_data from '@assets/no-data.png'
import styles from './index.module.scss'

const typeMap = {
  '1': '未发起',
  '2': '拟定中',
  '3': '签署中',
  '4': '作废中',
  '5': '已完成',
  '6': '已过期',
  '7': '已撤回',
  '9': '已作废',
  '10': '已发起作废'
}
const Index = () => {
  const [list, setlist] = useState<defs.ncmp.EleContract[] | unknown[]>([])
  const PlatformInfo = sessionStorage.getItem('PlatformInfo')
  const accountInfo = PlatformInfo && JSON.parse(PlatformInfo)
  let { empId } = getGlobalData<'account'>('account')
  if(!empId) {
    empId = accountInfo?.empId
  }
  useEffect(() => {
    if (!empId) return
    ncmp.elecSign.getEleContractList
      .request({
        empId
      },{isToken: true})
      .then((res:any) => {
        if (res.code === '200') {
          Array.isArray(res?.resultObj) && setlist(res?.resultObj || [])
        } else {
          Taro.showModal({
            title: '提示',
            content: res.errorMsg || '系统异常!',
            showCancel: false
          })
        }
      }).catch(() => {
        const title = '系统异常！'
        Taro.showToast({ title, icon: 'none' })
      })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const toEelSignaturePage = (params = {}) => {
    ncmp.elecSign.getEleContractUrl.request(params, {isToken: true}).then(res => {
      if (res.code === '200') {
        const eleSinUrl = res?.resultObj?.eleSinUrl || ''
        window.location.href = eleSinUrl
      }
    })
  }
  return (
    <View className={styles.wrap}>
      {list.length > 0 ? (
        list.map((item: defs.ncmp.EleContract & { eleContractStatus: string }) => (
          <View
            className={styles.item}
            key={item.eleContractName}
            onClick={() => {
              toEelSignaturePage(item)
            }}
          >
            <Text className={styles.eleContractName}>{item.eleContractName}</Text>
            <Button className={styles.eleContractStatus}>{typeMap[item.eleContractStatus]}</Button>
          </View>
        ))
      ) : (
        <View className={styles.empty}>
          <Image className={styles.no_data} src={no_data} />
          <Text className={styles.no_text}>暂无可以查看的电子合同</Text>
        </View>
      )}
    </View>
  )
}

export default withPage(Index)
