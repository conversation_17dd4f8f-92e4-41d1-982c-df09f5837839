.containers {
  padding: 30px;
  height: calc(100vh - 100px);
  box-sizing: border-box;

  .wrapper {
    height: 100%;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 10px 0px rgba(204, 204, 204, 1);
    border-radius: 2px;
  }

  .box_wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: absolute;

    left: 50%;
    width: 504px;
    height: 565px;
    transform: translateX(-50%);
    background: #ffffff;

    border-radius: 8px;
    z-index: 20000;
  }

  .box_wrap {
    position: absolute;
    padding-top: 209px;
  }

  .box_wrap .success {
    width: 186px;
    height: 186px;
  }

  .box_wrap .error {
    width: 186px;
    height: 186px;
  }

  .times {
    margin-top: 55px;
    width: 100%;
    height: 89px;
    font: 100px/89px "arial";
    text-align: center;
    font-weight: bold;
    color: rgba(26, 173, 25, 1);
  }

  .suc {
    margin-top: 22px;
    height: 56px;
    font: 40px/56px "PingFangSC-Medium";

    font-weight: 500;
    color: rgba(26, 173, 25, 1);
  }
}
