import Taro, { useRouter } from '@tarojs/taro'
import { View, Text } from '@tarojs/components'
import { BottomBtn, withPage } from '@components'
import styles from './index.module.scss'
import { useEffect, useState } from 'react'
import { ncmp } from '@apis/ncmp'
import { stateObj } from '..'

const Index = () => {

  const { applyId, custId } = useRouter().params
  const [holDetail, setHolDetail] = useState<any>({});

  useEffect(() => {
    ncmp.acHolApply.getDetail.request({ applyId: Number(applyId), custId: Number(custId) }, { isToken: true }).then(res => {
      if (res.code === '200') {
        setHolDetail(res.resultObj)
      } else {
        Taro.showToast({ title: res.errorMsg || '系统异常, 请稍后重试', icon: 'none', });
      }
    })
  }, []);

  const { empName, holName, serialNum, eosEmpId, startTime, endTime, viewHours, applyTime, remark, state } = holDetail?.acHolApply || {};

  return (
    <View className={styles.container}>
      <View>
        <View className={styles.title}>{empName}{holName}{' '}</View>
        <View className={styles.detail}>
          <View className={styles.item}>流水号：{serialNum}</View>
          <View className={styles.item}>姓名：{empName}</View>
          <View className={styles.item}>工号：{eosEmpId}</View>
          <View className={styles.item}>开始日期：{startTime}</View>
          <View className={styles.item}>结束日期：{endTime}</View>
          <View className={styles.item}>请假时长：{viewHours}</View>
          <View className={styles.item}>申请时间：{applyTime}</View>
          <View className={styles.item}>请假说明: {remark}</View>
        </View>
        <View className={styles.applyer}>
          <View className={styles.title}>审批人信息</View>
          <View className={styles.item}>当前审批人：<Text style={{ color: '#6190E8' }}>{holDetail?.acApproveStep?.examEmpName}</Text></View>
          <View className={styles.item}>处理状态：<Text style={{ color: stateObj[state]?.color }}>{stateObj[state]?.name}</Text></View>
        </View>
      </View>
      <BottomBtn
        btns={[
          {
            title: '撤销申请',
            hide: state !== 0,
            onClick: () => {
              Taro.showModal({
                title: '提示',
                content: '是否确认撤销',
                success: function (res) {
                  if (res.confirm) {
                    ncmp.acHolApply.cancel.request({ applyId: Number(applyId), custId: Number(custId) }, { isToken: true }).then(res => {
                      if (Number(res.code) === 200) {
                        Taro.showToast({ title: '撤销成功', icon: 'none', duration: 2000 });
                        setTimeout(() => {
                          Taro.navigateTo({ url: "/attendance/leave?type=1" })
                        }, 2000);
                      } else {
                        Taro.showToast({ title: res.errorMsg || '系统异常, 请稍后重试', icon: 'none', duration: 3000 })
                      }
                    })
                  } else if (res.cancel) {
                    // console.log('用户点击取消')
                  }
                }
              })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
