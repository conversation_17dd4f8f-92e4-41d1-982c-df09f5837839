/**
 * @description 获取城市的动态文件条目项
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** cityCode */
  cityCode: string;
  /** openId */
  openId: string;
  /** uuid */
  uuid?: string;
}

export type Result = object;
export const path = '/yc-wepact-mobile/dynamics-form/file/{cityCode}/{openId}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
