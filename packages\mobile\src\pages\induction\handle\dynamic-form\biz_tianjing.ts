import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleTianJingColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
  const hasEmpolymentCert = form.watch('hasEmpolymentCert')
  const isEnterAccumulationFund = form.watch('isEnterAccumulationFund')

  if (page === '2') {
    let flag = !hasEmpolymentCert || hasEmpolymentCert === '1'
    return { ...column, isHidden: flag }
  }
  if (column.name === 'tianjinUnemploymentCardNo') {
    // let flag = !hasEmpolymentCert || hasEmpolymentCert === '0'
    return { ...column, isHidden: hasEmpolymentCert !== '1' }
  }
  if (column.name === 'pfNo12') {
    // let flag = !isEnterAccumulationFund || isEnterAccumulationFund === '0'
    return { ...column, isHidden: isEnterAccumulationFund !== '1' }
  }
  return column
}

export { handleTianJingColumn }
