/**
 * @description 获取手机验证码
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.auth.GlobalResult<defs.auth.BindVerifyCodeResp>;
export const path = '/enterprise-auth/web/getMobileCode';
export const method = 'POST';
export const request = (
  data: defs.auth.MobileVerifyParams,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.auth.MobileVerifyParams,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
