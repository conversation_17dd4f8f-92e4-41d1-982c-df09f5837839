/**
 * @description 取消绑定/绑定
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.JsonMessage;
export const path = '/yc-wepact-mobile/appbind/updateStatus';
export const method = 'POST';
export const request = (
  data: defs.pact.AppBindInfo,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.AppBindInfo,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
