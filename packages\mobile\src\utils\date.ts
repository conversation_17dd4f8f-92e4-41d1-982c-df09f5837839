function genArr(start, end) {
  return Array.from(new Array(end + 1).keys()).slice(start).map(i => i < 10 ? '0' + i : i)

}

const curY = new Date().getFullYear();
const curY10 = genArr(curY - 10, curY + 10);
const M = genArr(1, 12);
const D = genArr(1, 31);
const H = genArr(0, 23);
const S = genArr(0, 59);

const dataTimeArr = [[...curY10], [...M], [...D], [...H], [...S], [...S]]


export {
  curY10,
  M,
  D,
  H,
  S,
  dataTimeArr
}