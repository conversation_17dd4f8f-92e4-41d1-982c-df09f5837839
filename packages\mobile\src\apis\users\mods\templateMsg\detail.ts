/**
 * @description detail
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** uuid */
  uuid: string;
}

export type Result = defs.users.ResponseEntity;
export const path = '/user-server/api/template-msg/detail/{uuid}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
