/**
 * @description form形式上传预约业务办理附件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.upload.HttpResult<defs.upload.UploadResult>;
export const path = '/wx-upload/appointment/uploadAppointmentFileByForm';
export const method = 'POST';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
