import { PickerDateProps } from '@tarojs/components/types/Picker';
import { StandardProps } from '@tarojs/components'
import { InputProps } from '@tarojs/components/types/Input'
import {
  ControllerProps,
  ControllerRenderProps,
  ControllerFieldState,
  UseFormStateReturn,
  UseFormReturn
} from 'react-hook-form'

type FormItemType =
  | 'text'
  | 'email'
  | 'id_card'
  | 'mobile'
  | 'textarea'
  | 'single'
  | 'select'
  | 'multiSelector' // 联动 数据结构为树形结构
  | 'date'
  | 'time'
  | 'region'
  | 'post_code'
  | 'province_city'
  | 'region_city'
  | 'date_selector'
  | 'page_choice' // 跳转页面选择


interface CommonItemProps extends Omit<StandardProps, 'ref'>, ControllerRenderProps, Omit<ControllerProps, 'render'> {
  fieldState?: ControllerFieldState
  formState?: UseFormStateReturn<any>
  type: FormItemType
  title?: string;
  // type存在 自定义不就
  isCustom?:boolean;
  renderTitleRight?: () => React.ReactElement
  remind?: string
  titleRemind ?: string
  placeholder?: string
  level?: 1 | 2 | 3
  options?: string[] | number[] | Record<string, any>[]
  rangeKeys?: string[] // 显示/提交的key default:key,value
  disabled?:boolean;
  // pick 其他属性
  start?:string;
  end?:string;
  fields?: keyof PickerDateProps.Fields;
  pageOptions?: {
    keys?: string[] // 需要录入表单的字段
    labelKey?: string // 显示字段,keys中获取
    url?: string // 页面Url default: /page-choice
    title?: string // 页面标题
    scen?: string // 使用场景 用来区分业务
    eventName?: string // 数据通信事件名称，默认表单采用name
    message?: any // 页面其他参数
  }
}


// type FormItemProps = any

interface FormItemProps extends Partial<CommonItemProps> {
  title?: string
  required?: boolean
  render?: (field: ControllerRenderProps<any, any>) => React.ReactElement
  // 是否可用
  disabled?: boolean
  // 是否显示分割线
  showLine?: boolean
  // 是否隐藏
  isHidden?: boolean
  // 错误信息样式
  errorClassName?: string
  inputProps?: InputProps
  // 自定义错误提示(error.type==valid时使用)
  customMsg?: string
}

interface FormProps<TFieldValues = any> extends StandardProps {
  form?: UseFormReturn<TFieldValues>
  columns?: FormItemProps[]
}

export { CommonItemProps, FormItemProps, FormProps }
