import { ScrollView, View, Text, Image } from '@tarojs/components'
import { Fragment, useEffect, useState, FunctionComponent } from 'react'
import { BottomBtn,FormLabel } from '@components'
import { getGlobalData, getScrollStyle } from '@utils'
import { pact } from '@apis/pact'
import Taro from '@tarojs/taro'
import styles from './index.module.scss'
import { PageParams } from './type'

interface AppointmentMaterialPage extends PageParams {}
const AppointmentMaterial: FunctionComponent<AppointmentMaterialPage> = props => {
  // const { eventName, keys } = props;
  const { openId, accountId } = getGlobalData<'account'>('account')
  useEffect(() => {
    pact.appoint.getMaterialList
      .request({
        subTypeId: '461',
        openId: openId
      })
      .then(res => {})
    // pact.appoint.fetchAppointmentFile
    // .request({
    //   subTypeId: '461',
    //   openId: openId
    // })
    // .then(res => {

    // })
  }, [])
  const scrollStyle = getScrollStyle({ bottom: 120 })
  return (
    <Fragment>
      <ScrollView style={scrollStyle} scrollY>
        <FormLabel level={2} title='办理业务' />
        <View>
          <View>
            <Text className={styles.a}>{`业务项目：社保项目`}</Text>
            <Text className={styles.a}>{`事务类别：社保信息变更`}</Text>
            <Text className={styles.a}>{`业务项目：变更身份证号`}</Text>
          </View>
        </View>
        <FormLabel level={2} title='所属资料' />
        <FormLabel level={2} title='资料上次' />
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回',
            onClick: () => Taro.navigateBack()
          }
        ]}
      />
    </Fragment>
  )
}

export default AppointmentMaterial
