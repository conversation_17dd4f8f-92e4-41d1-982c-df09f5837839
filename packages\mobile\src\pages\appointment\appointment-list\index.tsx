/*
 * @Author: your name
 * @Date: 2021-09-09 10:49:38
 * @LastTime: 2021-12-02 14:32:54
 * @LastAuthor: 王正荣
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\appointment\appointment-list\index.tsx
 */
import { Fragment, useState } from 'react'
import { View, Text } from '@tarojs/components'
import { withPage, BottomBtn, DateSelect, ListView, usePagination, FormTip, Buttons } from '@components'
import Taro from '@tarojs/taro'
import { pact } from '@apis/pact'
import { getScrollStyle } from '@utils/transforms'
import { getGlobalData } from '@utils'
import dayjs from 'dayjs'
import styles from './index.module.scss'

const itemConfig = [
  { title: '所属类型', key: 'categoryName' },
  { title: '业务大类', key: 'busTypeName' },
  { title: '业务小类', key: 'busSubTypeName' },
  { title: '提交预约时间', key: 'createDt' },
  { title: '预约办理时间', key: 'bookingDt' },
  { title: '预约状态', key: 'bookingStatus' }
]

const bookingStatusMap = new Map<string, string>([
  ['0', '预约中'],
  ['1', '预约成功'],
  ['2', '预约失败'],
  ['3', '过期'],
  ['4', '办理完成']
])

const Index = () => {
  const scrollStyle = getScrollStyle({ bottom: 120, top: 256 + 60 })
  const { openId, empId } = getGlobalData<'account'>('account')
  const startDate = dayjs()
    .subtract(3, 'months')
    .add(1, 'days')
    .format('YYYY-MM-DD')
  const endDate = dayjs().format('YYYY-MM-DD')
  const [createDt, setCreateDt] = useState(`${startDate},${endDate}`)
  const list = usePagination(
    async page => {
      const result = await pact.appoint.getAppointmentList.request({
        pageNo: String(page),
        createDt,
        empId,
        openId
      })
      return result.data
    },
    { deps: [createDt] }
  )
  const detail = (bookingId: string) => {
    Taro.navigateTo({ url: `/pages/appointment/appointment-detail/index?bookingId=${bookingId}` })
  }
  const cancel = (bookingId: string) => {
    Taro.navigateTo({ url: `/pages/appointment/appointment-cancel/index?bookingId=${bookingId}&source=1` })
  }
  return (
    <Fragment>
      <DateSelect
        onSelectHandle={e => {
          setCreateDt(`${e.startDate},${e.endDate}`)
        }}
        pickerProps={[{ fields: 'day' }, { fields: 'day' }]}
      />
      <View className={styles.tip}>
        <FormTip tip='提示：查询时间为提交时间' />
      </View>
      <ListView
        refreshEventName='Appointment'
        style={scrollStyle}
        itemSize={502}
        unlimitedSize
        renderItem={(item,_,id) => (
          <View className={styles.item} id={id}>
            {itemConfig.map(config => (
              <View className={styles.text_wrap} key={config.key}>
                <Text className={styles.title}>{config.title}</Text>
                <Text className={styles.detail}>
                  {config.key !== 'bookingStatus' ? item[config.key] : bookingStatusMap.get(item[config.key])}
                </Text>
              </View>
            ))}
            <View className={styles.btn_wrap}>
              {item.bookingStatus === '0' && (
                <Buttons title='取消预约' icon='cancel' onClick={() => cancel(item.bookingId)} />
              )}
              <Buttons title='查看明细' icon='detail' onClick={() => detail(item.bookingId)} />
            </View>
          </View>
        )}
        {...list}
      />
      <BottomBtn
        btns={[{ title: '我要预约', onClick: () => Taro.navigateTo({ url: '/pages/appointment/add/index' }) }]}
      />
    </Fragment>
  )
}

export default withPage(Index)
