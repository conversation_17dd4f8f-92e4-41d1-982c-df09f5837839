/**
 * @description 保存员工业务办理申请
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult;
export const path = '/wx-ncmp/ebmbusiness/saveApplication';
export const method = 'POST';
export const request = (
  data: defs.ncmp.EbmBusinessApplicationDTO,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.EbmBusinessApplicationDTO,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
