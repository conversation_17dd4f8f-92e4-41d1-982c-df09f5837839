/*  #ifndef rn */
@import '~taro-ui/dist/style/index.scss';
/*  #endif  */

/*  #ifdef  h5  */
* {
  box-sizing: border-box;
}
taro-view-core,
taro-text-core,
div {
  font-size: 32px;
  font-family: Avenir, Helvetica, Arial, sans-serif;
}
/*  #endif  */

/*  #ifdef  weapp  */
view,
text {
  box-sizing: border-box;
  font-family: Avenir, Helvetica, Arial, sans-serif;
}
/*  #endif  */

// -----  常用方法与变量
@mixin border($dir, $width, $style, $color) {
  border: 0 $style $color;
  @each $d in $dir {
    #{border-#{$d}-width}: $width;
  }
}


// ---- 公共样式
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}

// 全屏
.full-page {
  width: 750px;
  position: relative;
  flex: 1;
  /*  #ifndef rn */
  height: 100vh;
  /*  #endif  */
}
