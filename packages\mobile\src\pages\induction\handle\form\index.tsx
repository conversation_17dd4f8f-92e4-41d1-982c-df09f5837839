import { Fragment, useEffect, useState } from 'react'
import { View, Text, Image } from '@tarojs/components'
import { Form, BottomBtn, useForm, withPage, FormTip, FormLabel } from '@components'
import { getGlobalData, encryPhoneNumber, encryCertificateNumber, useRequest } from '@utils'
import { isEmpty } from 'lodash'
import { useRouter } from '@tarojs/taro'
import { getScrollStyle } from '@utils/transforms'
import { pact } from '@apis/pact'
import { getColumns, FormItemType } from './biz'
import styles from './index.module.scss'

const Index = () => {
  const { cityId, uuid } = useRouter().params
  const [items, setItems] = useState<FormItemType[]>([])
  const [otherItems, setOtherItems] = useState<FormItemType[]>([])
  const scrollStyle = getScrollStyle({ bottom: 120 + 70 })
  const { openId } = getGlobalData<'account'>('account')
  const form = useForm()
  const values = form.getValues()
  const { result = [], run } = useRequest(`/yc-wepact-mobile/dynamics-form/file/${cityId}/${openId}`, { uuid })
  let fileItems = result as defs.dynamicsForms.FormFileItemBean[]
  useEffect(() => {
    const mobilePhone = form.getValues('mobilePhoneNum')
    const idCard = form.getValues('idCardNum')
    const emergencyP = form.getValues('emergencyPhone')
    form.reset({
      ...values,
      mobilePhoneNum: mobilePhone && encryPhoneNumber(mobilePhone),
      idCardNum: idCard && encryCertificateNumber(idCard),
      emergencyPhone: emergencyP && encryPhoneNumber(emergencyP)
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  useEffect(() => {
    pact.cityDynamicsForm.getDynamicItem.request({ cityCode: cityId, openId, uuid, pageNo: 1 }).then(res => {
      setItems(res as any)
    })
    if (cityId === '10740' || cityId === '10743') {
      pact.cityDynamicsForm.getDynamicItem.request({ cityCode: cityId, openId, uuid, pageNo: 2 }).then(res => {
        setOtherItems(res as any)
      })
    }
  }, [cityId, openId, uuid])

  const renderFileItem: any = () => {
    return (
      <>
        <FormLabel level={2} title='资料信息' />
        <View className={styles.item_wrap}>
          {fileItems
            .filter(it => it.itemCode !== 'identificationCard')
            ?.map(item => (
              <View key={item.itemCode} className={styles.file_item}>
                <Text className={styles.title}>{item.itemName}</Text>
                <View className={styles.img_wrap}>
                  {!isEmpty(item?.data) && <Image className={styles.img} src={item.data?.[0]?.fileUrl!} />}
                </View>
              </View>
            ))}
        </View>
      </>
    )
  }
  return (
    <Fragment>
      <View className={styles.tip}>
        <FormTip tip='如果写的信息有问题需要修改，请在易才管家联系您的责任客服。' />
      </View>
      <Form
        form={form}
        columns={getColumns(items, otherItems, cityId, form, renderFileItem)}
        disabled
        style={scrollStyle}
      />
      <BottomBtn btns={[{ title: '返回', onClick: () => Taro.navigateBack() }]} />
    </Fragment>
  )
}

export default withPage(Index)
