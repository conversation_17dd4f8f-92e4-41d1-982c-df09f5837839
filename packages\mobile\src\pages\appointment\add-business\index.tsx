import { Fragment, useEffect, useState, useCallback, useMemo } from 'react'
import { View, Text } from '@tarojs/components'
import Taro, { useDidShow } from '@tarojs/taro'
import { Form, BottomBtn, FormItemProps, withPage, useForm, FormTip, FormLabel, SimpleMaterialUpload } from '@components'
import { getScrollStyle } from '@utils/transforms'
import { pact } from '@apis/pact'
import { ncmp } from '@apis/ncmp'
import { encryCertificateNumber, encryPhoneNumber, getGlobalData, getGuid, navigateTo } from '@utils'
import type { FileInfo } from '@components/simple-material-upload'
import styles from './index.module.scss'


type AccountInfo = { idCardNum?: string; mobilePhoneNum?: string; empName?: string }
type options = { key: string; value: string }[]
const categoryType: options = [
  { key: '1', value: '社保业务' },
  { key: '2', value: '公积金业务' },
  { key: '3', value: '人力资源收费服务' },
]
const Index = () => {
  const [company] = useState<defs.pact.CompanyResponseData>()
  const [busnameClassOpts, setBusnameClassOpts] = useState<any[]>()
  const [busTypes, setBusTypes] = useState<any[]>()
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [accountInfo, setAccountInfo] = useState<AccountInfo>()
  const [materials, setMaterials] = useState<any[]>([])
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [materialFiles, setMaterialFiles] = useState<Record<string, FileInfo[]>>({})
  const { openId, accountId, empId } = getGlobalData<'account'>('account')
  console.log('openId, accountId, empId ', openId, accountId, empId )
  const [uuid] = useState(getGuid())
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const form = useForm()
  // 获取材料列表
  const getMaterialList = useCallback((busCityConfigId: string) => {
    if (!busCityConfigId) return

    ncmp.ebmBusiness.getCnMaterialList
      .request({
        busCityConfigId:''
      })
      .then(res => {
        if (res.code === '200' && res.resultObj) {
          setMaterials(res?.resultObj)
        } else {
          Taro.showToast({ icon: 'none', title: '获取材料列表失败' })
        }
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  }, [])

  // 处理文件变化
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleFileChange = useCallback((materialId: string, files: FileInfo[]) => {
    setMaterialFiles(prev => ({
      ...prev,
      [materialId]: files
    }))
  }, [])

  const onSubmit = (values: any) => {
    values.accountInfo.mobilePhoneNum = values?.mobilePhoneNum
    pact.appoint.appointment
      .request({
        ...values,
        busSubTypeIdStr: values?.busSubtypeId,
        cancelReason: '',
        uuid,
        busSubTypeIdStrName: undefined,
        createBy: empId,
        bookingRemark: values.bookingRemark?.replace(/[\r\n]/g, ''),
        openId
      })
      .then(res => {
        if (res.code == '200') {
          Taro.navigateBack()
          navigateTo('/appointment/success')
        } else {
          Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
        }
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  }
  useDidShow(() => {
    pact.per.personInformation
      .request({ accountId: '1698345'})
      .then(res => {
        const { idCardNum, mobilePhoneNum, empName } = res.data || {}
        const data = { idCardNum, mobilePhoneNum, empName }
        setAccountInfo(data)
        data.idCardNum = encryCertificateNumber(idCardNum);
        data.mobilePhoneNum = encryPhoneNumber(mobilePhoneNum);
        form.setValue('idCardNum', data.idCardNum);
        form.setValue('mobilePhoneNum', data.mobilePhoneNum);
        form.setValue('empName', data.empName)
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  })
  const cityId = form.watch('cityId')
  const categoryId = form.watch('categoryId')
  const bussNameClassId = form.watch('busnameClassId')
  const busSubtypeId = form.watch('busSubtypeId')

  // 缓存API调用函数，避免重复创建
  const fetchBusinessProjects = useCallback(async (catId: string) => {
    try {
      const res: any = await ncmp.policy.getBusnameClass.request({ categoryId: catId })
      if (res.code === '200' && res.resultObj) {
        return res.resultObj.map((item: any) => ({
          key: item.bussNameClassId,
          value: item.bussNameClassName
        }))
      }
      throw new Error('获取业务项目失败')
    } catch (error) {
      Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      return []
    }
  }, [])

  const fetchBusinessContents = useCallback(async (params: { busnameClassId: string; categoryId: string; cityId: string }) => {
    try {
      const res: any = await ncmp.ebmBusiness.getBusContentDropdownList.request(params)
      if (res.code === '200' && res.resultObj) {
        return res.resultObj.map((item: any, index:any) => ({
          key:index,
          value: item.busContent,
          transactProperty: item.transactProperty,
          busCityConfigId: item.busCityConfigId,
        }))
      }
      throw new Error('获取业务内容失败')
    } catch (error) {
      Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      return []
    }
  }, [])

  // 业务类型变化时直接获取业务项目数据的处理函数
  const handleCategoryChange = useCallback(async (value: string) => {
    const data = await fetchBusinessProjects(value)
    setBusnameClassOpts(data)
    form.setValue('categoryId', value)
  }, [fetchBusinessProjects, form])

  // 业务项目变化时直接获取业务内容数据的处理函数
  const handleBusinessProjectChange = useCallback(async (value: string) => {
    const data = await fetchBusinessContents({
      busnameClassId: value,
      categoryId,
      cityId
    })
    setBusTypes(data)
    form.setValue('busnameClassId', value)
  }, [categoryId, cityId, fetchBusinessContents, form])

  // 监听业务小类变化，获取材料列表
  useEffect(() => {
    if (busSubtypeId) {
      getMaterialList(busSubtypeId)
    } else {
      setMaterials([])
      setMaterialFiles({})
    }
  }, [busSubtypeId, getMaterialList])

  // 缓存表单禁用状态计算
  const formDisabledStates = useMemo(() => ({
    businessProject: !cityId || !categoryId,
    businessContent: !cityId || !categoryId || !bussNameClassId || busTypes?.length === 0
  }), [cityId, categoryId, bussNameClassId, busTypes?.length])

  const columns: FormItemProps[] = [
    {
      showLine: false,
      render: () => <FormLabel level={2} title='业务信息' />
    },
    {
      name: 'cityId',
      type: 'page_choice',
      title: '城市',
      rules: { required: true },
      pageOptions: {
        keys: ['cityId', 'cityName'],
        labelKey: 'cityName',
        url: '/policy-city'
      }
    },
    {
      isHidden: true,
      render: () => <View className={styles.company_name}>{company?.name}</View>
    },
    {
      isHidden: true,
      render: () => (
        <View>
          <View className={styles.company_title}>分公司联系信息</View>
          <View className={styles.company_name}>公司地址：{company?.address}</View>
          <View className={styles.company_name}>联系电话：{company?.contactTel}</View>
        </View>
      )
    },
    {
      title: '业务类型',
      name: 'categoryId',
      type: 'select',
      rules: { required: true },
      options: categoryType,
      disabled: !cityId,
      onChange: (value) => {
        handleCategoryChange(value)
      }
    },
    {
      title: '业务项目',
      name: 'busnameClassId',
      type: 'select',
      rules: { required: true },
      options: busnameClassOpts,
      disabled: formDisabledStates.businessProject,
      onChange: (value) => {
        handleBusinessProjectChange(value)
      }
    },
    {
      title: '业务内容',
      name: 'busContent',
      type: 'select',
      rules: { required: true },
      options: busTypes,
      disabled: formDisabledStates.businessContent,
      onChange: (value) => {
        // 判断当前 transactProperty 是单次业务，接口请求材料接口，显示材料数据
        const selectedItem = busTypes.find((item) => item.key === value)  
        console.log(busTypes)
      }
    },
    {
      title: '预约人姓名',
      rules: { required: true },
      name: 'empName',
      type: 'text',
      disabled: true
    },
    {
      title: '身份证号',
      rules: { required: true },
      name: 'idCardNum',
      type: 'text',
      disabled: true
    },
    {
      title: '手机号码',
      type: 'mobile',
      rules: { required: true },
      name: 'mobilePhoneNum'
    },
    // 若所选业务内容为 流程业务，此字段隐藏不显示
    {
      showLine: false,
      render: () => <FormLabel level={2} title='材料列表' />,
      isHidden: materials.length === 0
    },
    // 若所选业务内容为 流程业务，此字段隐藏不显示
    {
      showLine: false,
      isHidden: materials.length === 0,
      render: () => (
        <View className={styles.materialSection}>
                {/* <SimpleMaterialUpload
                  material={{
                    materialsId: '002',
                    materialsName: '合同',
                    isOriginal: '0',
                    materialsAccount: 1,
                    isReturn: '0',
                    isRequired: true
                  }}
                  files={[]}
                  uuid={uuid}
                  onFileChange={() => {}}
                  onUploadSuccess={(file) => {
                    console.log('合同上传成功:', file)
                  }}
                  onUploadError={(error) => {
                    console.error('合同上传失败:', error)
                    // 这里可以添加额外的错误处理逻辑，比如记录日志等
                  }}
                /> */}
          {materials.map((material, index) => (
            <SimpleMaterialUpload
              key={material.materialsInfo?.materialsId || index}
              material={{
                materialsId: material.materialsInfo?.materialsId || '',
                materialsName: material.materialsInfo?.materialsName || '',
                isOriginal: material.materialsInfo?.isOriginal || '0',
                materialsAccount: material.materialsAccount || (index + 1),
                isReturn: material.isReturn?.toString() || '0',
                isRequired: true // 可以根据业务逻辑调整
              }}
              files={materialFiles[material.materialsInfo?.materialsId || ''] || []}
              uuid={uuid}
              onFileChange={(files) => handleFileChange(material.materialsInfo?.materialsId || '', files)}
              onUploadSuccess={(file) => {
                console.log('上传成功:', file)
              }}
              onUploadError={(error) => {
                console.error('上传失败:', error)
              }}
            />
          ))}
        </View>
      )
    },
    {
      render: () => <FormTip tip=' 提示: 上述材料仅供参考，实际提交材料以易才管家推送为准。' />,
      isHidden: materials.length === 0
    },
    {
      title: '员工自助办理途径',
      type: 'textarea'
    },
    {
      title: '是否自助办理',
      type: 'select'
    },
    {
      title: '预约备注',
      type: 'textarea',
      name: 'bookingRemark'
    }
  ]
  return (
    <Fragment>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '提交',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </Fragment>
  )
}

export default withPage(Index)
