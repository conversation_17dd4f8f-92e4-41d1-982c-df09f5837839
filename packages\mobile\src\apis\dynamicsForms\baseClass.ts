class CustomFormBean {
  /** businessId */
  businessId = '';

  /** code */
  code = '';

  /** defaultValue */
  defaultValue = '';

  /** itemUUID */
  itemUUID = '';

  /** itemValue */
  itemValue = '';

  /** name */
  name = '';

  /** operation */
  operation = '';

  /** optionList */
  optionList = [];

  /** orderValue */
  orderValue = undefined;

  /** parentItemUUID */
  parentItemUUID = '';

  /** remind */
  remind = '';

  /** requireFlag */
  requireFlag = false;

  /** restrictValue */
  restrictValue = '';

  /** subList */
  subList = [];

  /** titleLevel */
  titleLevel = undefined;
}

class DynamicFormReqBean {
  /** birthdate */
  birthdate = '';

  /** cityCode */
  cityCode = '';

  /** idCard */
  idCard = '';

  /** name */
  name = '';

  /** openId */
  openId = '';

  /** pageNo */
  pageNo = undefined;

  /** sex */
  sex = '';

  /** uuid */
  uuid = '';
}

class EntryFileData {
  /** fileId */
  fileId = '';

  /** fileUrl */
  fileUrl = '';
}

class FormFileItemBean {
  /** businessId */
  businessId = '';

  /** data */
  data = [];

  /** itemCode */
  itemCode = '';

  /** itemName */
  itemName = '';

  /** maxCount */
  maxCount = undefined;

  /** remind */
  remind = '';

  /** requireValue */
  requireValue = false;
}

class Map {}

export const dynamicsForms = {
  CustomFormBean,
  DynamicFormReqBean,
  EntryFileData,
  FormFileItemBean,
  Map,
};
