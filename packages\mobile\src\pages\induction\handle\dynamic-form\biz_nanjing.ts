import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleNanJingColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
    const isEnterFund_nanjing = form.watch('isEnterFund_nanjing')
    const hasNanjingHealthIns = form.watch('hasNanjingHealthIns')
    if (column.name === 'isEnterFund_nanjing') {
        return { ...column, remind: ''}
    }
    if (column.name === 'insuranceType_nanjing'){
        return { ...column, isHidden: '0'.includes(hasNanjingHealthIns), remind: ''}
    }
    if (column.name === 'email') {
        return { ...column, isHidden: !'1'.includes(isEnterFund_nanjing), rules:{required: true}, remind: '用于接受易才公积金转入接受函电子版'}
    }
    return column
}
  
export { handleNanJingColumn }