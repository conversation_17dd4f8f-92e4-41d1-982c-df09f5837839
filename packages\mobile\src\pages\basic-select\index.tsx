/*
 * @Author: your name
 * @Date: 2021-10-15 17:26:24
 * @LastTime: 2021-11-26 15:16:52
 * @LastAuthor: 王正荣
 * @Description: In User Settings Edit
 * @FilePath: \mobile\src\pages\basic-select\index.tsx
 */
import { useEffect, useState } from 'react'
import { View, Image, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import basic_select from '@assets/list/basic_select.png'
import sqdksq from '@assets/list/sqdksq.png'
import xzdacx from '@assets/list/xzdacx.png'
import yhkwh from '@assets/list/yhkwh.png'
import { users } from '@apis/users'
import { getGlobalData } from '@utils'
import { withPage } from '@components';
import styles from './index.module.scss'

const Index = () => {
  const { empId, openId: cmpToken } = getGlobalData<'account'>('account')
  const [urlInfo, setUrlInfo] = useState<string>('')
  const selectBankCardPath = () => {
    users.getBankcardInfo.getBankcardInfo
      .request({ empId, cmpToken })
      .then(res => {
        if (res.code === 50020 && res.data?.[0]?.bankAcct) {
          Taro.navigateTo({ url: '/pages/bank/list/index' })
        } else {
          Taro.navigateTo({ url: '/pages/bank/empty/index' })
        }
      })
      .finally(() => Taro.hideLoading())
  }
  const list = [
    {
      title: '工资及完税情况查询',
      img: basic_select,
      onClick: () => Taro.navigateTo({ url: '/pages/wage-inquiry/main/index' })
    },
    // {
    //   title: '税前抵扣申请',
    //   img: sqdksq,
    //   onClick: () => Taro.navigateTo({ url: urlInfo })
    // },
    {
      title: '薪资档案查询',
      img: xzdacx,
      onClick: () => Taro.navigateTo({ url: '/pages/salary-files/main/index' })
    },
    {
      title: '银行卡维护',
      img: yhkwh,
      onClick: selectBankCardPath
    }
  ]
  useEffect(() => {
    if (!empId) return
    users.user.getEventFlag
      .request({
        employeeId: empId
      })
      .then(res => {
        if (res.data?.essentialFlag !== false) {
          setUrlInfo('/pages/pre-tax/declare-list/index')
        } else {
          setUrlInfo('/pages/pre-tax/pledge/index')
        }
      })
  }, [empId, urlInfo])

  return (
    <View className={styles.wrap}>
      {list.map(item => (
        <View className={styles.item} key={item.title} onClick={item.onClick}>
          <Image className={styles.img} src={item.img} />
          <Text className={styles.text}>{item.title}</Text>
        </View>
      ))}
    </View>
  )
}

export default withPage(Index)
