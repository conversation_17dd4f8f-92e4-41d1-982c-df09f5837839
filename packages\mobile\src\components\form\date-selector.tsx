import React, { FunctionComponent, useEffect, useState, useRef} from 'react'
import { View, Text, Picker, Image } from '@tarojs/components'
import dayjs from 'dayjs'
import icon_arrow from '@assets/icon/icon-arrow.png'
import { holidays } from './holidays'
import styles from './index.module.scss'
import { CommonItemProps } from './type'

const dateSelector: FunctionComponent<Omit<CommonItemProps, 'ref'> & {ref:any}> = React.forwardRef((props, ref) => {
 const { level, placeholder, options, value, ...reset } = props
 const [values, setValues] = useState<number[]>([0,0,0])
 const [range, setRange] = useState<(string | number | unknown)[]>([])
 const [label, setLabel] = useState<string>('')
 const yearsRef = useRef<string[]>([])
 const monthsRef = useRef<(string | number)[]>([])
 const daysRef = useRef<(string | number)[]>([])
 const year = useRef<string>('')
 const currentDay = useRef(dayjs().date())
 const currentMonth = dayjs().month() + 1
 const currentYear = dayjs().year()

  useEffect(() => {
   setLabel(value)
  },[value])
  useEffect(() => {
   const currentEndTime = dayjs(`${dayjs().format('YYYY-MM-DD')} 17:00`).valueOf()
   const currentTime = dayjs(dayjs().format()).valueOf()
   let month:number[] = []
   let holiday:string[] = []
   let allDate:string[] | number[] | any = []
   let dateByMonth = new Date(currentYear, currentMonth, 0).getDate()
   yearsRef.current = Object.keys(holidays[0])
   if (currentTime > currentEndTime) {
    currentDay.current = dayjs().date() + 1
   }
   for (let i = currentMonth; i <= 12; i++) {
     if (i < 10) {
       i = '0' + i
     }
     month.push(i)
   }
   monthsRef.current = month
   for (let i = 0; i < holidays.length; i++) {
     holiday = holidays[i][currentYear][currentMonth]
   }
   for (let i = currentDay.current; i <= dateByMonth; i++) {
    allDate.push(i)
   }
   for (let d = 0; d < allDate.length; d++) {
     for (let j = 0; j < holiday.length; j++) {
       if (allDate[d] === holiday[j]) {
         allDate.splice(d, 1)
       }
     }
     if (allDate[d] < 10) {
       allDate[d] = '0' + allDate[d]
     }
   }
   daysRef.current = allDate
   setRange([yearsRef.current, month, daysRef.current])
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return (
    <Picker
      ref={ref}
      mode='multiSelector'
      range={range as any}
      {...reset}
      value={values}
      onChange={
        (e: any) => {
           const [v1 = 0, v2 = 0, v3 = 0]: any = e.detail.value || []
           reset.onChange(`${yearsRef.current[v1]}-${monthsRef.current[v2]}-${daysRef.current[v3]}`)
        }
      }
      onColumnChange={e => {
       let allDate:string[] | number[] | any = []
       let holiday:string[] = []
       let month:string | number[] = []
       let dateByMonth:number
       const column = e.detail.column
       if (column === 0) {
        const v = e.detail.value || 0
        const y = Object.keys(holidays[0])
        year.current = y[v]
        if ((currentYear ==  Number(y[v])) && (new Date().getMonth() + 1  == currentMonth)) {
          dateByMonth = new Date(currentYear, currentMonth, 0).getDate()
          for (let i = currentMonth; i <= 12; i++) {
            if (i < 10) {
              i = '0' + i
            }
            month.push(i)
          }
          monthsRef.current = month
          for (let i = 0; i < holidays.length; i++) {
            holiday = holidays[i][currentYear][currentMonth]
          }
          for (let i = currentDay.current; i <= dateByMonth; i++) {
            allDate.push(i)
           }
        } else {
          dateByMonth = new Date(currentYear+1, 1, 0).getDate()
          monthsRef.current = ['01']
          for (let i = 0; i < holidays.length; i++) {
            holiday = holidays[i][currentYear+1][1]
          }
          for (let i = 1; i <= dateByMonth; i++) {
            allDate.push(i)
           }
        }
        for (let d = 0; d < allDate.length; d++) {
          for (let j = 0; j < holiday.length; j++) {
            if (allDate[d] === holiday[j]) {
              allDate.splice(d, 1)
            }
          }
          if (allDate[d] < 10) {
            allDate[d] = '0' + allDate[d]
          }
        }
        daysRef.current = allDate
        setRange([yearsRef.current, monthsRef.current, daysRef.current])
        setValues([v,0,0])
       }
       if (column === 1) {
          const v = e.detail.value || 0
          let currentM:number
          if (Number(year.current) == currentYear+1) {
            currentM = 1
            dateByMonth = new Date(currentYear, currentM, 0).getDate()
            for (let i = 0; i < holidays.length; i++) {
              holiday = holidays[i][currentYear+1][currentM]
            }
          } else {
            currentM = 12-monthsRef.current.length+v+1
            dateByMonth = new Date(currentYear, currentM, 0).getDate()
            for (let i = 0; i < holidays.length; i++) {
              holiday = holidays[i][currentYear][currentM]
            }
          }
          if (new Date().getFullYear() ==  currentYear && new Date().getMonth() + 1  == currentM) {
            for (let i = currentDay.current; i <= dateByMonth; i++) {
              allDate.push(i)
            }
           } else {
             for (let i = 1; i <= dateByMonth; i++) {
               allDate.push(i)
             }
          }
          for (let d = 0; d < allDate.length; d++) {
            for (let k = 0; k < holiday.length; k++) {
              if (allDate[d] === holiday[k]) {
                allDate.splice(d, 1)
              }
            }
            if (allDate[d] < 10) {
              allDate[d] = '0' + allDate[d]
            }
          }
        daysRef.current = allDate
        setRange([yearsRef.current, monthsRef.current, daysRef.current])
        if (Number(year.current) == currentYear+1) {
          setValues([1,v,0])
          return 
        }
        setValues([0,v,0])
       }
     }}
    >
      <View className={level === 2 ? styles.full_pick : styles.picker}>
        {label ? (
          <Text className={styles.item_text}>{label}</Text>
        ) : (
          <Text className={styles.placeholder}>{placeholder || '请选择'}</Text>
        )}
        <Image className={styles.item_arrow} src={icon_arrow} />
      </View>
    </Picker>
   )
})

export default dateSelector
