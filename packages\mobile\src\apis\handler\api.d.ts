type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};

declare namespace defs {
  export namespace handler {
    export class ResponseBean {
      /** code */
      code?: string;

      /** data */
      data?: defs.handler.ResponseData;

      /** message */
      message?: string;
    }

    export class ResponseData {
      /** accountId */
      accountId?: string;

      /** cmpToken */
      cmpToken?: string;
    }
  }
}

declare namespace API {
  export namespace handler {
    /**
     * Callback Controller
     */
    export namespace callback {
      /**
       * call
       * /handler-service/call
       */
      export namespace call {
        export class Params {}

        export type Response = any;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * call
       * /handler-service/call
       */
      export namespace postCall {
        export class Params {
          /** creationTime */
          creationTime?: number;
          /** id */
          id?: string;
          /** lastAccessedTime */
          lastAccessedTime?: number;
          /** maxInactiveInterval */
          maxInactiveInterval?: number;
          /** new */
          new?: boolean;
          /** classLoader */
          classLoader?: ref;
          /** contextPath */
          contextPath?: string;
          /** defaultSessionTrackingModes */
          defaultSessionTrackingModes?: Array<'COOKIE' | 'URL' | 'SSL'>;
          /** effectiveMajorVersion */
          effectiveMajorVersion?: number;
          /** effectiveMinorVersion */
          effectiveMinorVersion?: number;
          /** effectiveSessionTrackingModes */
          effectiveSessionTrackingModes?: Array<'COOKIE' | 'URL' | 'SSL'>;
          /** buffer */
          buffer?: string;
          /** defaultContentType */
          defaultContentType?: string;
          /** deferredSyntaxAllowedAsLiteral */
          deferredSyntaxAllowedAsLiteral?: string;
          /** elIgnored */
          elIgnored?: string;
          /** errorOnUndeclaredNamespace */
          errorOnUndeclaredNamespace?: string;
          /** includeCodas */
          includeCodas?: Array<string>;
          /** includePreludes */
          includePreludes?: Array<string>;
          /** isXml */
          isXml?: string;
          /** pageEncoding */
          pageEncoding?: string;
          /** scriptingInvalid */
          scriptingInvalid?: string;
          /** trimDirectiveWhitespaces */
          trimDirectiveWhitespaces?: string;
          /** urlPatterns */
          urlPatterns?: Array<string>;
          /** taglibLocation */
          taglibLocation?: string;
          /** taglibURI */
          taglibURI?: string;
          /** majorVersion */
          majorVersion?: number;
          /** minorVersion */
          minorVersion?: number;
          /** requestCharacterEncoding */
          requestCharacterEncoding?: string;
          /** responseCharacterEncoding */
          responseCharacterEncoding?: string;
          /** serverInfo */
          serverInfo?: string;
          /** servletContextName */
          servletContextName?: string;
          /** comment */
          comment?: string;
          /** domain */
          domain?: string;
          /** httpOnly */
          httpOnly?: boolean;
          /** maxAge */
          maxAge?: number;
          /** name */
          name?: string;
          /** path */
          path?: string;
          /** secure */
          secure?: boolean;
          /** sessionTimeout */
          sessionTimeout?: number;
          /** virtualServerName */
          virtualServerName?: string;
          /** valueNames */
          valueNames?: Array<string>;
        }

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * Subsribe Controller
     */
    export namespace subsribe {
      /**
       * 微信再次关注
       * /handler-service/againsubsribe
       */
      export namespace againsubsribe {
        export class Params {
          /** accountId */
          accountId?: string;
          /** cmpToken */
          cmpToken?: string;
        }

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 新用户注册
       * /handler-service/subsribe
       */
      export namespace subsribe {
        export class Params {
          /** openId */
          openId?: string;
        }

        export type Response = defs.handler.ResponseBean;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 微信取消关注
       * /handler-service/unsubsribe
       */
      export namespace unsubsribe {
        export class Params {
          /** accountId */
          accountId?: string;
          /** cmpToken */
          cmpToken?: string;
          /** openId */
          openId?: string;
        }

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }
  }
}
