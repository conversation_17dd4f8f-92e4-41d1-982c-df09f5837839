import { useRef, useState } from 'react'
import Taro, { useDidShow } from '@tarojs/taro'
import { AtIcon } from 'taro-ui'
import { Icon, Input, View} from '@tarojs/components'
import { BottomBtn, withPage, ListView, usePagination} from '@components'
import { getScrollStyle } from '@utils/index'
import classNames from 'classnames'
import { ncmp } from '@apis/ncmp'
import styles from './index.module.scss'


const Index = () => {
  const [selectedValues, setSelectedValues] = useState({
    province: []
  })
  const [search, setSearch] = useState<any>('')
  const inputRef = useRef<any>(null)
  const scrollStyle = getScrollStyle({ bottom: 120, top: 200,})
  const { PROVINCE } = Taro.getCurrentInstance()?.preloadData || {}
  const list = usePagination(
    async () => {
      const result:any = await  ncmp.policyEncyclopedia.listProvince.request({
        cityname: search
      })
      result.resultObj.unshift({SHORTNAME: '全部省份', KEY: '', isAllProvince: 1})
      const data = result.resultObj.reduce((pre,cur) => {
        if (cur.SHORTNAME.includes(search)){
          pre.push(cur)
        }
        return pre
      }, [])
      return data
    },
    { deps: [search] }
  )
  const handleSelectedCity = item => {
    const idx = selectedValues.province.findIndex((cattr: any) => {
      return cattr.KEY === item.KEY
    })
    let newFilterAttrs: any = []
    newFilterAttrs = selectedValues.province.slice()
    if (idx !== -1) {
      newFilterAttrs.splice(idx, 1)
      setSelectedValues({
        ...selectedValues,
        province: newFilterAttrs
      })
    } else {
      newFilterAttrs.push(item)
      let selectedCitys;
      if (!item.KEY){
        selectedCitys = newFilterAttrs.filter((it:any) => !it.KEY )
      } else {
        selectedCitys = newFilterAttrs.filter((it:any) => it.KEY )
      }
      setSelectedValues({
        ...selectedValues,
        province: selectedCitys
      })
    }
  }
  const confirm = () => {
    Taro.preload({
      ...(Taro.getCurrentInstance().preloadData || {}),
      PROVINCE: selectedValues.province,
      CITYS: [],
      SPECIALAREA: [],
    })
    Taro.navigateTo({
      url: `/pages/search_filter/index`
    })
  }
  useDidShow(() => {
    if (!PROVINCE) {
      return
    }
    setSelectedValues({
      ...selectedValues,
      province: PROVINCE
    })
  })
  return (
    <View className={styles.wrap1}>
      <View className={styles.header}>
        <View className={styles.input_wrap}>
          <Input className={styles.input_C} placeholder='搜索省份' ref={inputRef} />
          <View
            className={styles.search}
            onClick={() => {
              const value: any = inputRef.current.tmpValue || inputRef.current.value
              setSearch(value)
            }}
          >
            <Icon size='20' type='search' color='#fff' />
          </View>
        </View>
      </View>
      <ListView 
        style={scrollStyle}
        itemSize={100}
        noPull
        renderItem={(item)=>
          <View key={item.KEY} className={styles.item} onClick={() => handleSelectedCity(item)}>
          <View
            className={classNames(
              selectedValues.province.some((cAttr: any) => {
                return cAttr.KEY === item.KEY
              })
                ? styles.active
                : ''
            )}
          >
            {item.SHORTNAME}
          </View>
          <View>
            {selectedValues.province.some((cAttr: any) => {
              return cAttr.KEY === item.KEY
            }) && <AtIcon size={24} value='check' color='#B51E25' />}
          </View>
        </View>
        }
        {...list}
      />
      <BottomBtn
        btns={[
          {
            title: '确定',
            onClick: () => {
              confirm()
            }
          }
        ]}
      />
    </View>
  )
}
export default withPage(Index, {needLogin: false})
