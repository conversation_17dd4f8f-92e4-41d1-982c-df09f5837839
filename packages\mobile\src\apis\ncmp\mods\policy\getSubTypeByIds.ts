/**
 * @description 获取业务小类明细
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.BusinessSubType>;
export const path = '/wx-ncmp/policy/getSubTypeByIds';
export const method = 'POST';
export const request = (
  data: defs.ncmp.BusinessSubTypeMailQuery,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.BusinessSubTypeMailQuery,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
