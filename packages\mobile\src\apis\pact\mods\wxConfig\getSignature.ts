/**
 * @description 获取微信签名
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** url */
  url?: string;
}

export type Result = defs.pact.JsonMessage;
export const path = '/yc-wepact-mobile/getSignature';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
