import { Fragment, useEffect, useState, useRef } from 'react'
import { View, Text } from '@tarojs/components'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { Form, BottomBtn, FormItemProps, withPage, useForm, FormTip } from '@components'
import { getScrollStyle } from '@utils/transforms'
import { ncmp } from '@apis/ncmp'
import { pact } from '@apis/pact'
import { getGlobalData, getGuid, navigateTo } from '@utils'
import dayjs from 'dayjs'
import styles from './index.module.scss'

type AccountInfo = { idCardNum?: string; mobilePhoneNum?: string; empName?: string }
type options = { key: string, value: string }[]
const Options: options = [
  { key: '1', value: '社保业务' },
  { key: '2', value: '公积金业务' },
  { key: '3', value: '人力资源收费服务' },
  { key: '4', value: '定点医疗机构变更' }
]
const Index = () => {
  const [company, setCompany] = useState<defs.pact.CompanyResponseData>()
  const [things, setThings] = useState<any[]>()
  const [busTypes, setBusTypes] = useState<any[]>()
  const [accountInfo, setAccountInfo] = useState<AccountInfo>()
  const [dataOptions, setDataOptions] = useState([
    { key: '1', value: '09:00~11:00' },
    { key: '2', value: '13:00~17:00' }
  ])
  const companyOptionsRef = useRef<options>([])
  // const ref = useRef(dayjs().format('YYYY-MM-DD'))
  const { openId, accountId, empId } = getGlobalData<'account'>('account')
  const { categoryName, businessName } = useRouter().params
  const [uuid] = useState(getGuid())
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const form = useForm()
  const onSubmit = (values: any) => {
    values.accountInfo.mobilePhoneNum = values?.mobilePhoneNum
    const bookingDt = values.bookingDt?.split('-')?.join('');
    const isNumber = Number(bookingDt) + '' !== NaN + '';//会有可能出现undefined0720 等等日期的不明情况 做一个校验
    if (bookingDt.length !== 8 || !isNumber) {
      form.setValue('bookingDt', "")
      return Taro.showToast({ icon: 'none', title: '预约日期格式不正确，请重新选择' })
    }
    pact.appoint.appointment
      .request({ ...values, busSubTypeIdStr: values?.busSubtypeId, cancelReason: '', bookingDt, uuid, busSubTypeIdStrName: undefined, createBy: empId, bookingRemark: values.bookingRemark?.replace(/[\r\n]/g, ""), openId })
      .then(res => {
        if (res.code == '200') {
          Taro.navigateBack()
          navigateTo('/appointment/success')
        } else {
          Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
        }
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  }
  useDidShow(() => {
    pact.per.personInformation.request({ accountId, openId }).then(res => {
      const { idCardNum, mobilePhoneNum, empName } = res.data || {}
      const _accountInfo = { idCardNum, mobilePhoneNum, empName }
      setAccountInfo(_accountInfo)
      form.setValue('accountInfo', _accountInfo)
      form.setValue('mobilePhoneNum', mobilePhoneNum)
    }).catch(() => {
      Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
    })
  })
  const cityId = form.watch('cityId')
  const categoryId = form.watch('categoryId')
  const bussNameClassId = form.watch('bussNameClassId')
  const busTypeId = form.watch('busTypeId')
  const busSubtypeId = form.watch('busSubtypeId')
  const bookingDt = form.watch('bookingDt')
  const dateType = form.watch('dateType')
  // 处理当前预约人数
  useEffect(() => {
    if (dateType && bookingDt && cityId) {
      const bookingDate= bookingDt?.split('-').join('')
      const isNumber = Number(bookingDate) + '' !== NaN + '';//会有可能出现undefined0720 等等日期的不明情况 做一个校验
      if (bookingDate.length !== 8 || !isNumber) {
        form.setValue('bookingDt', "")
        Taro.showToast({ icon: 'none', title: '预约日期格式不正确，请重新选择,以获取预约人数' })
        return
      }
      pact.appoint.getAppointmentNum.request({ cityId, dateType, bookingDate, openId }).then(res => {
        form.setValue('personNum', res.data?.data)
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dateType, bookingDt, cityId])

  useEffect(() => {
    if (!bookingDt) return
    const currentTime = dayjs(dayjs().format()).valueOf()
    const endTime1 = dayjs(`${bookingDt} 11:00`).valueOf()
    const endTime2 = dayjs(`${bookingDt} 17:00`).valueOf()
    const tomorrowTime = dayjs(`${bookingDt} 09:00`).valueOf()
    if (tomorrowTime > currentTime || currentTime > endTime2) {
      setDataOptions([{ key: '1', value: '09:00~11:00' }, { key: '2', value: '13:00~17:00' }])
    } else {
      currentTime > endTime1 && setDataOptions([{ key: '2', value: '13:00~17:00' }])
    }
  }, [bookingDt])

  // 处理分公司
  useEffect(() => {
    if (cityId) {
      pact.appoint.getBranchCompanies.request({ cityId, openId: openId }).then(res => {
        const department = res.data?.[0]
        setCompany(department)
        form.setValue('departmentId', department?.id)
      })
      if (cityId !== '10740' && form.getValues('categoryId') === '4') {
        form.setValue('categoryId', undefined)
      }
    }
    if (cityId === '10740') {
      companyOptionsRef.current = Options
      return
    }
    companyOptionsRef.current = Options.slice(0, -1)
    // eslint-disable-next-line react-hooks/exhaustive-deps 
  }, [cityId])
  useEffect(()=> {
    if (categoryName){
      try {
        const decodedCategoryName = decodeURIComponent(categoryName);
        const categoryNameId = getKeyByValue(Options, decodedCategoryName);
        form.setValue('categoryId', categoryNameId)
      } catch (error) {
        console.error('Error decoding categoryName:', error);
        const categoryNameId = getKeyByValue(Options, categoryName);
        form.setValue('categoryId', categoryNameId)
      }
    }
  }, [categoryName])
  useEffect(() => {
    if (categoryId) {
      const values = form.getValues()
      console.log('values', values)
    
      // 重新选择分类 清空事物类别、具体事物
      form.reset({ ...values, bussNameClassId: undefined, bussNameClassName: undefined, busTypeId: undefined, busTypeName: undefined, busSubtypeId: undefined, busSubtypeName: undefined })
      setThings([])
    
      ncmp.policy.getBusnameClass.request({categoryId})
      .then((res: any) => {
        if (res.code == '200') {
          let data = res?.resultObj?.map(item => {
            return {
              key: item.bussNameClassId,
              value: item.bussNameClassName
            }
          })
          if (businessName){
            try {
              const decodedBusinessName = decodeURIComponent(businessName);
              const businessNameId = getKeyByValue(data, decodedBusinessName);
              form.setValue('bussNameClassId', businessNameId)
            } catch (error) {
              console.error('Error decoding businessName:', error);
              const businessNameId = getKeyByValue(data, businessName);
              form.setValue('bussNameClassId', businessNameId)
            }
          }
          setThings(data)
        } else {
          Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
        }
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryId, businessName])
  useEffect(() => {
    if (bussNameClassId) {
      const values = form.getValues()
      form.reset({ ...values, busTypeId: undefined, busTypeName: undefined, busSubtypeId: undefined, busSubtypeName: undefined })
      setBusTypes([])
      ncmp.policy.getBusType
        .request({ busnameClassId: bussNameClassId, categoryId, cityId })
        .then((res: any) => {
          if (res.code == '200') {
            let data = res?.resultObj?.map(item => {
              return {
                key: item.busTypeId,
                value: item.busTypeName
              }
            })
            setBusTypes(data)
          } else {
            Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
          }
        })
        .catch(() => {
          Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
        })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bussNameClassId])
  useEffect(() => {
    if (busTypeId) {
      const values = form.getValues()
      form.reset({ ...values, busSubtypeId: undefined, busSubtypeName: undefined })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [busTypeId])
  useEffect(() => {
    if (busTypeId) {
      form.setValue('busSubtypeId', undefined)
      form.setValue('busSubtypeName', undefined)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [busTypeId])

  const columns: FormItemProps[] = [
    {
      name: 'cityId',
      type: 'page_choice',
      title: '城市',
      rules: { required: true },
      pageOptions: {
        keys: ['cityId', 'cityName'],
        labelKey: 'cityName',
        url: '/policy-city'
      }
    },
    {
      isHidden: !company?.name,
      render: () => <View className={styles.company_name}>{company?.name}</View>
    },
    {
      isHidden: !company?.address,
      render: () => (
        <View>
          <View className={styles.company_title}>分公司联系信息</View>
          <View className={styles.company_name}>公司地址：{company?.address}</View>
          <View className={styles.company_name}>联系电话：{company?.contactTel}</View>
        </View>
      )
    },
    {
      title: '所属类型',
      name: 'categoryId',
      type: 'select',
      rules: { required: true },
      options: companyOptionsRef.current,
      disabled: !cityId,
      onClick: () => {
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
        }
      }
    },
    {
      title: '业务项目',
      name: 'bussNameClassId',
      type: 'select',
      rules: { required: true },
      options: things,
      disabled: !cityId || !categoryId,
      onClick: () => {
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
        }
        if (!categoryId) {
          Taro.showToast({ title: '请选择所属类型' })
        }
      }
    },
    {
      title: '业务大类',
      name: 'busTypeId',
      type: 'select',
      rules: { required: true },
      options: busTypes,
      disabled: !cityId || !categoryId || !bussNameClassId || busTypes?.length === 0,
      onClick: () => {
        if (busTypes?.length === 0) {
          Taro.showToast({ title: '暂无可选信息！' })
          return
        }
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
          return
        }
        if (!categoryId) {
          Taro.showToast({ title: '请选择所属类型' })
          return
        }
        if (!bussNameClassId) {
          Taro.showToast({ title: '请选择业务项目' })
          return
        }
      }
    },
    {
      title: '业务小类',
      name: 'busSubtypeId',
      type: 'page_choice',
      rules: { required: true },
      pageOptions: {
        scen: 'AppointmentBusType',
        title: '业务小类',
        keys: ['busSubtypeId', 'busSubtypeName'],
        labelKey: 'busSubtypeName',
        message: { busTypeId, busSubtypeId }
      },
      disabled: !cityId || !categoryId || !bussNameClassId || !busTypeId,
      onClick: () => {
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
          return
        }
        if (!categoryId) {
          Taro.showToast({ title: '请选择所属类型' })
          return
        }
        if (!bussNameClassId) {
          Taro.showToast({ title: '请选择业务项目' })
          return
        }
        if (!busTypeId) {
          Taro.showToast({ title: '请选择业务大类' })
          return
        }
      }
    },
    {
      title: '办理所需材料',
      type: 'page_choice',
      name: 'matertitle',
      placeholder: '查看/下载/上传',
      pageOptions: {
        scen: 'AppointmentMaterial',
        url: '/appointment/material',
        message: {
          categoryId,
          busTypeId,
          cityId,
          busSubtypeId,
          uuid,
          categoryName: companyOptionsRef.current.find(t => t.key === categoryId)?.value,
          bussNameClassName: things?.find(t => t.key === bussNameClassId)?.value,
          busTypeName: busTypes?.find(t => t.key === busTypeId)?.value,
          busSubtypeName: form.getValues('busSubtypeName')
        }
      },
      disabled: !cityId || !categoryId || !bussNameClassId || !busTypeId || !busSubtypeId,
      onClick: () => {
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
          return
        }
        if (!categoryId) {
          Taro.showToast({ title: '请选择所属类型' })
          return
        }
        if (!bussNameClassId) {
          Taro.showToast({ title: '请选择业务项目' })
          return
        }
        if (!busTypeId) {
          Taro.showToast({ title: '请选择业务大类' })
          return
        }
        if (!busSubtypeId) {
          Taro.showToast({ title: '请选择业务小类' })
          return
        }
      }
    },
    {
      title: '预约人姓名',
      rules: { required: true },
      render: () => <Text className={styles.company_name}>{accountInfo?.empName}</Text>
    },
    {
      title: '身份证号',
      rules: { required: true },
      render: () => <Text className={styles.company_name}>{accountInfo?.idCardNum}</Text>
    },
    {
      title: '手机号码',
      type: 'mobile',
      rules: { required: true },
      name: 'mobilePhoneNum'
    },
    {
      title: '预约日期',
      rules: { required: true },
      type: 'date_selector',
      name: 'bookingDt',
    },
    {
      title: '预约时间',
      rules: { required: true },
      type: 'select',
      name: 'dateType',
      options: dataOptions
    },
    {
      title: '当前预约人数',
      name: 'personNum',
      render: field => <Text className={styles.company_name}>{field?.value}</Text>
    },
    {
      title: '预约备注',
      type: 'textarea',
      name: 'bookingRemark'
    },
    {
      showLine: false,
      render: () => (
        <FormTip tip=' 提示: 如医疗机构变更, 请留下您现在需要备案的医疗机构全称; 如手机号变更, 请留下您新的手机号; 其他业务办理内容补充' />
      )
    }
  ]
  const getKeyByValue = (OptionsData, value) => {
    // Handle null or undefined values
    if (!value || !OptionsData || !Array.isArray(OptionsData)) {
      return null;
    }
    
    try {
      // Try to decode URL-encoded value if needed
      const decodedValue = decodeURIComponent(value.trim());
      
      // First try exact match with decoded value
      let option = OptionsData.find(opt => 
        opt && opt.value && opt.value.trim() === decodedValue
      );
      
      // If no match found, try with the original value
      if (!option) {
        option = OptionsData.find(opt => 
          opt && opt.value && opt.value.trim() === value.trim()
        );
      }
      
      return option ? option.key : null;
    } catch (error) {
      // If decoding fails, fall back to original value
      const option = OptionsData.find(opt => 
        opt && opt.value && opt.value.trim() === value.trim()
      );
      
      return option ? option.key : null;
    }
  }
  return (
    <Fragment>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '提交',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </Fragment>
  )
}

export default withPage(Index)
