/**
 * @description 查询客服返回的办理附件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 业务办理ID */
  businessId: number;
  /** 文件主键id */
  ebmBusinessImageId?: number;
  /** empId */
  empId?: string;
}

export type Result = defs.upload.HttpResult<
  Array<defs.upload.CtEbmBusinessImageDTO>
>;
export const path = '/wx-upload/appointment/fetchBusinessFile';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
