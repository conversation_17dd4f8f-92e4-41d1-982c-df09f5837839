import { AtModal } from "taro-ui";
import { ComponentOptions } from "@tarojs/taro";
import { FunctionComponent } from "react";
import { ModalProps } from "./type";
import Content from "./content";
import "./index.global.scss";
import { useModal } from './useModal';


const Modal: FunctionComponent<ModalProps> & {
  options?: ComponentOptions;
  useModal: typeof useModal
} = props => {
  const { visible = false, ...rest } = props;
  return (
    <AtModal isOpened={visible} closeOnClickOverlay={false}>
      <Content {...rest} />
    </AtModal>
  );
};

Modal.options = {
  addGlobalClass: true
};

Modal.useModal = useModal;

export default Modal;
