/*
 * @Author: 刘双
 * @Email: <EMAIL>
 * @Date: 2020-09-07 16:46:26
 * @LastAuthor: 刘双
 * @LastTime: 2020-10-09 18:36:44
 * @message:
 */
import * as Pont from "pont-engine";
import { CodeGenerator, Interface, BaseClass, Property } from "pont-engine";

export default class MyGenerator extends CodeGenerator {
  surrounding: any;
  // 接口声明
  getInterfaceContentInDeclaration(inter: Interface) {
    const paramsCode = inter.getParamsCode("Params");

    const requestParams = inter.getRequestParams(this.surrounding);
    let paramsType: string = "";
    let bodyType: string = "";
    requestParams.split(",").forEach((item) => {
      const [name, type] = item.split(":");
      if (name.includes("params")) {
        paramsType = type;
      }
      if (name.includes("body")) {
        bodyType = type;
      }
    });
    // path body params form 请求参数
    let requestDataType = bodyType ? bodyType : paramsType;
    return `
      export ${paramsCode}
      export type Response = ${inter.responseType}
      export const request:(data?: ${requestDataType},options?: Taro.request.CommonOption) => Promise<Response>
      export const useRequest:(data?: ${requestDataType},options?: Taro.request.CommonUseRequestOption<${requestDataType}>) => Taro.request.CommonUseResultType<Response,${requestDataType}>
    `;
  }

  getBaseClassInDeclaration(base: BaseClass) {
    const originProps = base.properties;
    base.properties = base.properties.map((prop) => {
      return new Property({
        ...prop,
        required: false,
      });
    });
    const result = super.getBaseClassInDeclaration(base);
    base.properties = originProps;
    return result;
  }

  getInterfaceContent(inter: Interface) {
    const path = inter.path;
    const method = inter.method.toUpperCase();
    const requestParams = inter.getRequestParams(this.surrounding);
    let paramsType: string = "";
    let bodyType: string = "";
    requestParams.split(",").forEach((item) => {
      const [name, type] = item.split(":");
      if (name.includes("params")) {
        paramsType = type;
      }
      if (name.includes("body")) {
        bodyType = type;
      }
    });
    // path body params form 请求参数
    let requestDataType = bodyType ? bodyType : paramsType;
    const paramsCode = inter.getParamsCode("Params", this.surrounding);
    let optionString =
      method === "GET" ? "options" : `{ method:'${method}', ...options }`;

    return `
     /**
      * @description ${inter.description.split("\n")[0]}
      */
      import { useRequest as useFetch,request as fetch } from "@utils/request";

      export ${paramsCode}
      export type Result = ${inter.responseType}
      export const path = '${path}'
      export const method = '${method}'
      export const request = (data:${requestDataType},options?:Taro.request.CommonOption) => fetch<Result,Params>(path,data,${optionString})
      export const useRequest = (data:${requestDataType},options?:Taro.request.CommonUseRequestOption<Result>) => useFetch<Result,Params>(path,data,${optionString})
   `;
  }
}




export class FileStructures extends Pont.FileStructures {
  getDataSourcesTs() {
    const dsNames = (this as any).generators.map((ge) => ge.dataSource.name);
    return `
      ${dsNames
        .map((name: any) => {
          return `import { defs as ${name}Defs, ${name} } from './${name}';
          `;
        })
        .join('\n')}

      export const defs = {
        ${dsNames.map((name) => `${name}: ${name}Defs,`).join('\n')}
      };
      export const API = {
        ${dsNames.join(',\n')}
      };
      const g:any = IS_RN ? global : window;
      g.API = API;

    `;
  }

  getDataSourcesDeclarationTs() {
    const dsNames = (this as any).generators.map((ge) => ge.dataSource.name);
    return `
    ${dsNames
      .map((name) => {
        return `/// <reference path="./${name}/api.d.ts" />`;
      })
      .join('\n')}

      type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
        [key in Key]: Value;
      }

    `;
  }
}
