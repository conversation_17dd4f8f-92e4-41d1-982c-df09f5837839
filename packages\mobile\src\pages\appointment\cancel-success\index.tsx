/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-17 17:25:18
 * @LastAuthor: 王正荣
 * @LastTime: 2021-11-18 17:43:48
 * @message: 
 */
import { View, Image, ScrollView } from '@tarojs/components'
import { getScrollStyle } from '@utils'
import { BottomBtn, withPage } from '@components'
import Taro, { useRouter } from '@tarojs/taro'
import Success from '@assets/appointment/img-success.png'
import styles from './index.module.scss'

const Index = () => {

  const { source } = useRouter<{ source: string }>().params
  const scrollStyle = getScrollStyle({ bottom: 120 })

  return (
    <View>
      <ScrollView style={scrollStyle} scrollY>
        <View className={styles.img_wrap}>
          <Image className={styles.img} src={Success} />
        </View>
        <View className={styles.text_wrap}>
          <View className={styles.title}>取消成功</View>
          <View className={styles.content}>您已成功取消预约，如有问题，请随时联系您的易才管家。</View>
        </View>
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回预约列表',
            onClick: () => {
              Taro.navigateBack({ delta: Number(source) })
              Taro.eventCenter.trigger('Appointment')
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
