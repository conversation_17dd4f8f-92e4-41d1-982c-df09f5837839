/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-08-17 10:11:12
 * @LastAuthor: 王正荣
 * @LastTime: 2021-09-14 14:16:07
 * @message:
 */
import { View, ScrollView } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { getGlobalData, getScrollStyle } from '@utils'
import { pact } from '@apis/pact'
import { BottomBtn, withPage, Labels, FormLabel } from '@components'
import Taro,{ useRouter } from '@tarojs/taro'
import styles from './index.module.scss'

const Index = () => {
  const { openId, empId } = getGlobalData<'account'>('account')
  const { type, data } = useRouter<{ type: 'social-security' | 'accumulation'; data: string }>().params
  const originData = JSON.parse(data)
  const [list, setList] = useState<Array<defs.pact.FundDetailData>>()
  const scrollStyle = getScrollStyle({ bottom: 120 })

  useEffect(() => {
    pact.busi.getSocialnsuranceAndfundDetail
      .request({
        openId,
        empId,
        category: type === 'social-security' ? '1' : '2',
        serviceMonth: originData.serviceMonth
      })
      .then(res => {
        setList(res.data)
      })
  }, [originData.serviceMonth, type, openId, empId])
  return (
    <View>
      <ScrollView style={scrollStyle} scrollY>
        <View>
          <FormLabel level={2} title={originData.serviceMonth} />
          {/* <Labels title='企业名称' detail={originData.custName} /> */}
          <Labels title='企业金额' detail={originData.eAmt} />
          <Labels title='个人金额' detail={originData.pAmt} />
          <Labels title='合计' detail={originData.amt} />
        </View>
        {list?.map(item => (
          <View className={styles.group} key={item.productName}>
            <FormLabel level={2} title={item.productName || ''} />
            <Labels title='城市' detail={item.city} />
            <Labels title='企业基数' detail={item.eBase} />
            <Labels title='企业比例' detail={item.eRatio} />
            <Labels title='个人基数' detail={item.pBase} />
            <Labels title='个人比例' detail={item.pRatio} />
            <Labels title='企业附加' detail={item.pAdditionalAmt} />
            <Labels title='企业金额' detail={item.eAmt} />
            <Labels title='个人金额' detail={item.pAmt} />
          </View>
        ))}
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回列表',
            onClick: () => {
              Taro.navigateBack({ delta: 1 })
            }
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
