.wrap {
  width: 100%;
  background-color: #ffffff;
  flex: 1;
  position: relative;
}

.label_wrap {
  display: flex;
  align-items: center;
  flex-direction: row;
  min-height: 88px;
  position: relative;
}
.label{
  display: flex;
  flex-direction: column;
  border: 0px solid #efeff4;
  border-bottom-width: 1px;
  padding-top: 24px;
  padding-bottom: 24px;
}
.label_wrap2 {
  display: flex;
  align-items: center;
  flex-direction: row;

}

.required {
  margin-left: 30px;
  width: 16px;
  height: 16px;
}

.title1 {
  font-size: 30px;
  color: #b51e25;
}

.tips {
  position: absolute;
  top: 55px;
  font-size: 24px;
  color: #b51e25;
}

.title1_line {
  width: 20px;
  height: 2px;
  background-color: #b51e25;
  margin: 0px 20px;
}

.title2 {
  font-size: 30px;
  font-weight: 400;
  color: #333;
  margin-left: 20px;
  margin-right: 46%;
}
.title22 {
  font-size: 30px;
  font-weight: 400;
  color: #333;
  margin-left: 20px;
}

.title2_line {
  background-color: #b51e25;
  width: 8px;
  height: 30px;
}

.title3 {
  font-size: 30px;
  color: #333;
  margin-left: 20px;
  flex: 1;
}

.item {
  display: flex;
  align-items: center;
  flex-direction: row;
  border: 0px solid #efeff4;
  border-bottom-width: 1px;
  padding: 16px 0;
  height:auto;
  min-height: 88px;
  flex-wrap: wrap;
  line-height: 36px;
}

.placeholder {
  font-size: 30px;
  color: #999;
}

.item_text {
  font-size: 30px;
  color: #999;
  width: 380px;
  flex: 1;
  flex-wrap: wrap;
/*  #ifdef  h5  */
word-break:break-all;
/*  #endif  */
  display: flex;
}

.item_arrow {
  height:20.55px;
  width: 12.97px;
  margin-left: 20px;
}

.picker {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 415px;
  flex-wrap: wrap;
}

.full_pick {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 710px;
  flex-wrap: wrap;
  margin: 0 20px;
  min-height: 88px;
}


.input {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
  font-size: 28px;
  color: #333;
  height: 72px;
  width: 430px;
  background-color: #fff;
}

.input_text {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
  margin: 20px 10px;
  font-size: 28px;
  color: #333;
  height: 72px;
  width: 96%;
  background-color: #fff;
}
.picker1 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 290px;
  flex-wrap: wrap;
}
.input_text1 {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
  margin: 20px 10px;
  font-size: 28px;
  color: #333;
  height: 72px;
  width: 290px;
  background-color: #fff;
}
.input_text:focus,
.input_text:hover,
.input:focus,
.input:hover,
.textarea:hover,
.textarea:focus {
  border-color: #b51f24;
}

.textarea {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  display: flex;
  flex-direction: row;
  align-items: center;
  color: #333;
  height: 172px;
  width: 420px;
  padding: 16px;
}
textarea{
  font-size: 28px
}

.single {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 24px 0;
}

.tag_wrap {
  border-radius: 8px;
  border: 1px solid #ccc;
  height: 80px;
  width: 45%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 15px 10px;
}

.tag {
  color: #ccc;
  font-size: 30px;
}
.error {
  color: #b51e25;
  font-size: 28px;
  text-align: center;
  display: flex;
}

.tip {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 20px;
  margin-left: 15px;
}

.tip_img {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.tip_text {
  color: #555f79;
  font-size: 24px;
  display: flex;
  flex: 1;
  padding-bottom: 24px;
}
