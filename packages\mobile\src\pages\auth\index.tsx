/**
 * 给微信小程序用的鉴权页面
 */

import { withPage } from '@components/page'
import { View } from '@tarojs/components'
import { getGlobalData } from '@utils/global-data'
import { useEffect } from 'react'

const Index = () => {
  const account = getGlobalData<'account'>('account')
  useEffect(() => {
    if (account.openId && IS_H5) {
      console.log('account-----', account)
      console.log('是否是小程序环境', window.miniProgram)
      window.postMessage({ type: 'account', meaasge: account })
      wx.miniProgram.postMessage({ type: 'account', meaasge: account })
      wx?.miniProgram?.navigateTo()
    }
  }, [account])
  return <View></View>
}

export default withPage(Index)
