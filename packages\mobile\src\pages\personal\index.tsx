/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-08-17 10:11:12
 * @LastAuthor: 王正荣
 * @LastTime: 2021-09-14 14:25:26
 * @message:
 */
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { useEffect, useState } from 'react'
import { encryCertificateNumber, encryPhoneNumber, getGlobalData, getPlatformCode, isWx, setGlobalData } from '@utils'
import { getScrollStyle } from '@utils/transforms'
import Taro from '@tarojs/taro'
import icon_user from '@assets/icon/icon-user.png'
import isEmpty from 'lodash/isEmpty'
import { auth } from '@apis/auth'
import { BottomBtn, Modal, withPage } from '@components'
import { pact } from '@apis/pact'
import styles from './index.module.scss'


const Index = () => {
  const account = getGlobalData<'account'>('account')
  const [personInfo, setPersonInfo] = useState<defs.pact.PersonInfoRespData | undefined>()
  const [sSInfo, setSSInfo] = useState<Array<defs.pact.PersonSocialSecurityData> | undefined>()
  const [relation, setRelation] = useState<Array<defs.pact.PersonRelationData> | undefined>()
  const [visible, setVisible] = useState<boolean>(false)
  const { accountId, openId } = account
  useEffect(() => {
    pact.per.personInformation
      .request({
        accountId,
        openId
      })
      .then(res => {
        !isEmpty(res.data) && setPersonInfo(res.data)
        !isEmpty(res.data?.socialSecurity) && setSSInfo(res.data?.socialSecurity)
        !isEmpty(res.data?.relation) && setRelation(res.data?.relation)
      })
  }, [accountId, openId])

  const info = [
    {
      title: '身份证号',
      data: encryCertificateNumber(personInfo?.idCardNum)
    },
    {
      title: '公司名称',
      data: personInfo?.company
    },
    {
      title: '手机号码',
      data: encryPhoneNumber(personInfo?.mobilePhoneNum)
    },
    {
      title: '电子邮箱',
      data: personInfo?.email
    },
    {
      title: '出生年月',
      data: personInfo?.birthday
    },
    {
      title: '性别',
      data: personInfo?.gender
    },
    {
      title: '民族',
      data: personInfo?.ethnic
    },
    {
      title: '文化程度',
      data: personInfo?.educationLevel
    },
    {
      title: '政治面貌',
      data: personInfo?.politicalStatus
    },
    {
      title: '健康状态',
      data: personInfo?.healthStatus
    },
    {
      title: '求职状态',
      data: personInfo?.jobStatus
    },
    {
      title: '工作年限',
      data: personInfo?.workExperience
    },
    {
      title: '目前年薪',
      data: personInfo?.currentSalary
    }
  ]

  const addressInfo = [
    {
      title: '详细地址',
      data: personInfo?.residentAddress
    },
    {
      title: '邮政编码',
      data: personInfo?.residentZipCode
    }
  ]
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const goBindingPage = () => {
    Taro.navigateTo({
      url: `/pages/binding/index`
    })
  }
  return (
    <View className={styles.wrap}>
      <ScrollView style={scrollStyle} scrollY>
        <View className={styles.header_user}>
          <View className={styles.bgImage_right}>
            <Image className={styles.bg_image_right} src={require('@assets/personal/bg-logo.png')} />
          </View>
          <View className={styles.bgImage}>
            <Image className={styles.bg_image} src={require('@assets/personal/bg-logo.png')} />
          </View>
          <View className={styles.user_info}>
            <View style={{ display: 'flex' }}>
              <Image className={styles.user_avatar} src={personInfo?.picUrl ? personInfo?.picUrl : ''} />
            </View>
            <View className={styles.user_group}>
              <View className={styles.user_group_left}>
                <View className={styles.user_group_inner}>
                  <Image className={styles.icon_text} src={icon_user} />
                  {personInfo?.empName}
                </View>
              </View>
            </View>
          </View>
        </View>

        <View className={styles.group_item}>
          {info.map(item => (
            <View key={item.title} className={styles.info}>
              <Text className={styles.item_title}>{item.title}</Text>
              <Text className={styles.item_data}>{item.data}</Text>
            </View>
          ))}
        </View>

        <View className={styles.group_item}>
          <View className={styles.title_secondary}>
            <View className={styles.title_mark}></View>
            现居住地址
          </View>
          <View className={styles.info}>
            <Text className={styles.item_title}>省、市</Text>
            <Text className={styles.item_data}>
              {personInfo?.residentProvince}
              {personInfo?.residentCity}
            </Text>
          </View>
          {addressInfo.map(item => (
            <View key={item.title} className={styles.info}>
              <Text className={styles.item_title}>{item.title}</Text>
              <Text className={styles.item_data}>{item.data}</Text>
            </View>
          ))}
        </View>

        <View className={styles.group_item}>
          <View className={styles.title_secondary}>
            <View className={styles.title_mark}></View>
            银行账号
          </View>
        </View>
        <View className={styles.box_b}>
          <View className={styles.box_dotted}>
            <View className={styles.list_stripe}>
              {/* {bankInfo.map((item) => (
                <View key={item.title} className={styles.list_stripe_info}>
                  <Text className={styles.item_title}>{item.title}</Text>
                  <Text className={styles.item_data}>{item.data}</Text>
                </View>
              ))} */}
              <View key='开户行' className={styles.list_stripe_info}>
                <Text className={styles.list_stripe_item_title}>开户行</Text>
                <Text className={styles.item_data}>{personInfo?.bankName}</Text>
              </View>
              <View key='城市' className={styles.list_stripe_info_dark}>
                <Text className={styles.list_stripe_item_title}>城市</Text>
                <Text className={styles.item_data}>{personInfo?.bankCity}</Text>
              </View>
              <View key='支行' className={styles.list_stripe_info}>
                <Text className={styles.list_stripe_item_title}>支行</Text>
                <Text className={styles.item_data}>{personInfo?.subbranch}</Text>
              </View>
              <View key='账号' className={styles.list_stripe_info_dark}>
                <Text className={styles.list_stripe_item_title}>账号</Text>
                <Text className={styles.item_data}>{personInfo?.bankAccount}</Text>
              </View>
            </View>
          </View>
        </View>

        <View className={styles.group_item}>
          <View className={styles.title_secondary}>
            <View className={styles.title_mark}></View>
            个人社保公积金信息
          </View>
        </View>
        <View className={styles.box_b}>
          <View className={styles.box_dotted}>
            <View className={styles.list_stripe}>
              {sSInfo
                ? sSInfo.map(item => (
                    <View key={item.acct}>
                      <View key='城市名称' className={styles.list_stripe_info}>
                        <Text className={styles.list_stripe_item_title}>城市名称</Text>
                        <Text className={styles.item_data}>{item.cityName}</Text>
                      </View>
                      <View key='社保类型' className={styles.list_stripe_info_dark}>
                        <Text className={styles.list_stripe_item_title}>社保类型</Text>
                        <Text className={styles.item_data}>{item.ssGroupType}</Text>
                      </View>
                      <View key='保险名称' className={styles.list_stripe_info}>
                        <Text className={styles.list_stripe_item_title}>保险名称</Text>
                        <Text className={styles.item_data}>{item.insuranceName}</Text>
                      </View>
                      <View key='帐号' className={styles.list_stripe_info_dark}>
                        <Text className={styles.list_stripe_item_title}>帐号</Text>
                        <Text className={styles.item_data}>{item.acct}</Text>
                      </View>
                    </View>
                  ))
                : null}
            </View>
          </View>
        </View>

        <View className={styles.group_item}>
          <View className={styles.title_secondary}>
            <View className={styles.title_mark}></View>
            身份证
          </View>
        </View>
        <View className={styles.box_b}>
          <View className={styles.box_dotted}>
            <View className={styles.sun_row}>
              <View className={styles.sun_col}>
                {
                  personInfo?.idCardPositive && <Image className={styles.sun_thumb} src={personInfo?.idCardPositive} />
                }
              </View>
              <View className={styles.sun_col}>
                {
                  personInfo?.idCardNegative && <Image className={styles.sun_thumb} src={personInfo?.idCardNegative} />
                }
              </View>
            </View>
          </View>
        </View>

        <View className={styles.group_item}>
          <View className={styles.title_secondary}>
            <View className={styles.title_mark}></View>
            连带人管理
          </View>
        </View>
        <View className={styles.box_b}>
          {relation
            ? relation.map(item => (
                <View key={item.idCardNum}>
                  <View key='城市名称' className={styles.info}>
                    <Text className={styles.item_title}>姓名</Text>
                    <Text className={styles.item_data}>{item.name}</Text>
                  </View>
                  <View key='社保类型' className={styles.info}>
                    <Text className={styles.item_title}>身份证</Text>
                    <Text className={styles.item_data}>{encryCertificateNumber(item.idCardNum)}</Text>
                  </View>
                  <View key='保险名称' className={styles.info}>
                    <Text className={styles.item_title}>出生日期</Text>
                    <Text className={styles.item_data}>{item.birthDate}</Text>
                  </View>
                  <View key='帐号' className={styles.info}>
                    <Text className={styles.item_title}>与本人关系</Text>
                    <Text className={styles.item_data}>{item.relationship}</Text>
                  </View>
                  <View style={{ width: '50%' }}>
                    <Image
                      style={{ position: 'relative', background: '#EFEFF4' }}
                      src={item?.proofRelPath ? item?.proofRelPath : ''}
                    />
                  </View>
                </View>
              ))
            : null}
        </View>
      </ScrollView>
      <BottomBtn
        btns={[
          {
            title: '返回首页',
            onClick: () => {
              Taro.navigateBack({ delta: 1 })
            }
          },
          {
            title: '退出登录',
            onClick: () => {
              setVisible(true)
            }
          }
        ]}
      />
      <Modal
        visible={visible}
        onClose={() => {
          setVisible(false)
        }}
        onConfirm={() => {
          setVisible(false)
          if (isWx()) {
            pact.auth.unbindAccount.request({ openId: openId }).then(res => {
              if (res.code === '200' && res.data?.result === '1') {
                setGlobalData('account', { ...account, bindStatus: false })
                goBindingPage()
              } else {
                Taro.showToast({title: '系统异常', icon: 'none'})
              }
            })
          } else {
            const PlatformInfo = sessionStorage.getItem('PlatformInfo')
            if(PlatformInfo){
              const platformOpenid = JSON.parse(PlatformInfo).platformOpenid;
              auth.authWeb.deleteBind.request({platformOpenid, platform: getPlatformCode()}).then(res => {
                if (res.success){
                  Taro.showToast({title: '成功解除绑定', icon: 'none'})
                  goBindingPage()
                } else {
                  Taro.showToast({title: '系统异常', icon: 'none'})
                }
              })
            }
          }
        }}
        title='确定退出登录吗'
      ></Modal>
    </View>
  )
}

export default withPage(Index)
