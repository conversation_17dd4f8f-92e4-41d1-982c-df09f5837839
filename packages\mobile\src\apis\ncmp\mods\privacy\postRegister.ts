/**
 * @description 签署同意最新版本
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<boolean>;
export const path = '/wx-ncmp/privacy/register';
export const method = 'POST';
export const request = (
  data: defs.ncmp.PrivacyParam,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.PrivacyParam,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
