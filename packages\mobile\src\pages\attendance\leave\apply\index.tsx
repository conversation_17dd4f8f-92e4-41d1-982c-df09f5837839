
import Taro, { useRouter } from '@tarojs/taro'
import { Text, View } from '@tarojs/components'
import { BottomBtn, Form, FormItemProps, useForm, withPage } from '@components'
import styles from './index.module.scss'
import { getGlobalData, getScrollStyle } from '@utils/index'
import { useEffect, useState } from 'react';
import { ncmp } from '@apis/ncmp'

const holidayUnit = ['', '15分钟', '30分钟', '小时', '半天', '天']
// holType 假期（0自定义假期 1年假 2事假 3病假  4婚假 5产检假  6产假 ）

const Index = () => {

  const form = useForm<any>()
  const { custId = 32098982 } = useRouter().params
  const scrollStyle = getScrollStyle({ bottom: 120, top: 20, hasNav: false });
  const [holidayInfo, setHolidayInfo] = useState<any>({});
  const [holId, setHolidayId] = useState();
  const [holDetail, setHolDetail] = useState<any>();

  const curHol = holidayInfo?.holidays?.find(item => item.holId === holId);

  useEffect(() => {
    (async () => {
      const res = await ncmp.acHolApply.getHolidayList.request({ custId: Number(custId) }, { isToken: true })
      // console.log(res.resultObj);
      setHolidayInfo(res.resultObj)
    })()
  }, []);

  const computeTime = async (name, val) => {
    // await ncmp.acHolApply.getHolHours.request({ startTime: '2022-11-11 11:11:11', endTime: '2022-11-12 11:12:00', holId, custId: Number(custId) }, { isToken: true })
    form.setValue(name, val);
    const { sd, st, ed, et, ...rest } = await form.getValues();
    if (sd && st && ed && et && holId) {
      const res = await ncmp.acHolApply.getHolHours.request({ startTime: `${sd} ${st}:00`, endTime: `${ed} ${et}:00`, holId, custId: Number(custId), ...rest });
      if (Number(res.code) === 200) {
        form.setValue('viewHours', res.resultObj.viewHours)
        setHolDetail(res.resultObj)
      } else {
        form.resetField(name)
        Taro.showToast({ title: res.errorMsg || '系统异常, 请稍后重试', icon: 'none' });
      }
    }
  }

  const columns: FormItemProps[] = [
    {
      title: '假期类型',
      name: 'holId',
      type: 'select',
      rules: { required: true },
      showLine: !curHol,
      rangeKeys: ['holId', 'holName'],
      options: holidayInfo?.holidays,
      inputProps: {
        // onConfirm: (v) => {
        //   console.log(22, v);
        // }
      },
      onChange: (val) => {
        // console.log(11, val);
        setHolidayId(val)
        form.reset();
        form.setValue('holId', val)
      }
    },
    {
      title: '',
      name: '',
      isHidden: !curHol,
      showLine: false,
      render: () => curHol && <View className={styles.tips}><Text>{curHol?.holName} 最小单位 {holidayUnit[curHol?.holUnit]}</Text>{curHol?.holName === '年假' && <Text>年假 剩余{holidayInfo?.annualLeaveLeft}天</Text>}</View>
    },
    {
      title: '结婚登记日',
      name: 'marriageRegistrationDay',
      type: 'date',
      rules: { required: true },
      isHidden: curHol?.holType !== 4,
      showLine: true
    },
    {
      title: '预产期',
      name: 'childPreborn',
      type: 'date',
      rules: { required: true },
      isHidden: curHol?.holType !== 5,
      showLine: true
    },
    {
      title: '开始日期',
      name: 'sd',
      type: 'date',
      rules: { required: true },
      showLine: true,
      onChange: e => computeTime('sd', e.detail.value),
    },
    {
      title: '开始时间',
      name: 'st',
      type: 'time',
      rules: { required: true },
      onChange: e => computeTime('st', e.detail.value),
      showLine: true
    },
    {
      title: '结束日期',
      name: 'ed',
      type: 'date',
      rules: { required: true },
      onChange: e => computeTime('ed', e.detail.value),
      showLine: true
    },
    {
      title: '结束时间',
      name: 'et',
      type: 'time',
      rules: { required: true },
      onChange: e => computeTime('et', e.detail.value),
      showLine: true
    },
    // {
    //   title: '结束时间',
    //   name: 'test',
    //   type: 'multiSelector',
    //   // rules: { validate: val => {}, required: true },
    //   showLine: false,
    //   options: dataTimeArr,
    //   defaultValue: ['2024', 3, 3, 3, 3, 3],
    //   onChange: (val) => {
    //     console.log(11, val);
    //     form.setValue('test', [3, 3, 3, 3, 3, 3])
    //   },
    // },
    {
      title: '请假时长',
      name: 'viewHours',
      type: 'text',
      rules: { required: true },
      showLine: false,
      inputProps: { disabled: true }
    },
    {
      title: '',
      name: '',
      showLine: false,
      render: () => <View className={styles.line}></View>
    },
    {
      title: '请假说明',
      name: 'remark',
      type: 'textarea',
      inputProps: { maxlength: 200 },
      showLine: false
    },
  ]

  const onSubmit = data => {
    // console.log(data);
    delete data.sd;
    delete data.st;
    delete data.ed;
    delete data.et;
    ncmp.acHolApply.save.request({ ...data, ...holDetail, holName: curHol?.holName }).then(res => {
      if (Number(res.code) === 200) {
        Taro.showToast({ title: '申请已提交', icon: 'none' });
        setTimeout(() => {
          Taro.navigateTo({ url: "/attendance/leave?type=1" })
        }, 2000);
      } else {
        Taro.showToast({ title: res.errorMsg || '系统异常, 请稍后重试', icon: 'none', })
      }
    })
  }

  return (
    <View className={styles.leave}>
      <Form form={form} columns={columns} style={scrollStyle} />
      <BottomBtn
        btns={[
          {
            title: '提交',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </View>
  )
}

export default withPage(Index)
