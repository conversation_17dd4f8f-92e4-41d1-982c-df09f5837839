/**
 * @description saveContinuingEdu
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.users.ExecuteResult;
export const path = '/user-server/api/saveContinuingEdu';
export const method = 'POST';
export const request = (
  data: defs.users.ContinuingEduInfo,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.users.ContinuingEduInfo,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
