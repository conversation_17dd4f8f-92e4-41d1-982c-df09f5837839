import { useState } from 'react'
import styles from './index.module.scss'
import Taro from '@tarojs/taro'

type TabbarItem = {
  id: number
  title: string
  imgSrc?: string
  imgSrcActive?: string
  path: string
}

interface ITabBarProps {
  currentIndex?: number
  list: Array<TabbarItem>
}

const Index: React.FC<ITabBarProps> = ({ currentIndex = 0, list }) => {
  const [currentTabIndex, setCurrentTabIndex] = useState<number>(currentIndex)

  const onGoto = (path: string, index: number) => {
    setCurrentTabIndex(index)
    path && Taro.redirectTo({ url: path })
  }

  return (
    <div className={styles.tabbar}>
      {list?.map((m, i) => {
        return (
          <div key={m.id} className={styles.tabbar_item} onClick={() => onGoto(m.path, i)}>
            <div className={styles.tabbar_item_img}>
              <img src={currentTabIndex === i ? m.imgSrcActive || m.imgSrc : m.imgSrc} />
            </div>
            <div className={`${styles.tabbar_item_title} ${currentTabIndex === i ? styles.active : ''}`}>{m.title}</div>
          </div>
        )
      })}
    </div>
  )
}

export default Index
