import { Text, View } from '@tarojs/components'
import VirtualList from '@tarojs/components/virtual-list'
import { getNumberSize } from '@utils/transforms'
import { FunctionComponent, memo, useCallback, useEffect, useState } from 'react'
import { getGlobalData } from '@utils'
import { ListViewProps, RefreshStateType } from './type'
import styles from './index.module.scss'

const ListView: FunctionComponent<ListViewProps> = props => {
  const {
    onScrollToUpper,
    onScrollToLower,
    itemData,
    renderItem,
    refresherEnabled,
    refreshState,
    height,
    itemSize,
    width,
    onRefresh,
    onLoadMore,
    noPull,
    noRefresh,
    ...rest
  } = props
  const { style } = rest
  const [_refreshState, setRefreshState] = useState<RefreshStateType>('Default')
  useEffect(() => {
    refreshState && setRefreshState(refreshState)
  }, [refreshState])
  const renderBottom = useCallback(() => {
    if (_refreshState === 'LoadMore') {
      return (
        <View className={styles.footer}>
          <Text className={styles.footer_text}>加载中...</Text>
        </View>
      )
    }
    if (_refreshState === 'NoMoreData') {
      return (
        <View className={styles.footer}>
          <Text className={styles.footer_text}>- 没有更多数据 -</Text>
        </View>
      )
    }
  }, [_refreshState])
  const _itemData = (getGlobalData<'systemInfo'>('systemInfo').windowWidth * itemSize) / 750
  return (
    <VirtualList
      height={height || getNumberSize((style as any)?.height)}
      itemSize={_itemData}
      width={width}
      itemData={itemData}
      itemCount={itemData.length}
      refresherEnabled={!noRefresh}
      refresherTriggered={_refreshState === 'Refresh'}
      scrollWithAnimation
      renderBottom={renderBottom()}
      onScrollToLower={async () => {
        if (noPull) return
        if (_refreshState === 'Default') {
          setRefreshState('LoadMore')
          const canLoadMore = await onLoadMore?.()
          setRefreshState(canLoadMore ? 'Default' : 'NoMoreData')
        }
      }}
      onRefresherRefresh={async () => {
        setRefreshState('Refresh')
        await onRefresh?.()
        setRefreshState('Default')
      }}
      {...(rest as any)}
    >
      {memo(({ index, data }: any) => {
        const item = data[index]
        return renderItem(item, index)
      })}
    </VirtualList>
  )
}

export default ListView
