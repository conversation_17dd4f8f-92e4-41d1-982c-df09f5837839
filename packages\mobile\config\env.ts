// 在开发环境中，IS_H5可能还未定义，所以需要安全检查
const isH5 = typeof IS_H5 !== 'undefined' ? IS_H5 : false;

const envs = {
  local: {
    BaseUrl: isH5 ? '' : 'https://wehr.ctgapp.com', // H5开发环境使用代理，其他环境直接请求
    AppId: 'wx51be6b4ca1db413c',
    WeComAppId: 'ww4ee4e21aedabec6a',
  },
  dev: {
    BaseUrl: 'https://wehr.ctgapp.com',
    AppId: 'wx51be6b4ca1db413c',
    WeComAppId: 'ww4ee4e21aedabec6a',
  },
  test: {
    BaseUrl: 'https://wxtest.ctgapp.com',
    AppId: 'wxe78e417fd82492ca',
    WeComAppId: 'ww3846e0a56359d626',
  },
  veri: {
    BaseUrl: 'https://wxveri.ctgapp.com',
    AppId: 'wx10cde48dc53bd85f',
    WeComAppId: 'ww3846e0a56359d626',
  },
  prod: {
    BaseUrl: 'https://wx.ctgapp.com',
    AppId: 'wx6a56da3e9fc40e7c',
    WeComAppId: 'ww5658807344c7525e',
  }
}

const { BaseUrl, AppId,WeComAppId } = envs[APP_ENV]
export { BaseUrl, AppId , WeComAppId }
