/**
 * @description Entry Controller
 */
import * as appdesignatedHospitals from './appdesignatedHospitals';
import * as appinsertEntryInfo from './appinsertEntryInfo';
import * as delEntryFile from './delEntryFile';
import * as delEntryFileWithHro from './delEntryFileWithHro';
import * as designatedHospitals from './designatedHospitals';
import * as echoFileSubmit from './echoFileSubmit';
import * as getOCRImageInfo from './getOCRImageInfo';
import * as getuuid from './getuuid';
import * as idCardCompose from './idCardCompose';
import * as insertEntryInfo from './insertEntryInfo';
import * as uploadEntryFile from './uploadEntryFile';
import * as uploadEntryFileByForm from './uploadEntryFileByForm';

export {
  appdesignatedHospitals,
  appinsertEntryInfo,
  delEntryFile,
  delEntryFileWithHro,
  designatedHospitals,
  echoFileSubmit,
  getOCRImageInfo,
  getuuid,
  idCardCompose,
  insertEntryInfo,
  uploadEntryFile,
  uploadEntryFileByForm,
};
