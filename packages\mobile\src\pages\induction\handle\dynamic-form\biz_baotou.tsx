import { FormItemProps } from '@components'
import { UseFormReturn } from 'react-hook-form'

const handleBaoTouColumn = (column: FormItemProps, form: UseFormReturn, page: string) => {
    if (column.name === 'bankNo_baotou'){
        return { ...column, titleRemind: "必须为本人银行卡信息"}
    }
    if (column.name === 'accountBank_baotou'){
        return { ...column, remind: "格式为xx银行"}
    }
    if (column.name === 'bankName_baotou'){
        return { ...column, remind: '格式为xx银行xxxxxx分行'}
    }
    return column
}

export { handleBaoTouColumn }